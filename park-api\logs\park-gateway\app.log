2025-08-01 02:40:05.629  WARN 26864 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 02:40:05.629  WARN 26864 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 02:40:05.646  WARN 26864 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 02:40:05.648  WARN 26864 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 02:40:05.691  INFO 26864 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 02:40:05.712  INFO 26864 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-08-01 09:18:08.050  INFO 6160 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : The following 1 profile is active: "dev"
2025-08-01 09:18:08.931  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 09:18:08.934  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 09:18:08.958  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-01 09:18:09.146  INFO 6160 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a28cdf3d-2497-3ef5-9b94-3ee1e1ff5151
2025-08-01 09:18:09.354  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:09.355  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:09.356  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:15.652  INFO 6160 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-01 09:18:16.063  INFO 6160 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 09:18:16.127  INFO 6160 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-01 09:18:16.375  WARN 6160 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-01 09:18:16.540  INFO 6160 --- [main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-08-01 09:18:18.821  INFO 6160 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 09:18:18.821  INFO 6160 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 09:18:19.012  INFO 6160 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-gateway 192.168.3.140:8080 register finished
2025-08-01 09:18:19.140  INFO 6160 --- [main] a.c.n.d.NacosDiscoveryHeartBeatPublisher : Start nacos heartBeat task scheduler.
2025-08-01 09:18:19.165  INFO 6160 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : Started LgjyGatewayApplication in 17.082 seconds (JVM running for 18.882)
2025-08-01 09:18:19.172  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway, group=DEFAULT_GROUP
2025-08-01 09:18:19.174  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway-dev.yml, group=DEFAULT_GROUP
2025-08-01 09:18:19.174  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway.yml, group=DEFAULT_GROUP
2025-08-01 09:18:19.176  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-01 10:18:28.005  WARN 6160 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 10:18:28.005  WARN 6160 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 10:18:28.016  WARN 6160 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 10:18:28.023  WARN 6160 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 10:18:28.073  INFO 6160 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 10:18:28.091  INFO 6160 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
