2025-08-01 02:40:13.901  WARN 28560 --- [Thread-13] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 02:40:13.900  WARN 28560 --- [Thread-8] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 02:40:13.909  WARN 28560 --- [Thread-13] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 02:40:13.910  WARN 28560 --- [Thread-8] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 02:40:15.467  INFO 28560 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 02:40:15.470  INFO 28560 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-08-01 02:40:15.645  INFO 28560 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 02:40:15.660  INFO 28560 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-08-01 09:19:05.158  INFO 28316 --- [main] com.lgjy.system.LgjySystemApplication    : The following 1 profile is active: "dev"
2025-08-01 09:19:06.013  INFO 28316 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 09:19:06.016  INFO 28316 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 09:19:06.046  INFO 28316 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-01 09:19:06.261  INFO 28316 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=20a971a3-cb48-330f-ad21-a538a4b7d629
2025-08-01 09:19:06.773  INFO 28316 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 9201 (http)
2025-08-01 09:19:06.783  INFO 28316 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 09:19:06.783  INFO 28316 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.105]
2025-08-01 09:19:06.800  WARN 28316 --- [main] o.a.c.webresources.DirResourceSet        : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.9201.8604947436949037170] which is part of the web application []
2025-08-01 09:19:06.922  INFO 28316 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 09:19:06.922  INFO 28316 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1742 ms
2025-08-01 09:19:06.989  INFO 28316 --- [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-08-01 09:19:07.577  INFO 28316 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 09:19:08.738  INFO 28316 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-gate' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 09:19:08.839  INFO 28316 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 09:19:08.911  INFO 28316 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 09:19:10.493  INFO 28316 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-file' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 09:19:10.819  INFO 28316 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-01 09:19:13.709  INFO 28316 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-system' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 09:19:15.264  WARN 28316 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-01 09:19:15.273  INFO 28316 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 09:19:15.353  INFO 28316 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 9201 (http) with context path ''
2025-08-01 09:19:15.363  INFO 28316 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 09:19:15.363  INFO 28316 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 09:19:15.554  INFO 28316 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-system *************:9201 register finished
2025-08-01 09:19:16.669  INFO 28316 --- [main] com.lgjy.system.LgjySystemApplication    : Started LgjySystemApplication in 17.784 seconds (JVM running for 19.338)
2025-08-01 09:19:16.683  INFO 28316 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system.yml, group=DEFAULT_GROUP
2025-08-01 09:19:16.684  INFO 28316 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system, group=DEFAULT_GROUP
2025-08-01 09:19:16.686  INFO 28316 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-01 09:19:16.687  INFO 28316 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system-dev.yml, group=DEFAULT_GROUP
2025-08-01 09:19:17.729  INFO 28316 --- [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 09:19:17.731  INFO 28316 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 09:19:17.734  INFO 28316 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-01 10:10:16.564  INFO 28316 --- [http-nio-9201-exec-10] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [Hm_lvt_4dbdbc5421c41984499f878628d60f2f=1752679026,1752748916,1752802660,1752835650;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-08-01 10:18:57.393  WARN 28316 --- [Thread-8] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 10:18:57.393  WARN 28316 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 10:18:57.394  WARN 28316 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 10:18:57.397  WARN 28316 --- [Thread-8] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 10:18:59.540  INFO 28316 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 10:18:59.551  INFO 28316 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-08-01 10:18:59.769  INFO 28316 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 10:18:59.788  INFO 28316 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
