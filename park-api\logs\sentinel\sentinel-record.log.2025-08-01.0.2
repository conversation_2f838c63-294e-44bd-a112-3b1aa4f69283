2025-08-01 02:25:02.589 INFO Add child </logout> to node <sentinel_spring_web_context>
2025-08-01 02:25:03.802 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-auth-metrics.log.2025-07-28
2025-08-01 02:25:03.804 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-auth-metrics.log.2025-07-28.idx
2025-08-01 02:25:03.805 INFO [MetricWriter] New metric file created: logs/sentinel\park-auth-metrics.log.2025-08-01
2025-08-01 02:25:03.805 INFO [MetricWriter] New metric index file created: logs/sentinel\park-auth-metrics.log.2025-08-01.idx
2025-08-01 09:19:17.754 INFO App name resolved from property csp.sentinel.app.name: park-system
2025-08-01 09:19:17.755 INFO [SentinelConfig] Application type resolved: 0
2025-08-01 09:21:22.728 INFO Add child <sentinel_default_context> to node <machine-root>
2025-08-01 09:21:22.729 INFO Add child <sentinel_spring_web_context> to node <machine-root>
2025-08-01 09:21:22.736 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.737 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.738 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, aliasName=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.739 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, aliasName=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.741 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.742 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.743 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc
2025-08-01 09:21:22.744 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc
2025-08-01 09:21:22.744 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit
2025-08-01 09:21:22.744 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit
2025-08-01 09:21:22.744 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc
2025-08-01 09:21:22.744 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc
2025-08-01 09:21:22.747 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.CommandCenter, provider=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, aliasName=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.748 INFO [CommandCenterProvider] CommandCenter resolved: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 09:21:22.752 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.753 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.753 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.754 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.754 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.755 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.755 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.756 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.756 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.756 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.757 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.757 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.758 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.758 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.759 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.759 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.760 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.761 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.761 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.762 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.763 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.763 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.764 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.764 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.764 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.765 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.765 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.766 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.766 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.768 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.768 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.771 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.command.CommandHandlerInterceptor, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 09:21:22.772 INFO [CommandCenterInit] Starting command center: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 09:21:22.772 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc with order -1
2025-08-01 09:21:22.775 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.HeartbeatSender, provider=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, aliasName=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.778 WARNING [SimpleHttpHeartbeatSender] Dashboard server address not configured or not available
2025-08-01 09:21:22.779 INFO [HeartbeatSenderProvider] HeartbeatSender activated: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 09:21:22.779 INFO [HeartbeatSenderInit] Heartbeat interval not configured in config property or invalid, using sender default: 10000
2025-08-01 09:21:22.780 INFO [HeartbeatSenderInit] HeartbeatSender started: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 09:21:22.780 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc with order -1
2025-08-01 09:21:22.784 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc with order 0
2025-08-01 09:21:22.786 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit with order **********
2025-08-01 09:21:22.787 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit with order **********
2025-08-01 09:21:22.792 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.792 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:22.796 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.TokenService, provider=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, aliasName=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, isSingleton=true, isDefault=true, order=0
2025-08-01 09:21:22.797 INFO [TokenServiceProvider] Global token service resolved: com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService
2025-08-01 09:21:22.797 INFO [DefaultClusterServerInitFunc] Default entity codec and processors registered
2025-08-01 09:21:22.797 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc with order **********
2025-08-01 09:21:22.800 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.SlotChainBuilder, provider=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, aliasName=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, isSingleton=true, isDefault=true, order=0
2025-08-01 09:21:22.800 INFO [SlotChainProvider] Global slot chain builder resolved: com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder
2025-08-01 09:21:22.804 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, aliasName=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, isSingleton=false, isDefault=false, order=-10000
2025-08-01 09:21:22.805 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, aliasName=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, isSingleton=false, isDefault=false, order=-9000
2025-08-01 09:21:22.806 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.logger.LogSlot, aliasName=com.alibaba.csp.sentinel.slots.logger.LogSlot, isSingleton=true, isDefault=false, order=-8000
2025-08-01 09:21:22.806 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, aliasName=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, isSingleton=true, isDefault=false, order=-7000
2025-08-01 09:21:22.807 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, aliasName=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, isSingleton=true, isDefault=false, order=-6000
2025-08-01 09:21:22.807 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.system.SystemSlot, aliasName=com.alibaba.csp.sentinel.slots.system.SystemSlot, isSingleton=true, isDefault=false, order=-5000
2025-08-01 09:21:22.808 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, isSingleton=true, isDefault=false, order=-2000
2025-08-01 09:21:22.808 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, aliasName=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, isSingleton=true, isDefault=false, order=-1000
2025-08-01 09:21:22.809 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, isSingleton=true, isDefault=false, order=-3000
2025-08-01 09:21:22.814 INFO Add child </user/info/{username}> to node <sentinel_spring_web_context>
2025-08-01 09:21:22.816 INFO [AuthorityRuleManager] Authority rules loaded: {}
2025-08-01 09:21:22.820 INFO [SystemRuleManager] Current system check status: false, highestSystemLoad: 1.797693e+308, highestCpuUsage: 1.797693e+308, maxRt: 9223372036854775807, maxThread: 9223372036854775807, maxQps: 1.797693e+308
2025-08-01 09:21:22.823 INFO [ParamFlowRuleManager] No parameter flow rules, clearing all parameter metrics
2025-08-01 09:21:22.823 INFO [ParamFlowRuleManager] Parameter flow rules received: {}
2025-08-01 09:21:22.825 INFO [FlowRuleManager] Flow rules loaded: {}
2025-08-01 09:21:22.827 INFO [MetricWriter] Creating new MetricWriter, singleFileSize=52428800, totalFileCount=6
2025-08-01 09:21:22.829 INFO [DegradeRuleManager] Degrade rules loaded: {}
2025-08-01 09:21:22.831 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.metric.extension.MetricExtension, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 09:21:22.831 INFO [MetricExtensionProvider] No existing MetricExtension found
2025-08-01 09:21:23.251 INFO Add child </logininfor> to node <sentinel_spring_web_context>
2025-08-01 09:21:23.318 INFO Add child </user/recordlogin> to node <sentinel_spring_web_context>
2025-08-01 09:21:23.843 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-system-metrics.log.2025-07-31
2025-08-01 09:21:23.843 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-system-metrics.log.2025-07-31.idx
2025-08-01 09:21:23.844 INFO [MetricWriter] New metric file created: logs/sentinel\park-system-metrics.log.2025-08-01.1
2025-08-01 09:21:23.844 INFO [MetricWriter] New metric index file created: logs/sentinel\park-system-metrics.log.2025-08-01.1.idx
2025-08-01 09:21:24.335 INFO Add child </user/getInfo> to node <sentinel_spring_web_context>
2025-08-01 09:21:24.773 INFO Add child </menu/getRouters> to node <sentinel_spring_web_context>
2025-08-01 09:21:25.252 INFO Add child </dashboard/warehouses> to node <sentinel_spring_web_context>
2025-08-01 09:21:25.299 INFO Add child </dashboard/system-statistics> to node <sentinel_spring_web_context>
2025-08-01 09:21:25.392 INFO Add child </dashboard/revenue-trend> to node <sentinel_spring_web_context>
2025-08-01 09:21:25.394 INFO Add child </dashboard/user-trend> to node <sentinel_spring_web_context>
2025-08-01 09:21:36.000 INFO Add child </agreement/list> to node <sentinel_spring_web_context>
2025-08-01 09:21:36.000 INFO Add child </dict/data/type/{dictType}> to node <sentinel_spring_web_context>
2025-08-01 09:21:37.355 INFO Add child </agreement/{id}> to node <sentinel_spring_web_context>
2025-08-01 10:02:51.040 INFO Add child </parkingOrder/list> to node <sentinel_spring_web_context>
2025-08-01 10:02:51.042 INFO Add child </platform/warehouse/optionSelect> to node <sentinel_spring_web_context>
2025-08-01 10:02:51.048 INFO Add child </parkingOrder/carTypeOptions> to node <sentinel_spring_web_context>
2025-08-01 10:02:58.261 INFO Add child </parkingOrder/{id}> to node <sentinel_spring_web_context>
2025-08-01 10:03:18.134 INFO Add child </dict/type/list> to node <sentinel_spring_web_context>
2025-08-01 10:03:25.716 INFO Add child </dict/type/{dictId}> to node <sentinel_spring_web_context>
2025-08-01 10:03:25.716 INFO Add child </dict/type/optionselect> to node <sentinel_spring_web_context>
2025-08-01 10:03:25.740 INFO Add child </dict/data/list> to node <sentinel_spring_web_context>
2025-08-01 10:07:23.008 INFO Add child </vip/member/warehouseOptions> to node <sentinel_spring_web_context>
2025-08-01 10:07:23.009 INFO Add child </errorDataLog/list> to node <sentinel_spring_web_context>
2025-08-01 10:07:25.964 INFO Add child </gateParkingInfo/list> to node <sentinel_spring_web_context>
2025-08-01 10:07:25.992 INFO Add child </gateParkingInfo/carTypeOptions> to node <sentinel_spring_web_context>
