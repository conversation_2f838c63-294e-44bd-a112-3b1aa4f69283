<template>
  <div class="starfield-login">
    <!-- 星空背景 -->
    <div class="starfield-background">
      <!-- 星星粒子 -->
      <div class="stars-container">
        <div v-for="n in 200" :key="n" class="star" :style="getStarStyle()"></div>
      </div>

      <!-- 流星效果 -->
      <div class="meteors-container">
        <div v-for="n in 5" :key="n" class="meteor" :style="getMeteorStyle(n)"></div>
      </div>

      <!-- 极光效果 -->
      <div class="aurora-container">
        <div class="aurora aurora-1"></div>
        <div class="aurora aurora-2"></div>
        <div class="aurora aurora-3"></div>
      </div>

      <!-- 复兴号高铁（双车头） -->
      <div class="space-train-container">
        <div class="space-train">
          <!-- 前车头 -->
          <div class="train-engine crh-engine front-engine">
            <div class="engine-nose-trapezoid"></div>
            <div class="engine-body"></div>
            <div class="engine-window"></div>
            <div class="engine-light"></div>
          </div>
          <!-- 车厢1 -->
          <div class="train-car train-car-1">
            <div class="car-body"></div>
            <div class="car-windows">
              <div class="window"></div>
              <div class="window"></div>
              <div class="window"></div>
            </div>
          </div>
          <!-- 车厢2 -->
          <div class="train-car train-car-2">
            <div class="car-body"></div>
            <div class="car-windows">
              <div class="window"></div>
              <div class="window"></div>
              <div class="window"></div>
            </div>
          </div>
          <!-- 车厢3 -->
          <div class="train-car train-car-3">
            <div class="car-body"></div>
            <div class="car-windows">
              <div class="window"></div>
              <div class="window"></div>
              <div class="window"></div>
            </div>
          </div>
          <!-- 后车头 -->
          <div class="train-engine crh-engine rear-engine">
            <div class="engine-body"></div>
            <div class="engine-nose-trapezoid rear"></div>
            <div class="engine-window"></div>
            <div class="engine-light"></div>
          </div>
          <div class="train-trail"></div>
        </div>
      </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-container">
      <div class="login-form-wrapper">
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="starfield-form">
          <div class="form-header">
            <h3 class="form-title">{{ title }}</h3>
          </div>

          <el-form-item prop="username" class="form-item">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.username"
                type="text"
                size="large"
                auto-complete="off"
                placeholder="请输入账号"
                class="starfield-input"
              >
                <template #prefix>
                  <svg-icon icon-class="user" class="input-icon" />
                </template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item prop="password" class="form-item">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.password"
                :type="passwordVisible ? 'text' : 'password'"
                size="large"
                auto-complete="off"
                placeholder="请输入密码"
                @keyup.enter="handleLogin"
                class="starfield-input"
              >
                <template #prefix>
                  <svg-icon icon-class="password" class="input-icon" />
                </template>
                <template #suffix>
                  <el-icon
                    class="password-toggle-icon"
                    @click="togglePasswordVisibility"
                    :style="{ cursor: 'pointer', color: passwordVisible ? '#409eff' : '#c0c4cc' }"
                  >
                    <component :is="passwordVisible ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled" class="form-item">
            <div class="captcha-wrapper">
              <div class="input-wrapper captcha-input">
                <el-input
                  v-model="loginForm.code"
                  size="large"
                  auto-complete="off"
                  placeholder="验证码"
                  @keyup.enter="handleLogin"
                  class="starfield-input"
                >
                  <template #prefix>
                    <svg-icon icon-class="validCode" class="input-icon" />
                  </template>
                </el-input>
              </div>
              <div class="captcha-image" @click="getCode">
                <img :src="codeUrl" class="captcha-img"/>
              </div>
            </div>
          </el-form-item>

          <el-form-item class="form-item login-btn-item">
            <button
              :disabled="loading"
              class="starfield-login-btn"
              @click.prevent="handleLogin"
            >
              <span class="btn-content">
                <span v-if="!loading" class="btn-text">启动登录</span>
                <span v-else class="btn-text">连接中...</span>
                <div class="btn-glow"></div>
              </span>
            </button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login"
import useUserStore from '@/store/modules/user'
import { View, Hide } from '@element-plus/icons-vue'

const title = import.meta.env.VITE_APP_TITLE
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: "",
  password: "",
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 密码可见性控制
const passwordVisible = ref(false)

const redirect = ref(undefined)

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

// 切换密码可见性
const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value
}

// 生成随机星星样式
function getStarStyle() {
  const size = Math.random() * 3 + 1
  const animationDuration = Math.random() * 3 + 2
  const animationDelay = Math.random() * 5

  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    width: size + 'px',
    height: size + 'px',
    animationDuration: animationDuration + 's',
    animationDelay: animationDelay + 's'
  }
}

// 生成流星样式
function getMeteorStyle(index) {
  const animationDelay = index * 8 + Math.random() * 5
  const startPosition = Math.random() * 100

  return {
    left: startPosition + '%',
    animationDelay: animationDelay + 's',
    animationDuration: '3s'
  }
}

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        router.push({ path: redirect.value || "/", query: otherQueryParams })
      }).catch(() => {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

getCode()
</script>

<style lang='scss' scoped>
/* 星空登录页面主容器 */
.starfield-login {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: radial-gradient(ellipse at bottom, #0a0a0a 0%, #000000 100%);
}

/* 星空背景容器 */
.starfield-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.starfield-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
  from { transform: translateX(0); }
  to { transform: translateX(-200px); }
}

/* 星星容器 */
.stars-container {
  position: absolute;
  width: 100%;
  height: 100%;

  .star {
    position: absolute;
    background: #ffffff;
    border-radius: 50%;
    animation: twinkle infinite ease-in-out alternate;
    box-shadow: 0 0 6px #ffffff;
  }
}

/* 星星闪烁动画 */
@keyframes twinkle {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 流星容器 */
.meteors-container {
  position: absolute;
  width: 100%;
  height: 100%;

  .meteor {
    position: absolute;
    width: 300px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #fff, transparent);
    border-radius: 50px;
    animation: meteor 3s linear infinite;
    opacity: 0;

    &:nth-child(1) {
      top: 10%;
      left: -300px;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      top: 30%;
      left: -300px;
      animation-delay: 2s;
    }

    &:nth-child(3) {
      top: 60%;
      left: -300px;
      animation-delay: 4s;
    }

    &:nth-child(4) {
      top: 80%;
      left: -300px;
      animation-delay: 6s;
    }

    &:nth-child(5) {
      top: 45%;
      left: -300px;
      animation-delay: 8s;
    }
  }
}

/* 流星动画 */
@keyframes meteor {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 300px)) translateY(300px);
    opacity: 0;
  }
}

/* 极光效果 */
.aurora-container {
  position: absolute;
  width: 100%;
  height: 100%;

  .aurora {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.3;
    animation: aurora 20s infinite ease-in-out;

    &.aurora-1 {
      background: radial-gradient(ellipse at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%);
      animation-delay: 0s;
    }

    &.aurora-2 {
      background: radial-gradient(ellipse at 80% 30%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
      animation-delay: 7s;
    }

    &.aurora-3 {
      background: radial-gradient(ellipse at 50% 80%, rgba(119, 198, 255, 0.3) 0%, transparent 50%);
      animation-delay: 14s;
    }
  }
}

/* 极光动画 */
@keyframes aurora {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1) rotate(5deg);
  }
}

/* 太空列车容器 */
.space-train-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
}

/* 太空列车 */
.space-train {
  position: absolute;
  bottom: 15%;
  left: -400px;
  display: flex;
  align-items: flex-end;
  animation: trainMove 25s linear infinite;
  filter: drop-shadow(0 0 5px rgba(100, 181, 246, 0.15));
}

/* 列车移动动画 */
@keyframes trainMove {
  0% {
    left: -400px;
  }
  100% {
    left: calc(100vw + 100px);
  }
}

/* 复兴号车头（双车头设计） */
.train-engine {
  position: relative;
  width: 100px;
  height: 45px;
  margin-right: 5px;

  &.crh-engine {
    /* 梯形车鼻（上短下长） */
    .engine-nose-trapezoid {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 45px;
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 30%, #bbdefb 70%, #90caf9 100%);
      clip-path: polygon(0% 30%, 100% 0%, 100% 100%, 0% 70%);
      z-index: 2;
      filter: drop-shadow(0 0 6px rgba(144, 202, 249, 0.3));
      border-radius: 0 8px 8px 0;
    }

    /* 后车头的梯形车鼻 */
    &.rear-engine .engine-nose-trapezoid.rear {
      left: auto;
      right: 0;
      clip-path: polygon(0% 0%, 100% 30%, 100% 70%, 0% 100%);
      border-radius: 8px 0 0 8px;
    }

    &.front-engine {
      margin-right: 5px;
    }

    &.rear-engine {
      margin-left: 5px;
      margin-right: 0;
    }
  }

  .engine-body {
    width: 75px;
    height: 100%;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 30%, #90caf9 70%, #64b5f6 100%);
    border-radius: 8px;
    position: relative;
    margin-left: 25px;
    box-shadow:
      0 0 10px rgba(100, 181, 246, 0.25),
      inset 0 3px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(0, 0, 0, 0.1);
  }

  &.rear-engine .engine-body {
    margin-left: 0;
    margin-right: 25px;
  }

  .engine-window {
    position: absolute;
    top: 10px;
    left: 40px;
    width: 25px;
    height: 18px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  &.rear-engine .engine-window {
    left: 10px;
  }

  .engine-light {
    position: absolute;
    top: 18px;
    right: 8px;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, #fff 0%, #ffeb3b 100%);
    border-radius: 50%;
    box-shadow: 0 0 8px #ffeb3b, 0 0 15px rgba(255, 235, 59, 0.3);
    animation: engineLight 2s ease-in-out infinite alternate;
  }

  &.rear-engine .engine-light {
    right: auto;
    left: 8px;
  }
}

/* 引擎灯光动画 */
@keyframes engineLight {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 复兴号车厢 */
.train-car {
  position: relative;
  width: 70px;
  height: 40px;
  margin-right: 3px;

  .car-body {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #e8f4fd 0%, #bbdefb 30%, #90caf9 70%, #64b5f6 100%);
    border-radius: 6px;
    box-shadow:
      0 0 8px rgba(100, 181, 246, 0.2),
      inset 0 3px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .car-windows {
    position: absolute;
    top: 10px;
    left: 8px;
    right: 8px;
    display: flex;
    gap: 6px;

    .window {
      flex: 1;
      height: 14px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 3px;
      box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }

  /* 车厢连接处 */
  &::after {
    content: '';
    position: absolute;
    right: -2px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #666 0%, #333 100%);
    border-radius: 2px;
  }
}

/* 列车尾迹 */
.train-trail {
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(100, 181, 246, 0.6), transparent);
  border-radius: 2px;
  animation: trailPulse 1s ease-in-out infinite alternate;
}

/* 尾迹脉冲动画 */
@keyframes trailPulse {
  0% {
    opacity: 0.4;
    width: 80px;
  }
  100% {
    opacity: 0.8;
    width: 120px;
  }
}

/* 登录容器 */
.login-container {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 20px;
}

/* 登录表单包装器 */
.login-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 28px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: formAppear 1s ease-out;
}

/* 表单出现动画 */
@keyframes formAppear {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #007AFF, #5856D6);
    border-radius: 2px;
  }

  .form-title {
    color: #1D1D1F;
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 700;
    margin: 0 0 16px 0;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #1D1D1F 0%, #333 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .form-subtitle {
    color: rgba(29, 29, 31, 0.7);
    font-size: 16px;
    margin: 0;
    font-weight: 400;
    line-height: 1.6;
  }
}

/* 表单项 */
.form-item {
  margin-bottom: 25px;

  :deep(.el-form-item__error) {
    color: #ff6b6b;
    font-size: 12px;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
  }
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 12px;
    z-index: -1;
    transition: all 0.3s ease;
  }

  &:hover::before {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  }
}

/* 星空输入框样式 */
.starfield-input {
  :deep(.el-input__wrapper) {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    height: 50px;

    &:hover {
      border-color: rgba(0, 0, 0, 0.2);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    &.is-focus {
      border-color: #007AFF;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }

  :deep(.el-input__inner) {
    color: #1D1D1F;
    background: transparent;
    border: none;
    font-size: 16px;
    height: 48px;
    line-height: 48px;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    &::placeholder {
      color: #86868B;
    }
  }

  :deep(.el-input__prefix) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 输入框图标 */
.input-icon {
  color: #86868B;
  font-size: 18px;
  transition: color 0.2s ease;
}

.starfield-input:hover .input-icon,
.starfield-input.is-focus .input-icon {
  color: #007AFF;
}

/* 验证码包装器 */
.captcha-wrapper {
  display: flex;
  gap: 15px;
  align-items: flex-start;

  .captcha-input {
    flex: 1;
  }

  .captcha-image {
    width: 120px;
    height: 50px;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
    transition: all 0.2s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    &:hover {
      border-color: rgba(0, 0, 0, 0.2);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    .captcha-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

/* 登录按钮项 */
.login-btn-item {
  margin-top: 30px;
  margin-bottom: 0;
}

/* 星空登录按钮 */
.starfield-login-btn {
  position: relative;
  width: 100%;
  height: 55px;
  background: #007AFF;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  &:hover:not(:disabled) {
    background: #0056CC;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    background: #004499;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #86868B;
  }

  .btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .btn-text {
    letter-spacing: 0.5px;
  }

  .btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover:not(:disabled) .btn-glow {
    left: 100%;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 1;
  }

  &:hover:not(:disabled)::before {
    opacity: 1;
  }
}

/* 密码切换图标样式 */
.password-toggle-icon {
  font-size: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.95);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form-wrapper {
    margin: 20px;
    padding: 30px 25px;
    max-width: none;
  }

  .form-header .form-title {
    font-size: 24px;
  }

  .captcha-wrapper {
    flex-direction: column;
    gap: 15px;

    .captcha-image {
      width: 100%;
      height: 50px;
    }
  }
}

@media (max-width: 480px) {
  .login-form-wrapper {
    margin: 15px;
    padding: 25px 20px;
  }

  .form-header .form-title {
    font-size: 22px;
  }

  .starfield-login-btn {
    height: 50px;
    font-size: 16px;
  }
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.starfield-login-btn:disabled .btn-text {
  animation: pulse 1.5s infinite ease-in-out;
}
</style>
