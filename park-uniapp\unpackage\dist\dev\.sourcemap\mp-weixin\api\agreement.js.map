{"version": 3, "file": "agreement.js", "sources": ["api/agreement.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n/**\r\n * 根据协议类型获取协议内容\r\n * @param {number} agreementType 协议类型 1-用户服务协议 2-隐私协议 3-发票抬头协议\r\n * @returns {Promise}\r\n */\r\nexport function getAgreementByType(agreementType) {\r\n  return request.get('/wx/agreement/getByType', {\r\n    agreementType\r\n  })\r\n}\r\n"], "names": ["request"], "mappings": ";;AAOO,SAAS,mBAAmB,eAAe;AAChD,SAAOA,cAAO,QAAC,IAAI,2BAA2B;AAAA,IAC5C;AAAA,EACJ,CAAG;AACH;;"}