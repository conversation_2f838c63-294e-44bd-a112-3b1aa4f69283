{"version": 3, "file": "car.js", "sources": ["api/car.js"], "sourcesContent": ["import  request  from '../utils/request'\r\n\r\n// 获取车辆列表\r\nexport const getCarList = () => request.get('/wx/car/list')\r\n\r\n// 添加车辆\r\nexport const addCar = (data) => request.post('/wx/car/insert', data)\r\n\r\n// 编辑车辆\r\nexport const editCar = (data) => request.put('/wx/car/update', data)\r\n\r\n// 删除车辆\r\nexport const deleteCar = (data) => request.delete('/wx/car/delete', data)\r\n\r\n// 获取车辆详情\r\nexport const getCarDetailById = (data) => request.get('/wx/car/detail', data)"], "names": ["request"], "mappings": ";;AAGY,MAAC,aAAa,MAAMA,cAAAA,QAAQ,IAAI,cAAc;AAG9C,MAAC,SAAS,CAAC,SAASA,cAAAA,QAAQ,KAAK,kBAAkB,IAAI;AAGvD,MAAC,UAAU,CAAC,SAASA,cAAAA,QAAQ,IAAI,kBAAkB,IAAI;AAGvD,MAAC,YAAY,CAAC,SAASA,cAAAA,QAAQ,OAAO,kBAAkB,IAAI;AAG5D,MAAC,mBAAmB,CAAC,SAASA,cAAO,QAAC,IAAI,kBAAkB,IAAI;;;;;;"}