{"version": 3, "file": "invoice.js", "sources": ["api/invoice.js"], "sourcesContent": ["import request from '../utils/request'\r\n\r\n// 获取发票抬头列表\r\nexport const getInvoiceTitleList = () => request.get('/wx/invoiceTitle/list')\r\n\r\n// 添加发票抬头\r\nexport const addInvoiceTitle = (data) => request.post('/wx/invoiceTitle/insert', data)\r\n\r\n// 编辑发票抬头\r\nexport const editInvoiceTitle = (data) => request.put('/wx/invoiceTitle/update', data)\r\n\r\n// 删除发票抬头\r\nexport const deleteInvoiceTitle = (data) => request.delete('/wx/invoiceTitle/delete', data)\r\n\r\n// 获取发票抬头详情\r\nexport const getInvoiceTitleDetail = (data) => request.get('/wx/invoiceTitle/detail', data)\r\n\r\n// 发送发票到邮箱\r\nexport const postInvoiceSend = (data) => request.post('/wx//invoice/record/send', data)\r\n\r\n// 保存开票记录\r\nexport const postSaveInvoiceRecord = (data) => request.post('/wx/invoice/record/insert', data)\r\n\r\n// 发票重开\r\nexport const postResumeInvoice = (data) => request.post('/wx/invoice/resume', data)\r\n\r\n// 获取开票记录列表\r\nexport const getInvoiceRecordList = () => request.post('/wx/invoice/record/list')\r\n"], "names": ["request"], "mappings": ";;AAGY,MAAC,sBAAsB,MAAMA,cAAAA,QAAQ,IAAI,uBAAuB;AAGhE,MAAC,kBAAkB,CAAC,SAASA,cAAAA,QAAQ,KAAK,2BAA2B,IAAI;AAGzE,MAAC,mBAAmB,CAAC,SAASA,cAAAA,QAAQ,IAAI,2BAA2B,IAAI;AAGzE,MAAC,qBAAqB,CAAC,SAASA,cAAAA,QAAQ,OAAO,2BAA2B,IAAI;AAM9E,MAAC,kBAAkB,CAAC,SAASA,cAAAA,QAAQ,KAAK,4BAA4B,IAAI;AAG1E,MAAC,wBAAwB,CAAC,SAASA,cAAAA,QAAQ,KAAK,6BAA6B,IAAI;AAGjF,MAAC,oBAAoB,CAAC,SAASA,cAAAA,QAAQ,KAAK,sBAAsB,IAAI;AAGtE,MAAC,uBAAuB,MAAMA,sBAAQ,KAAK,yBAAyB;;;;;;;;;"}