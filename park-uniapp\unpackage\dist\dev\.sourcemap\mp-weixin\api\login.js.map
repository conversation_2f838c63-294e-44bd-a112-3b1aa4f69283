{"version": 3, "file": "login.js", "sources": ["api/login.js"], "sourcesContent": ["import  request  from '../utils/request'\r\n\r\n// 获取验证码\r\nexport const getCode = loginForm => request.post('/wxauth/code', loginForm)\r\n\r\n// 手机号验证码登录\r\nexport const login = loginForm => request.post('/wxauth/login/code', loginForm)\r\n\r\n// 微信授权登录\r\nexport const loginWx = loginWxForm => request.post('/wxauth/login/wx', loginWxForm)\r\n\r\n// 退出登录\r\nexport const logout = () => request.delete('/wxauth/logout')\r\n\r\n// 获取openid\r\nexport const getOpenid = (data) => request.post('/wxauth/openid', data)\r\n\r\n\r\n\r\n\r\n\r\n"], "names": ["request"], "mappings": ";;AAGY,MAAC,UAAU,eAAaA,cAAAA,QAAQ,KAAK,gBAAgB,SAAS;AAG9D,MAAC,QAAQ,eAAaA,cAAAA,QAAQ,KAAK,sBAAsB,SAAS;AAGlE,MAAC,UAAU,iBAAeA,cAAAA,QAAQ,KAAK,oBAAoB,WAAW;AAMtE,MAAC,YAAY,CAAC,SAASA,cAAO,QAAC,KAAK,kBAAkB,IAAI;;;;;"}