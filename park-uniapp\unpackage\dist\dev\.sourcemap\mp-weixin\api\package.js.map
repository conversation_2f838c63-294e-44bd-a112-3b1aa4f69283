{"version": 3, "file": "package.js", "sources": ["api/package.js"], "sourcesContent": ["import request  from '../utils/request'\r\n\r\n// 获取选中场库的普通套餐列表\r\nexport const getPackageList = (data) => request.post('/wx/package/list', data)\r\n\r\n// 查询某用户在某场库的某车的未过期套餐\r\nexport const getUserPackagePlate = (data) => request.post('/wx/package/user/plate', data)\r\n\r\n// 查询用户的普通套餐购买记录\r\nexport const getUserPackageRecordList = (data) => request.post('/wx/package/user/record/list', data)\r\n\r\n// 创建订单\r\nexport const createOrder = (data) => request.post('/wx/package/order/create', data)\r\n\r\n// 更新订单\r\nexport const updateOrder = (data) => request.post('/wx/package/order/update', data)\r\n\r\n// 支付回调\r\nexport const payPackageCallBack = (data) => request.post('/wx/package/order/front/payCallback', data)\r\n\r\n// 套餐购买前，查看车辆是否在场\r\nexport const checkCarInWarehouse = (data) => request.post('/wx/package/packageJudge', data)\r\n\r\n// VIP用户查询所有车辆套餐\r\nexport const getVipUserPackageList = (vipType) => request.get(`/wx/package/vip/list/${vipType}`)"], "names": ["request"], "mappings": ";;AAGY,MAAC,iBAAiB,CAAC,SAASA,cAAAA,QAAQ,KAAK,oBAAoB,IAAI;AAGjE,MAAC,sBAAsB,CAAC,SAASA,cAAAA,QAAQ,KAAK,0BAA0B,IAAI;AAG5E,MAAC,2BAA2B,CAAC,SAASA,cAAAA,QAAQ,KAAK,gCAAgC,IAAI;AAGvF,MAAC,cAAc,CAAC,SAASA,cAAAA,QAAQ,KAAK,4BAA4B,IAAI;AAGtE,MAAC,cAAc,CAAC,SAASA,cAAAA,QAAQ,KAAK,4BAA4B,IAAI;AAMtE,MAAC,sBAAsB,CAAC,SAASA,cAAAA,QAAQ,KAAK,4BAA4B,IAAI;AAG9E,MAAC,wBAAwB,CAAC,YAAYA,cAAO,QAAC,IAAI,wBAAwB,OAAO,EAAE;;;;;;;;"}