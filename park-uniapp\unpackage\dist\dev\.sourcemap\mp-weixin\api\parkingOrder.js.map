{"version": 3, "file": "parkingOrder.js", "sources": ["api/parkingOrder.js"], "sourcesContent": ["import  request  from '../utils/request'\r\n\r\n// 根据车牌号,场库编号查询订单(小程序停车缴费)\r\nexport const getParkingOrderDetail = params => request.post('/wx/parking/order/plateNo', params)\r\n\r\n// 查询临停订单列表\r\nexport const getParkingOrderList = params => request.post('/wx/parking/order/list', params)\r\n\r\n// 停车缴费预下单\r\nexport const createParkingOrder = params => request.post('/wx/parking/order/create', params)\r\n\r\n// 停车订单支付成功前端回调\r\nexport const payParkingOrderCallBack = params => request.post('/wx/parking/order/front/payCallback', params)\r\n\r\n// 临停h5跳转小程序支付\r\nexport const paymentTemporary = params => request.post('/wx/parking/order/paymentTemporary', params)\r\n\r\n// 无牌车入场\r\nexport const noPlateIn = params => request.post('/wx/parking/order/noPlateIn', params)\r\n\r\n// 无牌车出场\r\nexport const noPlateOut = params => request.post('/wx/parking/order/noPlateOut', params)"], "names": ["request"], "mappings": ";;AAGY,MAAC,wBAAwB,YAAUA,cAAAA,QAAQ,KAAK,6BAA6B,MAAM;AAGnF,MAAC,sBAAsB,YAAUA,cAAAA,QAAQ,KAAK,0BAA0B,MAAM;AAG9E,MAAC,qBAAqB,YAAUA,cAAAA,QAAQ,KAAK,4BAA4B,MAAM;AAM/E,MAAC,mBAAmB,YAAUA,cAAAA,QAAQ,KAAK,sCAAsC,MAAM;AAGvF,MAAC,YAAY,YAAUA,cAAAA,QAAQ,KAAK,+BAA+B,MAAM;AAGzE,MAAC,aAAa,YAAUA,cAAAA,QAAQ,KAAK,gCAAgC,MAAM;;;;;;;"}