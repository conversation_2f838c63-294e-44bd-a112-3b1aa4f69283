{"version": 3, "file": "user.js", "sources": ["api/user.js"], "sourcesContent": ["import  request  from '../utils/request'\r\n\r\n// 根据code获取手机号\r\nexport const getPhoneNumberByCode = phoneCode => request.get(`/wxauth/phoneCode/${phoneCode}`)\r\n\r\n// 更新用户信息\r\nexport const updateUserInfo = params => request.post('/wxauth/wxUser/update', params)"], "names": ["request"], "mappings": ";;AAGY,MAAC,uBAAuB,eAAaA,sBAAQ,IAAI,qBAAqB,SAAS,EAAE;AAGjF,MAAC,iBAAiB,YAAUA,cAAAA,QAAQ,KAAK,yBAAyB,MAAM;;;"}