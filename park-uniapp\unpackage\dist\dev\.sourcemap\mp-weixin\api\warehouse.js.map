{"version": 3, "file": "warehouse.js", "sources": ["api/warehouse.js"], "sourcesContent": ["import request  from '../utils/request'\r\n\r\n// 获取厂库列表\r\nexport const getParkWareHouseList = () => request.get('/wx/warehouse/list')\r\n\r\n// 根据ID获取场库详细信息\r\nexport const getWarehouseDetail = (id) => request.get(`/wx/warehouse/${id}`)\r\n"], "names": ["request"], "mappings": ";;AAGY,MAAC,uBAAuB,MAAMA,cAAAA,QAAQ,IAAI,oBAAoB;AAG9D,MAAC,qBAAqB,CAAC,OAAOA,cAAO,QAAC,IAAI,iBAAiB,EAAE,EAAE;;;"}