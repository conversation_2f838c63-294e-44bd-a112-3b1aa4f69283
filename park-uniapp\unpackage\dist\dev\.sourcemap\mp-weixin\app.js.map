{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\nimport initPermission from '@/utils/permission'\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tinitPermission()\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"uview-plus/index.scss\";\r\n</style>\r\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()\r\n// #endif\r\nimport uviewPlus from 'uview-plus'\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  app.use(uviewPlus)\r\n  return {\r\n    app\r\n  }\r\n}\r\n// #endif"], "names": ["initPermission", "uni", "createSSRApp", "App", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,oCAAe;AACfC,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAAA,EACxB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AACD;ACGM,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAC5B,MAAI,IAAIC,uBAAS;AACjB,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}