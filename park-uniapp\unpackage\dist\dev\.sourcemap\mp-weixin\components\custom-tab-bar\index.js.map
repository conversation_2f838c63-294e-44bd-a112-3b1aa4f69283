{"version": 3, "file": "index.js", "sources": ["components/custom-tab-bar/index.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9jb21wb25lbnRzL2N1c3RvbS10YWItYmFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"tab-bar\">\r\n        <view v-for=\"(item, index) in tabs\" :key=\"index\" class=\"tab-item\" :class=\"{ active: isActive(index) }\"\r\n            @tap=\"switchTab(item.pagePath, index)\">\r\n            <image :src=\"getIconPath(index)\" class=\"tab-icon\" mode=\"aspectFit\"></image>\r\n            <text>{{ item.text }}</text>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted, computed } from 'vue'\r\n\r\nconst current = ref(0)\r\n\r\n// 定义标签页\r\nconst tabs = [\r\n    {\r\n        pagePath: \"pages/home/<USER>\",\r\n        text: \"首页\",\r\n        icon: \"/static/tabbar/home_off.png\",\r\n        selectedIcon: \"/static/tabbar/home_on.png\"\r\n    },\r\n    {\r\n        pagePath: \"pages/warehouse/warehouse\",\r\n        text: \"场库\",\r\n        icon: \"/static/tabbar/customer_off.png\",\r\n        selectedIcon: \"/static/tabbar/customer_on.png\"\r\n    },\r\n    {\r\n        pagePath: \"pages/package/package\",\r\n        text: \"套餐\",\r\n        icon: \"/static/tabbar/find_off.png\",\r\n        selectedIcon: \"/static/tabbar/find_on.png\"\r\n    },\r\n    {\r\n        pagePath: \"pages/mine/mine\",\r\n        text: \"我的\",\r\n        icon: \"/static/tabbar/work_off.png\",\r\n        selectedIcon: \"/static/tabbar/work_on.png\"\r\n    }\r\n]\r\n\r\n// 判断是否激活\r\nconst currentPath = computed(() => {\r\n    const pages = getCurrentPages();\r\n    return pages[pages.length - 1]?.route || '';\r\n});\r\n\r\nconst isActive = (index) => currentPath.value === tabs[index].pagePath;\r\n\r\n// 获取图标路径\r\nconst getIconPath = (index) => {\r\n    return isActive(index) ? tabs[index].selectedIcon : tabs[index].icon\r\n}\r\n\r\n// 切换标签页\r\nconst switchTab = (path, index) => {\r\n    if (current.value === index) return;\r\n    uni.switchTab({\r\n        url: `/${path}`\r\n    })\r\n}\r\n\r\n// 页面显示时更新状态\r\nconst updateCurrent = () => {\r\n    const pages = getCurrentPages()\r\n    if (!pages || pages.length === 0) {\r\n        current.value = 0\r\n        return\r\n    }\r\n\r\n    const currentPage = pages[pages.length - 1]\r\n    const currentPath = currentPage.route\r\n    const index = tabs.findIndex(item => item.pagePath === currentPath)\r\n    current.value = index !== -1 ? index : 0\r\n}\r\n\r\n// 监听页面显示\r\nuni.$on('tabPageShow', updateCurrent)\r\n\r\nonMounted(() => {\r\n    updateCurrent()\r\n})\r\n\r\nonUnmounted(() => {\r\n    uni.$off('tabPageShow', updateCurrent)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.tab-bar {\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 120rpx;\r\n    background: #ffffff;\r\n    display: flex;\r\n    padding-bottom: env(safe-area-inset-bottom);\r\n    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    z-index: 999;\r\n\r\n    .tab-item {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transition: all 0.3s;\r\n\r\n        .tab-icon {\r\n            width: 60rpx;\r\n            height: 60rpx;\r\n            margin-bottom: 4rpx;\r\n        }\r\n\r\n        text {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n            transition: color 0.3s;\r\n        }\r\n\r\n        &.active {\r\n            .tab-icon {\r\n                filter: none;\r\n            }\r\n\r\n            text {\r\n                color: #61adff;\r\n            }\r\n        }\r\n\r\n        &:active {\r\n            opacity: 0.8;\r\n        }\r\n    }\r\n}\r\n</style>", "import Component from 'F:/parking/park-uniapp/components/custom-tab-bar/index.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni", "currentPath", "onMounted", "onUnmounted", "Component"], "mappings": ";;;;;AAaA,UAAM,UAAUA,cAAG,IAAC,CAAC;AAGrB,UAAM,OAAO;AAAA,MACT;AAAA,QACI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,MACjB;AAAA,MACD;AAAA,QACI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,MACjB;AAAA,MACD;AAAA,QACI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,MACjB;AAAA,MACD;AAAA,QACI,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc;AAAA,MACjB;AAAA,IACL;AAGA,UAAM,cAAcC,cAAQ,SAAC,MAAM;;AAC/B,YAAM,QAAQ;AACd,eAAO,WAAM,MAAM,SAAS,CAAC,MAAtB,mBAAyB,UAAS;AAAA,IAC7C,CAAC;AAED,UAAM,WAAW,CAAC,UAAU,YAAY,UAAU,KAAK,KAAK,EAAE;AAG9D,UAAM,cAAc,CAAC,UAAU;AAC3B,aAAO,SAAS,KAAK,IAAI,KAAK,KAAK,EAAE,eAAe,KAAK,KAAK,EAAE;AAAA,IACpE;AAGA,UAAM,YAAY,CAAC,MAAM,UAAU;AAC/B,UAAI,QAAQ,UAAU;AAAO;AAC7BC,oBAAAA,MAAI,UAAU;AAAA,QACV,KAAK,IAAI,IAAI;AAAA,MACrB,CAAK;AAAA,IACL;AAGA,UAAM,gBAAgB,MAAM;AACxB,YAAM,QAAQ,gBAAiB;AAC/B,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B,gBAAQ,QAAQ;AAChB;AAAA,MACH;AAED,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAMC,eAAc,YAAY;AAChC,YAAM,QAAQ,KAAK,UAAU,UAAQ,KAAK,aAAaA,YAAW;AAClE,cAAQ,QAAQ,UAAU,KAAK,QAAQ;AAAA,IAC3C;AAGAD,kBAAAA,MAAI,IAAI,eAAe,aAAa;AAEpCE,kBAAAA,UAAU,MAAM;AACZ,oBAAe;AAAA,IACnB,CAAC;AAEDC,kBAAAA,YAAY,MAAM;AACdH,0BAAI,KAAK,eAAe,aAAa;AAAA,IACzC,CAAC;;;;;;;;;;;;;;;;;ACtFD,GAAG,gBAAgBI,SAAS;"}