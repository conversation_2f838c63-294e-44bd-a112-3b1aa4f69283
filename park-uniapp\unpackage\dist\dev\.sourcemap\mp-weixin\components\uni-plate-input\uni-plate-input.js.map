{"version": 3, "file": "uni-plate-input.js", "sources": ["components/uni-plate-input/uni-plate-input.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9jb21wb25lbnRzL3VuaS1wbGF0ZS1pbnB1dC91bmktcGxhdGUtaW5wdXQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"so-mask\">\r\n    <view class=\"so-plate animation-scale-up\">\r\n      <view class=\"so-plate-head\">\r\n        <view class=\"so-plate-type\">\r\n          <radio-group class=\"u-flex\" @change=\"typeChange\">\r\n            <label>\r\n              <radio value=\"1\" :checked=\"type === 1\" />\r\n              <view class=\"name\">普通车牌</view>\r\n            </label>\r\n            <label>\r\n              <radio value=\"2\" :checked=\"type === 2\" />\r\n              <view class=\"name\"> 新能源车牌 </view>\r\n            </label>\r\n          </radio-group>\r\n        </view>\r\n      </view>\r\n      <view class=\"so-plate-body\">\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 0 }\" @tap=\"inputSwitch\" data-index=\"0\">\r\n          <text>{{ currentInputValue[0] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 1 }\" @tap=\"inputSwitch\" data-index=\"1\">\r\n          <text>{{ currentInputValue[1] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-dot\"></view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 2 }\" @tap=\"inputSwitch\" data-index=\"2\">\r\n          <text>{{ currentInputValue[2] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 3 }\" @tap=\"inputSwitch\" data-index=\"3\">\r\n          <text>{{ currentInputValue[3] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 4 }\" @tap=\"inputSwitch\" data-index=\"4\">\r\n          <text>{{ currentInputValue[4] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 5 }\" @tap=\"inputSwitch\" data-index=\"5\">\r\n          <text>{{ currentInputValue[5] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 6 }\" @tap=\"inputSwitch\" data-index=\"6\">\r\n          <text>{{ currentInputValue[6] }}</text>\r\n        </view>\r\n        <view class=\"so-plate-word\" :class=\"{ active: currentInputIndex == 7 }\" @tap=\"inputSwitch\" v-if=\"type == 2\"\r\n          data-index=\"7\">\r\n          <text>{{ currentInputValue[7] }}</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"so-plate-foot\">\r\n        <view class=\"so-plate-keyboard\" :style=\"{ height: keyboardHeight }\">\r\n          <view id=\"keyboard\">\r\n            <block v-if=\"inputType == 1\">\r\n              <view hover-class=\"hover\" class=\"so-plate-key\" v-for=\"el of provinceText\" :key=\"el\" :data-value=\"el\"\r\n                @tap=\"chooseKey\">{{ el }}</view>\r\n            </block>\r\n            <block v-if=\"inputType == 1\">\r\n              <text class=\"so-plate-key fill-block\"></text>\r\n            </block>\r\n            <block v-if=\"inputType >= 3\">\r\n              <view hover-class=\"hover\" class=\"so-plate-key\" v-for=\"el of numberText\" :key=\"el\" :data-value=\"el\"\r\n                @tap=\"chooseKey\">{{ el }}</view>\r\n            </block>\r\n            <block v-if=\"inputType >= 2\">\r\n              <view hover-class=\"hover\" class=\"so-plate-key\" v-for=\"el of wordText\" :key=\"el\" :data-value=\"el\"\r\n                @tap=\"chooseKey\">{{ el }}</view>\r\n            </block>\r\n            <block v-if=\"inputType == 3\">\r\n              <text v-for=\"el of fillBlock\" :key=\"el.num\" class=\"so-plate-key fill-block\"></text>\r\n            </block>\r\n            <block v-if=\"inputType == 4\">\r\n              <view hover-class=\"hover\" class=\"so-plate-key\" v-for=\"el of lastWordText\" :key=\"el\" :data-value=\"el\"\r\n                @tap=\"chooseKey\">{{ el }}</view>\r\n            </block>\r\n            <text v-if=\"inputType == 4\" class=\"so-plate-key fill-block\"></text>\r\n          </view>\r\n        </view>\r\n        <view class=\"so-plate-btn-group\">\r\n          <view>\r\n            <button class=\"so-plate-btn so-plate-btn--cancel\" @tap=\"$emit('close')\">取消</button>\r\n          </view>\r\n          <view>\r\n            <button class=\"so-plate-btn so-plate-btn--delete\" @tap=\"deleteKey\">删除</button>\r\n            <button class=\"so-plate-btn so-plate-btn--submit\" @tap=\"exportPlate\">完成</button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"safe-area-inset-bottom\"></view>\r\n  </view>\r\n</template>\r\n<script setup>\r\nimport { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'\r\n\r\n// Props\r\nconst props = defineProps({\r\n  plate: {\r\n    type: String,\r\n    default: ''\r\n  }\r\n})\r\n\r\n// Emits\r\nconst emit = defineEmits(['close', 'typeChange', 'export'])\r\n\r\n// 响应式数据\r\nconst type = ref(1) // 车牌类型\r\nconst currentInputIndex = ref(0) // 当前编辑的输入框\r\nconst currentInputValue = ref(['', '', '', '', '', '', ''])\r\nconst keyboardHeightInit = ref(false)\r\nconst keyboardHeight = ref('auto')\r\n\r\n// 静态数据\r\nconst fillBlock = [{ num: 11 }, { num: 12 }, { num: 13 }, { num: 14 }, { num: 15 }, { num: 16 }] // 避免:key报错\r\nconst provinceText = [\r\n  '粤', '京', '冀', '沪', '津', '晋', '蒙', '辽', '吉', '黑', '苏', '浙', '皖', '闽', '赣',\r\n  '鲁', '豫', '鄂', '湘', '桂', '琼', '渝', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新'\r\n]\r\nconst numberText = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']\r\nconst wordText = [\r\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N',\r\n  'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'\r\n]\r\nconst lastWordText = ['挂', '港', '学', '领', '警']\r\n\r\n// 计算属性 - 输入框类型\r\nconst inputType = computed(() => {\r\n  switch (currentInputIndex.value) {\r\n    case 0:\r\n      return 1\r\n    case 1:\r\n      return 2\r\n    case 2:\r\n    case 3:\r\n    case 4:\r\n    case 5:\r\n      return 3\r\n    case 6:\r\n      return type.value == 2 ? 3 : 4\r\n    case 7:\r\n      return 4\r\n    default:\r\n      return 1\r\n  }\r\n})\r\n\r\n// 监听当前输入索引变化\r\nwatch(currentInputIndex, (n, o) => {\r\n  if (!keyboardHeightInit.value) return\r\n  nextTick(() => {\r\n    changeKeyboardHeight()\r\n  })\r\n})\r\n\r\n// 方法\r\nconst typeChange = (e) => {\r\n  emit('typeChange', e.detail.value)\r\n  const { value } = e.detail\r\n  type.value = parseInt(value)\r\n  currentInputIndex.value = 0\r\n  if (value == 1) {\r\n    currentInputValue.value = ['', '', '', '', '', '', '']\r\n  } else {\r\n    currentInputValue.value = ['', '', '', '', '', '', '', '']\r\n  }\r\n}\r\n\r\nconst inputSwitch = (e) => {\r\n  const { index } = e.currentTarget.dataset\r\n  currentInputIndex.value = parseInt(index)\r\n}\r\n\r\nconst chooseKey = (e) => {\r\n  const { value } = e.currentTarget.dataset\r\n  currentInputValue.value[currentInputIndex.value] = value\r\n  if (type.value == 1 && currentInputIndex.value < 6) {\r\n    currentInputIndex.value++\r\n  }\r\n  if (type.value == 2 && currentInputIndex.value < 7) {\r\n    currentInputIndex.value++\r\n  }\r\n}\r\n\r\nconst deleteKey = () => {\r\n  currentInputValue.value[currentInputIndex.value] = ''\r\n  if (currentInputIndex.value != 0) currentInputIndex.value--\r\n}\r\n\r\nconst exportPlate = () => {\r\n  const plate = currentInputValue.value.join('')\r\n  let err = false\r\n  if (type.value === 1 && plate.length != 7) {\r\n    err = true\r\n  } else if (type.value === 2 && plate.length != 8) {\r\n    err = true\r\n  }\r\n  if (err)\r\n    return uni.showToast({\r\n      title: '请输入完整的车牌号码',\r\n      icon: 'none'\r\n    })\r\n\r\n  emit('export', plate)\r\n}\r\n\r\nconst changeKeyboardHeight = () => {\r\n  try {\r\n    // 使用 setTimeout 确保 DOM 已经渲染\r\n    setTimeout(() => {\r\n      const query = uni.createSelectorQuery()\r\n      query.select('#keyboard').boundingClientRect()\r\n      query.exec(function (res) {\r\n        if (res && res[0]) {\r\n          keyboardHeight.value = res[0].height + uni.upx2px(30) + 'px'\r\n          keyboardHeightInit.value = true\r\n        }\r\n      })\r\n    }, 50)\r\n  } catch (error) {\r\n    console.warn('changeKeyboardHeight error:', error)\r\n    // 如果查询失败，设置一个默认高度\r\n    keyboardHeight.value = '300px'\r\n    keyboardHeightInit.value = true\r\n  }\r\n}\r\n\r\n// 生命周期 - 组件挂载\r\nonMounted(() => {\r\n  const plateKey = props.plate.split('')\r\n  if (plateKey.length === 7) {\r\n    type.value = 1\r\n  } else if (plateKey.length === 8) {\r\n    type.value = 2\r\n  }\r\n  if (plateKey.length === 7 || plateKey.length === 8) {\r\n    currentInputValue.value = plateKey\r\n    currentInputIndex.value = plateKey.length - 1\r\n  }\r\n\r\n  setTimeout(() => {\r\n    // 在动画结束之后才开始获取\r\n    nextTick(() => {\r\n      changeKeyboardHeight()\r\n    })\r\n  }, 500)\r\n})\r\n</script>\r\n<style scoped lang=\"less\">\r\n.so-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 998;\r\n}\r\n\r\n.so-plate {\r\n  box-sizing: border-box;\r\n  position: absolute;\r\n  bottom: 0;\r\n  width: 100%;\r\n  left: 0;\r\n  background: #fff;\r\n  padding: 25upx 25upx 0 25upx;\r\n\r\n  &-head {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  &-type {\r\n    flex: 1;\r\n    display: block;\r\n\r\n    label {\r\n      display: flex;\r\n      align-items: center;\r\n      min-height: 32upx;\r\n      margin-right: 10upx;\r\n    }\r\n\r\n    .name {\r\n      font-size: 32upx;\r\n      padding: 0 6rpx;\r\n    }\r\n  }\r\n\r\n  &-body {\r\n    box-sizing: border-box;\r\n    padding: 30upx 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  &-word {\r\n    border: 1upx solid #ccc;\r\n    border-radius: 10upx;\r\n    height: 0;\r\n    margin: 0 5upx;\r\n    box-sizing: border-box;\r\n    padding-bottom: calc((100% - 70upx) / 7);\r\n    width: calc((100% - 70upx) / 7);\r\n    position: relative;\r\n\r\n    &.active {\r\n      border-color: #007aff;\r\n      box-shadow: 0 0 15upx 0 #007aff;\r\n    }\r\n\r\n    text {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translateX(-50%) translateY(-50%);\r\n      font-weight: 700;\r\n      font-size: 32upx;\r\n    }\r\n  }\r\n\r\n  &-dot {\r\n    width: 15upx;\r\n    height: 15upx;\r\n    background: #ccc;\r\n    border-radius: 50%;\r\n    margin: 0 5upx;\r\n  }\r\n\r\n  &-keyboard {\r\n    background: #eee;\r\n    margin-left: -25upx;\r\n    margin-right: -25upx;\r\n    padding: 20upx 25upx 10upx 25upx;\r\n    box-sizing: border-box;\r\n    transition: all 0.3s;\r\n\r\n    &>view {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  &-key {\r\n    display: block;\r\n    background: #fff;\r\n    border-radius: 10upx;\r\n    box-shadow: 0 0 8upx 0 #bbb;\r\n    width: 80upx;\r\n    height: 80upx;\r\n    margin: 5upx 0;\r\n    font-size: 32upx;\r\n    text-align: center;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n\r\n    &.hover {\r\n      background: #efefef;\r\n    }\r\n\r\n    &.fill-block {\r\n      width: 80upx;\r\n      height: 80upx;\r\n      background: none;\r\n      box-shadow: none;\r\n    }\r\n  }\r\n\r\n  &-btn {\r\n    display: inline-block;\r\n    background: #fff;\r\n    border-radius: 10upx;\r\n    box-shadow: 0 0 10upx 0 #bbb;\r\n    font-size: 28upx;\r\n    text-align: center;\r\n    margin: 0 0 0 10upx;\r\n    padding: 0 25upx;\r\n\r\n    &-group {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      background: #eee;\r\n      margin-left: -25upx;\r\n      margin-right: -25upx;\r\n      box-sizing: border-box;\r\n      padding: 0 25upx 40upx 25upx;\r\n    }\r\n\r\n    &--cancel {\r\n      margin: 0;\r\n    }\r\n\r\n    &--submit {\r\n      background: #5773f9;\r\n      color: #fff;\r\n    }\r\n\r\n    &--delete {\r\n      color: #fd6b6d;\r\n    }\r\n  }\r\n}\r\n\r\n.animation-scale-up {\r\n  animation-duration: 0.2s;\r\n  animation-timing-function: ease-out;\r\n  animation-fill-mode: both;\r\n  animation-name: scale-up;\r\n}\r\n\r\n@keyframes scale-up {\r\n  0% {\r\n    opacity: 0.8;\r\n    transform: scale(0.8);\r\n  }\r\n\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'F:/parking/park-uniapp/components/uni-plate-input/uni-plate-input.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "watch", "nextTick", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;AA4FA,UAAM,QAAQ;AAQd,UAAM,OAAO;AAGb,UAAM,OAAOA,cAAG,IAAC,CAAC;AAClB,UAAM,oBAAoBA,cAAG,IAAC,CAAC;AAC/B,UAAM,oBAAoBA,cAAAA,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC1D,UAAM,qBAAqBA,cAAG,IAAC,KAAK;AACpC,UAAM,iBAAiBA,cAAG,IAAC,MAAM;AAGjC,UAAM,YAAY,CAAC,EAAE,KAAK,GAAE,GAAI,EAAE,KAAK,MAAM,EAAE,KAAK,GAAI,GAAE,EAAE,KAAK,GAAE,GAAI,EAAE,KAAK,MAAM,EAAE,KAAK,IAAI;AAC/F,UAAM,eAAe;AAAA,MACnB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACtE;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IAC7E;AACA,UAAM,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACpE,UAAM,WAAW;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IACpD;AACA,UAAM,eAAe,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAG7C,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,cAAQ,kBAAkB,OAAK;AAAA,QAC7B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,KAAK,SAAS,IAAI,IAAI;AAAA,QAC/B,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH,CAAC;AAGDC,kBAAAA,MAAM,mBAAmB,CAAC,GAAG,MAAM;AACjC,UAAI,CAAC,mBAAmB;AAAO;AAC/BC,oBAAAA,WAAS,MAAM;AACb,6BAAsB;AAAA,MAC1B,CAAG;AAAA,IACH,CAAC;AAGD,UAAM,aAAa,CAAC,MAAM;AACxB,WAAK,cAAc,EAAE,OAAO,KAAK;AACjC,YAAM,EAAE,UAAU,EAAE;AACpB,WAAK,QAAQ,SAAS,KAAK;AAC3B,wBAAkB,QAAQ;AAC1B,UAAI,SAAS,GAAG;AACd,0BAAkB,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACzD,OAAS;AACL,0BAAkB,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MAC1D;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,MAAM;AACzB,YAAM,EAAE,MAAK,IAAK,EAAE,cAAc;AAClC,wBAAkB,QAAQ,SAAS,KAAK;AAAA,IAC1C;AAEA,UAAM,YAAY,CAAC,MAAM;AACvB,YAAM,EAAE,MAAK,IAAK,EAAE,cAAc;AAClC,wBAAkB,MAAM,kBAAkB,KAAK,IAAI;AACnD,UAAI,KAAK,SAAS,KAAK,kBAAkB,QAAQ,GAAG;AAClD,0BAAkB;AAAA,MACnB;AACD,UAAI,KAAK,SAAS,KAAK,kBAAkB,QAAQ,GAAG;AAClD,0BAAkB;AAAA,MACnB;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtB,wBAAkB,MAAM,kBAAkB,KAAK,IAAI;AACnD,UAAI,kBAAkB,SAAS;AAAG,0BAAkB;AAAA,IACtD;AAEA,UAAM,cAAc,MAAM;AACxB,YAAM,QAAQ,kBAAkB,MAAM,KAAK,EAAE;AAC7C,UAAI,MAAM;AACV,UAAI,KAAK,UAAU,KAAK,MAAM,UAAU,GAAG;AACzC,cAAM;AAAA,MACV,WAAa,KAAK,UAAU,KAAK,MAAM,UAAU,GAAG;AAChD,cAAM;AAAA,MACP;AACD,UAAI;AACF,eAAOC,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAEH,WAAK,UAAU,KAAK;AAAA,IACtB;AAEA,UAAM,uBAAuB,MAAM;AACjC,UAAI;AAEF,mBAAW,MAAM;AACf,gBAAM,QAAQA,cAAG,MAAC,oBAAqB;AACvC,gBAAM,OAAO,WAAW,EAAE,mBAAoB;AAC9C,gBAAM,KAAK,SAAU,KAAK;AACxB,gBAAI,OAAO,IAAI,CAAC,GAAG;AACjB,6BAAe,QAAQ,IAAI,CAAC,EAAE,SAASA,oBAAI,OAAO,EAAE,IAAI;AACxD,iCAAmB,QAAQ;AAAA,YAC5B;AAAA,UACT,CAAO;AAAA,QACF,GAAE,EAAE;AAAA,MACN,SAAQ,OAAO;AACdA,sBAAAA,6EAAa,+BAA+B,KAAK;AAEjD,uBAAe,QAAQ;AACvB,2BAAmB,QAAQ;AAAA,MAC5B;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd,YAAM,WAAW,MAAM,MAAM,MAAM,EAAE;AACrC,UAAI,SAAS,WAAW,GAAG;AACzB,aAAK,QAAQ;AAAA,MACjB,WAAa,SAAS,WAAW,GAAG;AAChC,aAAK,QAAQ;AAAA,MACd;AACD,UAAI,SAAS,WAAW,KAAK,SAAS,WAAW,GAAG;AAClD,0BAAkB,QAAQ;AAC1B,0BAAkB,QAAQ,SAAS,SAAS;AAAA,MAC7C;AAED,iBAAW,MAAM;AAEfF,sBAAAA,WAAS,MAAM;AACb,+BAAsB;AAAA,QAC5B,CAAK;AAAA,MACF,GAAE,GAAG;AAAA,IACR,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjPD,GAAG,gBAAgB,SAAS;"}