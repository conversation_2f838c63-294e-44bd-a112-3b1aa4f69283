{"version": 3, "file": "warehouse-selector.js", "sources": ["components/warehouse-selector/warehouse-selector.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9jb21wb25lbnRzL3dhcmVob3VzZS1zZWxlY3Rvci93YXJlaG91c2Utc2VsZWN0b3IudnVl"], "sourcesContent": ["<template>\r\n  <view>\r\n    <!-- 场库选择弹出框 -->\r\n    <up-popup :show=\"show\" mode=\"bottom\" round=\"20\" :safeAreaInsetBottom=\"true\" @close=\"handleClose\">\r\n      <view class=\"warehouse-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">选择场库</text>\r\n          <up-icon name=\"close\" size=\"18\" color=\"#999\" @click=\"handleClose\"></up-icon>\r\n        </view>\r\n        <scroll-view class=\"warehouse-list\" scroll-y :style=\"{ maxHeight: windowHeightHalf + 'px' }\">\r\n          <view v-for=\"warehouse in warehouseList\" :key=\"warehouse.id\" class=\"warehouse-item\"\r\n            :class=\"{ active: warehouse.id === currentWarehouse.id }\" @click=\"handleSelect(warehouse)\">\r\n            <text class=\"warehouse-item-name\">{{ warehouse.name }}</text>\r\n            <up-icon v-if=\"warehouse.id === currentWarehouse.id\" name=\"checkmark\" size=\"16\" color=\"#40a9ff\"></up-icon>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </up-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n\r\n// 定义props\r\nconst props = defineProps({\r\n  show: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  warehouseList: {\r\n    type: Array,\r\n    default: () => []\r\n  },\r\n  currentWarehouse: {\r\n    type: Object,\r\n    default: () => ({})\r\n  },\r\n  windowHeightHalf: {\r\n    type: Number,\r\n    default: 400\r\n  }\r\n});\r\n\r\n// 定义emits\r\nconst emit = defineEmits(['close', 'select']);\r\n\r\n// 关闭弹出框\r\nconst handleClose = () => {\r\n  emit('close');\r\n};\r\n\r\n// 选择场库\r\nconst handleSelect = (warehouse) => {\r\n  emit('select', warehouse);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 场库选择弹出框\r\n.warehouse-popup {\r\n  background-color: #fff;\r\n\r\n  .popup-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 32rpx;\r\n    border-bottom: 1rpx solid #e2e2e2;\r\n\r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .warehouse-list {\r\n    padding: 16rpx 0;\r\n\r\n    .warehouse-item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 32rpx;\r\n      border-bottom: 1rpx solid #f8f9fa;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &.active {\r\n        background-color: #f0f8ff;\r\n\r\n        .warehouse-item-name {\r\n          color: #2da0fe;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      &:active {\r\n        background-color: #f5f5f5;\r\n      }\r\n\r\n      .warehouse-item-name {\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        text-align: center;\r\n        flex: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import Component from 'F:/parking/park-uniapp/components/warehouse-selector/warehouse-selector.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,UAAM,OAAO;AAGb,UAAM,cAAc,MAAM;AACxB,WAAK,OAAO;AAAA,IACd;AAGA,UAAM,eAAe,CAAC,cAAc;AAClC,WAAK,UAAU,SAAS;AAAA,IAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrDA,GAAG,gBAAgB,SAAS;"}