{"version": 3, "file": "index.js", "sources": ["config/index.js"], "sourcesContent": ["// config.js\r\nconst ENV_CONFIG = {\r\n  // 开发环境（本地调试）\r\n  develop: {\r\n    BASE_URL: 'http://localhost:8080',\r\n\t// BASE_URL: 'http://**************:8080',\r\n    API_PREFIX: ''\r\n  },\r\n  // 测试环境\r\n  trial: {\r\n    BASE_URL: 'https://test-parknew.lgfw24hours.com:3443',\r\n    API_PREFIX: '/api'\r\n  },\r\n  // 生产环境\r\n  release: {\r\n    BASE_URL: 'https://test-parknew.lgfw24hours.com:3443',\r\n    API_PREFIX: '/api'\r\n  }\r\n};\r\n\r\n// 获取小程序环境版本\r\nconst env = wx.getAccountInfoSync().miniProgram.envVersion || 'develop';\r\n\r\n// 拼接完整URL\r\nconst FULL_URL = `${ENV_CONFIG[env].BASE_URL}${ENV_CONFIG[env].API_PREFIX}`;\r\n\r\n// 导出完整URL\r\nexport const URL = FULL_URL;"], "names": ["wx"], "mappings": ";;AACA,MAAM,aAAa;AAAA;AAAA,EAEjB,SAAS;AAAA,IACP,UAAU;AAAA;AAAA,IAEV,YAAY;AAAA,EACb;AAAA;AAAA,EAED,OAAO;AAAA,IACL,UAAU;AAAA,IACV,YAAY;AAAA,EACb;AAAA;AAAA,EAED,SAAS;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,EACb;AACH;AAGA,MAAM,MAAMA,cAAE,KAAC,mBAAkB,EAAG,YAAY,cAAc;AAG9D,MAAM,WAAW,GAAG,WAAW,GAAG,EAAE,QAAQ,GAAG,WAAW,GAAG,EAAE,UAAU;AAG7D,MAAC,MAAM;;"}