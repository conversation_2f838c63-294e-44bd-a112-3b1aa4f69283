{"version": 3, "file": "u-empty.js", "sources": ["node_modules/uview-plus/components/u-empty/u-empty.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9ub2RlX21vZHVsZXMvdXZpZXctcGx1cy9jb21wb25lbnRzL3UtZW1wdHkvdS1lbXB0eS52dWU"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-empty\"\n\t    :style=\"[emptyStyle]\"\n\t    v-if=\"show\"\n\t>\n\t\t<u-icon\n\t\t    v-if=\"!isSrc\"\n\t\t    :name=\"mode === 'message' ? 'chat' : `empty-${mode}`\"\n\t\t    :size=\"iconSize\"\n\t\t    :color=\"iconColor\"\n\t\t    margin-top=\"14\"\n\t\t></u-icon>\n\t\t<image\n\t\t    v-else\n\t\t    :style=\"{\n\t\t\t\twidth: addUnit(width),\n\t\t\t\theight: addUnit(height),\n\t\t\t}\"\n\t\t    :src=\"icon\"\n\t\t    mode=\"widthFix\"\n\t\t></image>\n\t\t<text\n\t\t    class=\"u-empty__text\"\n\t\t    :style=\"[textStyle]\"\n\t\t>{{text ? text : icons[mode]}}</text>\n\t\t<view class=\"u-empty__wrap\" v-if=\"$slots.default || $slots.$default\">\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * empty 内容为空\n\t * @description 该组件用于需要加载内容，但是加载的第一页数据就为空，提示一个\"没有内容\"的场景， 我们精心挑选了十几个场景的图标，方便您使用。\n\t * @tutorial https://ijry.github.io/uview-plus/components/empty.html\n\t * @property {String}\t\t\ticon\t\t内置图标名称，或图片路径，建议绝对路径\n\t * @property {String}\t\t\ttext\t\t提示文字\n\t * @property {String}\t\t\ttextColor\t文字颜色 (默认 '#c0c4cc' )\n\t * @property {String | Number}\ttextSize\t文字大小 （默认 14 ）\n\t * @property {String}\t\t\ticonColor\t图标的颜色 （默认 '#c0c4cc' ）\n\t * @property {String | Number}\ticonSize\t图标的大小 （默认 90 ）\n\t * @property {String}\t\t\tmode\t\t选择预置的图标类型 （默认 'data' ）\n\t * @property {String | Number}\twidth\t\t图标宽度，单位px （默认 160 ）\n\t * @property {String | Number}\theight\t\t图标高度，单位px （默认 160 ）\n\t * @property {Boolean}\t\t\tshow\t\t是否显示组件 （默认 true ）\n\t * @property {String | Number}\tmarginTop\t组件距离上一个元素之间的距离，默认px单位 （默认 0 ）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * \n\t * @event {Function} click 点击组件时触发\n\t * @event {Function} close 点击关闭按钮时触发\n\t * @example <u-empty text=\"所谓伊人，在水一方\" mode=\"list\"></u-empty>\n\t */\n\texport default {\n\t\tname: \"u-empty\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ticons: {\n\t\t\t\t\tcar: '购物车为空',\n\t\t\t\t\tpage: '页面不存在',\n\t\t\t\t\tsearch: '没有搜索结果',\n\t\t\t\t\taddress: '没有收货地址',\n\t\t\t\t\twifi: '没有WiFi',\n\t\t\t\t\torder: '订单为空',\n\t\t\t\t\tcoupon: '没有优惠券',\n\t\t\t\t\tfavor: '暂无收藏',\n\t\t\t\t\tpermission: '无权限',\n\t\t\t\t\thistory: '无历史记录',\n\t\t\t\t\tnews: '无新闻列表',\n\t\t\t\t\tmessage: '消息列表为空',\n\t\t\t\t\tlist: '列表为空',\n\t\t\t\t\tdata: '数据为空',\n\t\t\t\t\tcomment: '暂无评论',\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 组件样式\n\t\t\temptyStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tstyle.marginTop = addUnit(this.marginTop)\n\t\t\t\t// 合并customStyle样式，此参数通过mixin中的props传递\n\t\t\t\treturn deepMerge(addStyle(this.customStyle), style)\n\t\t\t},\n\t\t\t// 文本样式\n\t\t\ttextStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tstyle.color = this.textColor\n\t\t\t\tstyle.fontSize = addUnit(this.textSize)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 判断icon是否图片路径\n\t\t\tisSrc() {\n\t\t\t\treturn this.icon.indexOf('/') >= 0\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddUnit\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n\t$u-empty-text-margin-top:20rpx !default;\n\t$u-empty-slot-margin-top:20rpx !default;\n\n\t.u-empty {\n\t\t@include flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\n\t\t&__text {\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tmargin-top: $u-empty-text-margin-top;\n\t\t}\n\t}\n\t\t.u-slot-wrap {\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tmargin-top:$u-empty-slot-margin-top;\n\t\t}\n</style>\n", "import Component from 'F:/parking/park-uniapp/node_modules/uview-plus/components/u-empty/u-empty.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "deepMerge", "addStyle"], "mappings": ";;AA0DC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,cAAAA,SAASC,cAAK,OAAEC,qBAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,aAAa;AACZ,YAAM,QAAQ,CAAC;AACf,YAAM,YAAYC,sBAAQ,KAAK,SAAS;AAExC,aAAOC,cAAS,UAACC,cAAQ,SAAC,KAAK,WAAW,GAAG,KAAK;AAAA,IAClD;AAAA;AAAA,IAED,YAAY;AACX,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAWF,sBAAQ,KAAK,QAAQ;AACtC,aAAO;AAAA,IACP;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,KAAK,QAAQ,GAAG,KAAK;AAAA,IAClC;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,SAAAA,cAAM;AAAA,EACP;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxGD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}