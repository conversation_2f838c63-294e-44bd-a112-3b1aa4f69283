{"version": 3, "file": "u-grid.js", "sources": ["node_modules/uview-plus/components/u-grid/u-grid.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9ub2RlX21vZHVsZXMvdXZpZXctcGx1cy9jb21wb25lbnRzL3UtZ3JpZC91LWdyaWQudnVl"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-grid\"\n\t\tref='u-grid'\n\t    :style=\"[gridStyle]\"\n\t>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * grid 宫格布局\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。\n\t * @tutorial https://ijry.github.io/uview-plus/components/grid.html\n\t * @property {String | Number}\tcol\t\t\t宫格的列数（默认 3 ）\n\t * @property {Boolean}\t\t\tborder\t\t是否显示宫格的边框（默认 false ）\n\t * @property {String}\t\t\talign\t\t宫格对齐方式，表现为数量少的时候，靠左，居中，还是靠右 （默认 'left' ）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * @event {Function} click 点击宫格触发\n\t * @example <u-grid :col=\"3\" @click=\"click\"></u-grid>\n\t */\n\texport default {\n\t\tname: 'u-grid',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tindex: 0,\n\t\t\t\twidth: 0\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\n\t\t\tparentData() {\n\t\t\t\tif (this.children.length) {\n\t\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t\t// 判断子组件(u-radio)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\n\t\t\t\t\t\ttypeof(child.updateParentData) == 'function' && child.updateParentData();\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tcreated() {\n\t\t\t// 如果将children定义在data中，在微信小程序会造成循环引用而报错\n\t\t\tthis.children = []\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算父组件的值是否发生变化\n\t\t\tparentData() {\n\t\t\t\treturn [this.hoverClass, this.col, this.size, this.border];\n\t\t\t},\n\t\t\t// 宫格对齐方式\n\t\t\tgridStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tswitch (this.align) {\n\t\t\t\t\tcase 'left':\n\t\t\t\t\t\tstyle.justifyContent = 'flex-start';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'center':\n\t\t\t\t\t\tstyle.justifyContent = 'center';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'right':\n\t\t\t\t\t\tstyle.justifyContent = 'flex-end';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tstyle.justifyContent = 'flex-start';\n\t\t\t\t};\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle));\n\t\t\t}\n\t\t},\n\t\temits: ['click'], // 防止事件执行两次\n\t\t// 20240409发现抖音小程序如果开启virtualHost会出现严重问题，几乎所有事件包括created等生命周期事件全部失效。\n\t\t// #ifdef MP-WEIXIN\n\t\toptions: {\n\t\t    // virtualHost: true ,//将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等\n\t\t},\n\t\t// #endif\n\t\tmethods: {\n\t\t\t// 此方法由u-grid-item触发，用于在u-grid发出事件\n\t\t\tchildClick(name) {\n\t\t\t\tthis.$emit('click', name)\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n     $u-grid-width:100% !default;\n\t.u-grid {\n\t\t/* #ifdef APP-NVUE */\n\t\twidth: $u-grid-width;\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\toverflow: hidden;\n\t\tdisplay: block;\n\t\t/* #endif */\n\t\tjustify-content: center;\n\t\t@include flex;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\t// 在uni-app中应尽量避免使用flex布局以外的方式,因为nvue/uvue等方案都支持flex布局\n\t\t// 这里使用grid布局使用为目前20240409uni-app在抖音小程序开启virtualHost时有bug，存在事件失效问题。\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: grid;\n\t\tgrid-gap: v-bind(gap);\n\t\tgrid-template-columns: repeat(v-bind(col), 1fr);\n\t\t/* #endif */\n\t}\n</style>\n", "import Component from 'F:/parking/park-uniapp/node_modules/uview-plus/components/u-grid/u-grid.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "addStyle"], "mappings": ";;AA0BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,cAAAA,SAASC,cAAK,OAAEC,qBAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,aAAa;AACZ,UAAI,KAAK,SAAS,QAAQ;AACzB,aAAK,SAAS,IAAI,WAAS;AAE1B,iBAAO,MAAM,oBAAqB,cAAc,MAAM,iBAAgB;AAAA,SACtE;AAAA,MACF;AAAA,IACA;AAAA,EACD;AAAA,EACD,UAAU;AAET,SAAK,WAAW,CAAC;AAAA,EACjB;AAAA,EACD,UAAU;AAAA;AAAA,IAET,aAAa;AACZ,aAAO,CAAC,KAAK,YAAY,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IACzD;AAAA;AAAA,IAED,YAAY;AACX,UAAI,QAAQ,CAAA;AACZ,cAAQ,KAAK,OAAK;AAAA,QACjB,KAAK;AACJ,gBAAM,iBAAiB;AACvB;AAAA,QACD,KAAK;AACJ,gBAAM,iBAAiB;AACvB;AAAA,QACD,KAAK;AACJ,gBAAM,iBAAiB;AACvB;AAAA,QACD;AACC,gBAAM,iBAAiB;AAAA;AAEzB,aAAOC,cAAS,UAAC,OAAOC,cAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA;AAAA;AAAA,EAGf,SAAS;AAAA;AAAA,EAER;AAAA,EAED,SAAS;AAAA;AAAA,IAER,WAAW,MAAM;AAChB,WAAK,MAAM,SAAS,IAAI;AAAA,IACzB;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;ACrFF,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}