{"version": 3, "file": "u-loading-page.js", "sources": ["node_modules/uview-plus/components/u-loading-page/u-loading-page.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9ub2RlX21vZHVsZXMvdXZpZXctcGx1cy9jb21wb25lbnRzL3UtbG9hZGluZy1wYWdlL3UtbG9hZGluZy1wYWdlLnZ1ZQ"], "sourcesContent": ["<template>\n    <u-transition\n        :show=\"loading\"\n        :custom-style=\"{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: bgColor,\n            display: 'flex',\n            zIndex: zIndex,\n            ...customStyle\n        }\"\n    >\n        <view class=\"u-loading-page\">\n            <view class=\"u-loading-page__warpper\">\n                <view class=\"u-loading-page__warpper__loading-icon\">\n                    <image\n                        v-if=\"image\"\n                        :src=\"image\"\n                        class=\"u-loading-page__warpper__loading-icon__img\"\n                        mode=\"widthFit\"\n\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\twidth: addUnit(iconSize),\n\t\t\t\t\t\t    height: addUnit(iconSize)\n\t\t\t\t\t\t}\"\n                    ></image>\n                    <u-loading-icon\n                        v-else\n                        :mode=\"loadingMode\"\n                        :size=\"addUnit(iconSize)\"\n                        :color=\"loadingColor\"\n                    ></u-loading-icon>\n                </view>\n                <slot>\n                    <text\n                        class=\"u-loading-page__warpper__text\"\n                        :style=\"{\n                            fontSize: addUnit(fontSize),\n                            color: color,\n                        }\"\n                        >{{ loadingText }}</text\n                    >\n                </slot>\n            </view>\n        </view>\n    </u-transition>\n</template>\n\n<script>\nimport { props } from \"./props\";\nimport { mpMixin } from '../../libs/mixin/mpMixin';\nimport { mixin } from '../../libs/mixin/mixin';\nimport { addUnit } from '../../libs/function/index';\n/**\n * loadingPage 加载动画\n * @description 警此组件为一个小动画，目前用在uView的loadmore加载更多和switch开关等组件的正在加载状态场景。\n * @tutorial https://ijry.github.io/uview-plus/components/loading.html\n * @property {String | Number}\tloadingText\t\t提示内容  (默认 '正在加载' )\n * @property {String}\t\t\timage\t\t\t文字上方用于替换loading动画的图片\n * @property {String}\t\t\tloadingMode\t\t加载动画的模式，circle-圆形，spinner-花朵形，semicircle-半圆形 （默认 'circle' ）\n * @property {Boolean}\t\t\tloading\t\t\t是否加载中 （默认 false ）\n * @property {String}\t\t\tbgColor\t\t\t背景色 （默认 '#ffffff' ）\n * @property {String}\t\t\tcolor\t\t\t文字颜色 （默认 '#C8C8C8' ）\n * @property {String | Number}\tfontSize\t\t文字大小 （默认 19 ）\n * @property {String | Number}\ticonSize\t\t图标大小 （默认 28 ）\n * @property {String}\t\t\tloadingColor\t加载中图标的颜色，只能rgb或者十六进制颜色值 （默认 '#C8C8C8' ）\n * @property {Number}\t\t\tzIndex\t        z-index层级 （默认10 ）\n * @property {Object}\t\t\tcustomStyle\t\t自定义样式\n * @example <u-loading mode=\"circle\"></u-loading>\n */\nexport default {\n    name: \"u-loading-page\",\n    mixins: [mpMixin, mixin, props],\n    data() {\n        return {};\n    },\n    methods: {\n        addUnit\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n\n$text-color: rgb(200, 200, 200) !default;\n$text-size: 19px !default;\n$u-loading-icon-margin-bottom: 10px !default;\n\n.u-loading-page {\n    @include flex(column);\n    flex: 1;\n    align-items: center;\n    justify-content: center;\n\n    &__warpper {\n        margin-top: -150px;\n        justify-content: center;\n        align-items: center;\n        /* #ifndef APP-NVUE */\n        color: $text-color;\n        font-size: $text-size;\n        /* #endif */\n        @include flex(column);\n\n        &__loading-icon {\n            margin-bottom: $u-loading-icon-margin-bottom;\n\n            &__img {\n                width: 40px;\n                height: 40px;\n            }\n        }\n\n        &__text {\n            font-size: $text-size;\n            color: $text-color;\n        }\n    }\n}\n</style>\n", "import Component from 'F:/parking/park-uniapp/node_modules/uview-plus/components/u-loading-page/u-loading-page.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit"], "mappings": ";;AAwEA,MAAK,YAAU;AAAA,EACX,MAAM;AAAA,EACN,QAAQ,CAACA,cAAAA,SAASC,cAAK,OAAEC,qBAAK;AAAA,EAC9B,OAAO;AACH,WAAO;EACV;AAAA,EACD,SAAS;AAAA,IACL,SAAAC,cAAM;AAAA,EACV;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChFA,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}