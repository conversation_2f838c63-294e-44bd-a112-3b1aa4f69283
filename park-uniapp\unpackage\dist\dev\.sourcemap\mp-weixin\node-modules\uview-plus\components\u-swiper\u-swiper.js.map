{"version": 3, "file": "u-swiper.js", "sources": ["node_modules/uview-plus/components/u-swiper/u-swiper.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9ub2RlX21vZHVsZXMvdXZpZXctcGx1cy9jb21wb25lbnRzL3Utc3dpcGVyL3Utc3dpcGVyLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view\n\t\tclass=\"u-swiper\"\n\t\t:style=\"{\n\t\t\tbackgroundColor: bgColor,\n\t\t\theight: addUnit(height),\n\t\t\tborderRadius: addUnit(radius)\n\t\t}\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-swiper__loading\"\n\t\t\tv-if=\"loading\"\n\t\t>\n\t\t\t<up-loading-icon mode=\"circle\"></up-loading-icon>\n\t\t</view>\n\t\t<swiper\n\t\t\tv-else\n\t\t\tclass=\"u-swiper__wrapper\"\n\t\t\t:style=\"{\n\t\t\t\tflex: '1',\n\t\t\t\theight: addUnit(height)\n\t\t\t}\"\n\t\t\t@change=\"change\"\n\t\t\t:circular=\"circular\"\n\t\t\t:interval=\"interval\"\n\t\t\t:duration=\"duration\"\n\t\t\t:autoplay=\"autoplay\"\n\t\t\t:current=\"current\"\n\t\t\t:currentItemId=\"currentItemId\"\n\t\t\t:previousMargin=\"addUnit(previousMargin)\"\n\t\t\t:nextMargin=\"addUnit(nextMargin)\"\n\t\t\t:acceleration=\"acceleration\"\n\t\t\t:displayMultipleItems=\"list.length > 0 ? displayMultipleItems : 0\"\n\t\t\t:easingFunction=\"easingFunction\"\n\t\t>\n\t\t\t<swiper-item\n\t\t\t\tclass=\"u-swiper__wrapper__item\"\n\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t:key=\"index\"\n\t\t\t>\n\t\t\t\t<slot :item=\"item\" :index=\"index\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper\"\n\t\t\t\t\t\t:style=\"[itemStyle(index)]\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<!-- 在nvue中，image图片的宽度默认为屏幕宽度，需要通过flex:1撑开，另外必须设置高度才能显示图片 -->\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__image\"\n\t\t\t\t\t\t\tv-if=\"getItemType(item) === 'image'\"\n\t\t\t\t\t\t\t:src=\"getSource(item)\"\n\t\t\t\t\t\t\t:mode=\"imgMode\"\n\t\t\t\t\t\t\t@tap=\"clickHandler(index)\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\theight: addUnit(height),\n\t\t\t\t\t\t\t\tborderRadius: addUnit(radius)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t\t<video\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__video\"\n\t\t\t\t\t\t\tv-if=\"getItemType(item) === 'video'\"\n\t\t\t\t\t\t\t:id=\"`video-${index}`\"\n\t\t\t\t\t\t\t:enable-progress-gesture=\"false\"\n\t\t\t\t\t\t\t:src=\"getSource(item)\"\n\t\t\t\t\t\t\t:poster=\"getPoster(item)\"\n\t\t\t\t\t\t\t:title=\"showTitle && testObject(item) && item.title ? item.title : ''\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\theight: addUnit(height)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t@tap=\"clickHandler(index)\"\n\t\t\t\t\t\t></video>\n\t\t\t\t\t\t<view v-if=\"showTitle && testObject(item) && item.title && testImage(getSource(item))\"\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__title\">\n\t\t\t\t\t\t\t<text class=\"u-line-1\">{{ item.title }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</slot>\n\t\t\t</swiper-item>\n\t\t</swiper>\n\t\t<view class=\"u-swiper__indicator\" :style=\"[addStyle(indicatorStyle)]\">\n\t\t\t<slot name=\"indicator\">\n\t\t\t\t<up-swiper-indicator\n\t\t\t\t\tv-if=\"!loading && indicator && !showTitle\"\n\t\t\t\t\t:indicatorActiveColor=\"indicatorActiveColor\"\n\t\t\t\t\t:indicatorInactiveColor=\"indicatorInactiveColor\"\n\t\t\t\t\t:length=\"list.length\"\n\t\t\t\t\t:current=\"currentIndex\"\n\t\t\t\t\t:indicatorMode=\"indicatorMode\"\n\t\t\t\t></up-swiper-indicator>\n\t\t\t</slot>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\timport { props } from './props.js';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, error } from '../../libs/function/index';\n\timport test from '../../libs/function/test';\n\t/**\n\t * Swiper 轮播图\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，\n\t * @tutorial https://ijry.github.io/uview-plus/components/swiper.html\n\t * @property {Array}\t\t\tlist\t\t\t\t\t轮播图数据\n\t * @property {Boolean}\t\t\tindicator\t\t\t\t是否显示面板指示器（默认 false ）\n\t * @property {String}\t\t\tindicatorActiveColor\t指示器非激活颜色（默认 '#FFFFFF' ）\n\t * @property {String}\t\t\tindicatorInactiveColor\t指示器的激活颜色（默认 'rgba(255, 255, 255, 0.35)' ）\n\t * @property {String | Object}\tindicatorStyle\t\t\t指示器样式，可通过bottom，left，right进行定位\n\t * @property {String}\t\t\tindicatorMode\t\t\t指示器模式（默认 'line' ）\n\t * @property {Boolean}\t\t\tautoplay\t\t\t\t是否自动切换（默认 true ）\n\t * @property {String | Number}\tcurrent\t\t\t\t\t当前所在滑块的 index（默认 0 ）\n\t * @property {String}\t\t\tcurrentItemId\t\t\t当前所在滑块的 item-id ，不能与 current 被同时指定\n\t * @property {String | Number}\tinterval\t\t\t\t滑块自动切换时间间隔（ms）（默认 3000 ）\n\t * @property {String | Number}\tduration\t\t\t\t滑块切换过程所需时间（ms）（默认 300 ）\n\t * @property {Boolean}\t\t\tcircular\t\t\t\t播放到末尾后是否重新回到开头（默认 false ）\n\t * @property {String | Number}\tpreviousMargin\t\t\t前边距，可用于露出前一项的一小部分，nvue和支付宝不支持（默认 0 ）\n\t * @property {String | Number}\tnextMargin\t\t\t\t后边距，可用于露出后一项的一小部分，nvue和支付宝不支持（默认 0 ）\n\t * @property {Boolean}\t\t\tacceleration\t\t\t当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持（默认 false ）\n\t * @property {Number}\t\t\tdisplayMultipleItems\t同时显示的滑块数量，nvue、支付宝小程序不支持（默认 1 ）\n\t * @property {String}\t\t\teasingFunction\t\t\t指定swiper切换缓动动画类型， 只对微信小程序有效（默认 'default' ）\n\t * @property {String}\t\t\tkeyName\t\t\t\t\tlist数组中指定对象的目标属性名（默认 'url' ）\n\t * @property {String}\t\t\timgMode\t\t\t\t\t图片的裁剪模式（默认 'aspectFill' ）\n\t * @property {String | Number}\theight\t\t\t\t\t组件高度（默认 130 ）\n\t * @property {String}\t\t\tbgColor\t\t\t\t\t背景颜色（默认 \t'#f3f4f6' ）\n\t * @property {String | Number}\tradius\t\t\t\t\t组件圆角，数值或带单位的字符串（默认 4 ）\n\t * @property {Boolean}\t\t\tloading\t\t\t\t\t是否加载中（默认 false ）\n\t * @property {Boolean}\t\t\tshowTitle\t\t\t\t是否显示标题，要求数组对象中有title属性（默认 false ）\n\t * @event {Function(index)}\tclick\t点击轮播图时触发\tindex：点击了第几张图片，从0开始\n\t * @event {Function(index)}\tchange\t轮播图切换时触发(自动或者手动切换)\tindex：切换到了第几张图片，从0开始\n\t * @example\t<u-swiper :list=\"list4\" keyName=\"url\" :autoplay=\"false\"></u-swiper>\n\t */\n\texport default {\n\t\tname: 'u-swiper',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentIndex: 0\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcurrent(val, preVal) {\n\t\t\t\tif(val === preVal) return;\n\t\t\t\tthis.currentIndex = val; // 和上游数据关联上\n\t\t\t}\n\t\t},\n\t\temits: [\"click\", \"change\", \"update:current\"],\n\t\tcomputed: {\n\t\t\titemStyle() {\n\t\t\t\treturn index => {\n\t\t\t\t\tconst style = {}\n\t\t\t\t\t// #ifndef APP-NVUE || MP-TOUTIAO\n\t\t\t\t\t// 左右流出空间的写法不支持nvue和头条\n\t\t\t\t\t// 只有配置了此二值，才加上对应的圆角，以及缩放\n\t\t\t\t\tif (this.nextMargin && this.previousMargin) {\n\t\t\t\t\t\tstyle.borderRadius = addUnit(this.radius)\n\t\t\t\t\t\tif (index !== this.currentIndex) style.transform = 'scale(0.92)'\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\ttestObject: test.object,\n\t\t\ttestImage: test.image,\n\t\t\tgetItemType(item) {\n\t\t\t\tif (typeof item === 'string') return test.video(this.getSource(item)) ? 'video' : 'image'\n\t\t\t\tif (typeof item === 'object' && this.keyName) {\n\t\t\t\tif (!item.type) return test.video(this.getSource(item)) ? 'video' : 'image'\n\t\t\t\tif (item.type === 'image') return 'image'\n\t\t\t\tif (item.type === 'video') return 'video'\n\t\t\t\treturn 'image'\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 获取目标路径，可能数组中为字符串，对象的形式，额外可指定对象的目标属性名keyName\n\t\t\tgetSource(item) {\n\t\t\t\tif (typeof item === 'string') return item\n\t\t\t\tif (typeof item === 'object' && this.keyName) return item[this.keyName]\n\t\t\t\telse error('请按格式传递列表参数')\n\t\t\t\treturn ''\n\t\t\t},\n\t\t\t// 轮播切换事件\n\t\t\tchange(e) {\n\t\t\t\t// 当前的激活索引\n\t\t\t\tconst {\n\t\t\t\t\tcurrent\n\t\t\t\t} = e.detail\n\t\t\t\tthis.pauseVideo(this.currentIndex)\n\t\t\t\tthis.currentIndex = current\n\t\t\t\tthis.$emit('update:current', this.currentIndex)\n\t\t\t\tthis.$emit('change', e.detail)\n\t\t\t},\n\t\t\t// 切换轮播时，暂停视频播放\n\t\t\tpauseVideo(index) {\n\t\t\t\tconst lastItem = this.getSource(this.list[index])\n\t\t\t\tif (test.video(lastItem)) {\n\t\t\t\t\t// 当视频隐藏时，暂停播放\n\t\t\t\t\tconst video = uni.createVideoContext(`video-${index}`, this)\n\t\t\t\t\tvideo.pause()\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 当一个轮播item为视频时，获取它的视频海报\n\t\t\tgetPoster(item) {\n\t\t\t\treturn typeof item === 'object' && item.poster ? item.poster : ''\n\t\t\t},\n\t\t\t// 点击某个item\n\t\t\tclickHandler(index) {\n\t\t\t\tthis.$emit('click', index)\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t\n\t.u-swiper__wrapper {\n\t\tflex: 1;\n\t}\n\t.u-swiper {\n\t\t@include flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&__wrapper {\n\t\t\tflex: 1;\n\n\t\t\t&__item {\n\t\t\t\tflex: 1;\n\n\t\t\t\t&__wrapper {\n\t\t\t\t\t@include flex;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\ttransition: transform 0.3s;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t&__image {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__video {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__title {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.3);\n\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tpadding: 12rpx 24rpx;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__indicator {\n\t\t\tposition: absolute;\n\t\t\tbottom: 10px;\n\t\t}\n\t}\n</style>\n", "import Component from 'F:/parking/park-uniapp/node_modules/uview-plus/components/u-swiper/u-swiper.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "addStyle", "test", "error", "uni"], "mappings": ";;AAmIC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,cAAAA,SAASC,cAAK,OAAEC,mBAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAQ,KAAK,QAAQ;AACpB,UAAG,QAAQ;AAAQ;AACnB,WAAK,eAAe;AAAA,IACrB;AAAA,EACA;AAAA,EACD,OAAO,CAAC,SAAS,UAAU,gBAAgB;AAAA,EAC3C,UAAU;AAAA,IACT,YAAY;AACX,aAAO,WAAS;AACf,cAAM,QAAQ,CAAC;AAIf,YAAI,KAAK,cAAc,KAAK,gBAAgB;AAC3C,gBAAM,eAAeC,sBAAQ,KAAK,MAAM;AACxC,cAAI,UAAU,KAAK;AAAc,kBAAM,YAAY;AAAA,QACpD;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,cAAQ;AAAA,IACR,SAAAD,cAAO;AAAA,IACP,YAAYE,cAAI,KAAC;AAAA,IACjB,WAAWA,cAAI,KAAC;AAAA,IAChB,YAAY,MAAM;AACjB,UAAI,OAAO,SAAS;AAAU,eAAOA,mBAAK,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU;AAClF,UAAI,OAAO,SAAS,YAAY,KAAK,SAAS;AAC9C,YAAI,CAAC,KAAK;AAAM,iBAAOA,mBAAK,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU;AACpE,YAAI,KAAK,SAAS;AAAS,iBAAO;AAClC,YAAI,KAAK,SAAS;AAAS,iBAAO;AAClC,eAAO;AAAA,MACP;AAAA,IACA;AAAA;AAAA,IAED,UAAU,MAAM;AACf,UAAI,OAAO,SAAS;AAAU,eAAO;AACrC,UAAI,OAAO,SAAS,YAAY,KAAK;AAAS,eAAO,KAAK,KAAK,OAAO;AAAA;AACjEC,sBAAAA,MAAM,YAAY;AACvB,aAAO;AAAA,IACP;AAAA;AAAA,IAED,OAAO,GAAG;AAET,YAAM;AAAA,QACL;AAAA,MACD,IAAI,EAAE;AACN,WAAK,WAAW,KAAK,YAAY;AACjC,WAAK,eAAe;AACpB,WAAK,MAAM,kBAAkB,KAAK,YAAY;AAC9C,WAAK,MAAM,UAAU,EAAE,MAAM;AAAA,IAC7B;AAAA;AAAA,IAED,WAAW,OAAO;AACjB,YAAM,WAAW,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC;AAChD,UAAID,cAAI,KAAC,MAAM,QAAQ,GAAG;AAEzB,cAAM,QAAQE,cAAAA,MAAI,mBAAmB,SAAS,KAAK,IAAI,IAAI;AAC3D,cAAM,MAAM;AAAA,MACb;AAAA,IACA;AAAA;AAAA,IAED,UAAU,MAAM;AACf,aAAO,OAAO,SAAS,YAAY,KAAK,SAAS,KAAK,SAAS;AAAA,IAC/D;AAAA;AAAA,IAED,aAAa,OAAO;AACnB,WAAK,MAAM,SAAS,KAAK;AAAA,IAC1B;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnND,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}