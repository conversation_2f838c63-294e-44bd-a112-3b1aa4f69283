{"version": 3, "file": "u-toolbar.js", "sources": ["node_modules/uview-plus/components/u-toolbar/u-toolbar.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9ub2RlX21vZHVsZXMvdXZpZXctcGx1cy9jb21wb25lbnRzL3UtdG9vbGJhci91LXRvb2xiYXIudnVl"], "sourcesContent": ["<template>\n\t<view\n\t\tclass=\"u-toolbar\"\n\t\************************=\"noop\"\n\t\tv-if=\"show\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-toolbar__left\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-toolbar__cancel__wrapper\"\n\t\t\t\thover-class=\"u-hover-class\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-toolbar__wrapper__cancel\"\n\t\t\t\t\t@tap=\"cancel\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\t\tcolor: cancelColor\n\t\t\t\t\t}\"\n\t\t\t\t>{{ cancelText }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<text\n\t\t\tclass=\"u-toolbar__title u-line-1\"\n\t\t\tv-if=\"title\"\n\t\t>{{ title }}</text>\n\t\t<view\n\t\t\tclass=\"u-toolbar__right\"\n\t\t>\n\t\t\t<view\n\t\t\t\tv-if=\"!rightSlot\"\n\t\t\t\tclass=\"u-toolbar__confirm__wrapper\"\n\t\t\t\thover-class=\"u-hover-class\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-toolbar__wrapper__confirm\"\n\t\t\t\t\t@tap=\"confirm\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\tcolor: confirmColor\n\t\t\t\t}\"\n\t\t\t\t>{{ confirmText }}</text>\n\t\t\t</view>\n\t\t\t<template v-else>\n\t\t\t\t<slot name=\"right\">\n\t\t\t\t</slot>\n\t\t\t</template>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\t/**\n\t * Toolbar 工具条\n\t * @description \n\t * @tutorial https://ijry.github.io/uview-plus/components/toolbar.html\n\t * @property {Boolean}\tshow\t\t\t是否展示工具条（默认 true ）\n\t * @property {String}\tcancelText\t\t取消按钮的文字（默认 '取消' ）\n\t * @property {String}\tconfirmText\t\t确认按钮的文字（默认 '确认' ）\n\t * @property {String}\tcancelColor\t\t取消按钮的颜色（默认 '#909193' ）\n\t * @property {String}\tconfirmColor\t确认按钮的颜色（默认 '#3c9cff' ）\n\t * @property {String}\ttitle\t\t\t标题文字\n\t * @event {Function} \n\t * @example \n\t */\n\texport default {\n\t\tname: 'u-toolbar',\n\t\tmixins: [mpMixin, mixin, props],\n\t\temits: [\"confirm\", \"cancel\"],\n\t\tcreated() {\n\t\t\t// console.log(this.$slots)\n\t\t},\n\t\tmethods: {\n\t\t\t// 点击取消按钮\n\t\t\tcancel() {\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\t\t\t// 点击确定按钮\n\t\t\tconfirm() {\n\t\t\t\tthis.$emit('confirm')\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-toolbar {\n\t\theight: 42px;\n\t\t@include flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\n\t\t&__wrapper {\n\t\t\t&__cancel {\n\t\t\t\tcolor: $u-tips-color;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tpadding: 0 15px;\n\t\t\t}\n\t\t}\n\n\t\t&__title {\n\t\t\tcolor: $u-main-color;\n\t\t\tpadding: 0 60rpx;\n\t\t\tfont-size: 16px;\n\t\t\tfont-weight: bold;\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t&__wrapper {\n\t\t\t&__left,\n\t\t\t&__right {\n\t\t\t\t@include flex;\n\t\t\t}\n\t\t\t&__confirm {\n\t\t\t\tcolor: $u-primary;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tpadding: 0 15px;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'F:/parking/park-uniapp/node_modules/uview-plus/components/u-toolbar/u-toolbar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props"], "mappings": ";;AAmEC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,cAAAA,SAASC,cAAK,OAAEC,sBAAK;AAAA,EAC9B,OAAO,CAAC,WAAW,QAAQ;AAAA,EAC3B,UAAU;AAAA,EAET;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAS;AACR,WAAK,MAAM,QAAQ;AAAA,IACnB;AAAA;AAAA,IAED,UAAU;AACT,WAAK,MAAM,SAAS;AAAA,IACrB;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;;ACnFD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}