{"version": 3, "file": "user-aggrement.js", "sources": ["pages/aggrement/user-aggrement.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWdncmVtZW50L3VzZXItYWdncmVtZW50LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"agreement-container\">\r\n    <!-- 内容区域 -->\r\n    <view class=\"content-wrapper\">\r\n      <view v-if=\"loading\" class=\"loading-container\">\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n      \r\n      <view v-else-if=\"error\" class=\"error-container\">\r\n        <text class=\"error-text\">{{ error }}</text>\r\n        <button class=\"retry-btn\" @click=\"loadAgreement\">重新加载</button>\r\n      </view>\r\n      \r\n      <view v-else class=\"agreement-content\">\r\n        <view class=\"agreement-body\">\r\n          <rich-text :nodes=\"agreementData.agreementContent || '暂无内容'\"></rich-text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport { getAgreementByType } from '@/api/agreement'\r\n\r\nconst loading = ref(true)\r\nconst error = ref('')\r\nconst agreementData = ref({})\r\n\r\n// 加载用户协议\r\nconst loadAgreement = async () => {\r\n  try {\r\n    loading.value = true\r\n    error.value = ''\r\n    \r\n    const response = await getAgreementByType(1) \r\n    \r\n    if (response.code === 200) {\r\n      agreementData.value = response.data || {}\r\n    } else {\r\n      error.value = response.msg || '获取协议失败'\r\n    }\r\n  } catch (err) {\r\n    console.error('加载用户协议失败:', err)\r\n    error.value = '网络错误，请稍后重试'\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nonLoad(() => {\r\n  loadAgreement()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.agreement-container {\r\n  min-height: 100vh;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 32rpx;\r\n}\r\n\r\n.loading-container,\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 0;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.error-text {\r\n  font-size: 28rpx;\r\n  color: #f56c6c;\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.retry-btn {\r\n  padding: 16rpx 32rpx;\r\n  background-color: #409eff;\r\n  color: #fff;\r\n  border-radius: 8rpx;\r\n  font-size: 28rpx;\r\n  border: none;\r\n}\r\n\r\n.agreement-content {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 32rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.agreement-body {\r\n  line-height: 1.8;\r\n  color: #666;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* rich-text 内容样式 */\r\n.agreement-body :deep(p) {\r\n  margin: 16rpx 0;\r\n  line-height: 1.8;\r\n}\r\n\r\n.agreement-body :deep(strong) {\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.agreement-body :deep(h1),\r\n.agreement-body :deep(h2),\r\n.agreement-body :deep(h3) {\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin: 32rpx 0 16rpx 0;\r\n}\r\n\r\n.agreement-body :deep(h1) {\r\n  font-size: 36rpx;\r\n}\r\n\r\n.agreement-body :deep(h2) {\r\n  font-size: 32rpx;\r\n}\r\n\r\n.agreement-body :deep(h3) {\r\n  font-size: 30rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/aggrement/user-aggrement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "getAgreementByType", "uni", "onLoad"], "mappings": ";;;;;;AA2BA,UAAM,UAAUA,cAAG,IAAC,IAAI;AACxB,UAAM,QAAQA,cAAG,IAAC,EAAE;AACpB,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5B,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACF,gBAAQ,QAAQ;AAChB,cAAM,QAAQ;AAEd,cAAM,WAAW,MAAMC,cAAkB,mBAAC,CAAC;AAE3C,YAAI,SAAS,SAAS,KAAK;AACzB,wBAAc,QAAQ,SAAS,QAAQ,CAAE;AAAA,QAC/C,OAAW;AACL,gBAAM,QAAQ,SAAS,OAAO;AAAA,QAC/B;AAAA,MACF,SAAQ,KAAK;AACZC,sBAAAA,MAAA,MAAA,SAAA,4CAAc,aAAa,GAAG;AAC9B,cAAM,QAAQ;AAAA,MAClB,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAEAC,kBAAAA,OAAO,MAAM;AACX,oBAAe;AAAA,IACjB,CAAC;;;;;;;;;;;;;;;;;ACrDD,GAAG,WAAW,eAAe;"}