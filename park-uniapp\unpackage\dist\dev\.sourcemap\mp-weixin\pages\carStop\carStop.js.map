{"version": 3, "file": "carStop.js", "sources": ["pages/carStop/carStop.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FyU3RvcC9jYXJTdG9wLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"car-stop-container\">\r\n        <view class=\"cell\">\r\n            <view class=\"top-cell u-flex u-flex-y-center\">\r\n                <view class=\"top-cell-title\">\r\n                    <view class=\"title\"> 停车缴费 </view>\r\n                    <view class=\"desc\"> Parking payment </view>\r\n                </view>\r\n                <image src=\"/static/image/plateQuery.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n<script setup>\r\nimport { ref } from \"vue\";\r\nimport { onLoad } from \"@dcloudio/uni-app\";\r\nimport { getOpenid } from \"@/api/login\";\r\nimport { paymentTemporary } from \"@/api/parkingOrder\";\r\n\r\nconst gateNo = ref(null);\r\nconst warehouse = ref(null);\r\nconst openid = ref(null);\r\n\r\nonLoad((options) => {\r\n    console.log('小程序接收到的参数:', options);\r\n\r\n    let stopNoValue = null;\r\n    \r\n    // 方案1：直接传参（原有逻辑）\r\n    if (options.stopNo) {\r\n        stopNoValue = options.stopNo;\r\n        console.log('直接传参模式，stopNo:', stopNoValue);\r\n    }\r\n    // 方案2：扫码跳转（新增逻辑）\r\n    else if (options.q) {\r\n        try {\r\n            // 1. URL解码\r\n            const decodedUrl = decodeURIComponent(options.q);\r\n            console.log('解码后的URL:', decodedUrl);\r\n            \r\n            // 2. 手动解析URL参数（小程序不支持URL构造函数）\r\n            const queryString = decodedUrl.split('?')[1];\r\n            if (queryString) {\r\n                const params = queryString.split('&');\r\n                for (let param of params) {\r\n                    const [key, value] = param.split('=');\r\n                    if (key === 'stopNo') {\r\n                        stopNoValue = value;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            console.log('扫码跳转模式，解析出的stopNo:', stopNoValue);\r\n        } catch (error) {\r\n            console.error('URL解析失败:', error);\r\n            uni.showToast({\r\n                title: 'URL解析失败',\r\n                icon: 'error'\r\n            });\r\n            return;\r\n        }\r\n    }\r\n\r\n    if (stopNoValue) {\r\n        // 解析 stopNo 参数：格式为 \"gateNoValue wId warehouseIdValue\"\r\n        let splitStr = stopNoValue.split(\"wId\");\r\n        if (splitStr.length === 2) {\r\n            gateNo.value = splitStr[0];\r\n            warehouse.value = splitStr[1];\r\n\r\n            console.log('解析成功:', {\r\n                gateNo: gateNo.value,\r\n                warehouse: warehouse.value\r\n            });\r\n\r\n            // 开始支付流程\r\n            createParkingOrder();\r\n        } else {\r\n            console.error('stopNo参数格式错误:', stopNoValue);\r\n            uni.showToast({\r\n                title: '参数格式错误',\r\n                icon: 'error'\r\n            });\r\n        }\r\n    } else {\r\n        console.error('缺少stopNo参数');\r\n        uni.showToast({\r\n            title: '缺少必要参数',\r\n            icon: 'error'\r\n        });\r\n    }\r\n});\r\n\r\nconst createParkingOrder = () => {\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: '正在获取支付信息...',\r\n        mask: true\r\n    });\r\n\r\n    uni.login({\r\n        provider: 'weixin',\r\n        success: (res) => {\r\n            getOpenid({ wxCode: res.code }).then(openidRes => {\r\n                openid.value = openidRes.data;\r\n\r\n                if (!openid.value) {\r\n                    uni.hideLoading();\r\n                    uni.showToast({\r\n                        title: '获取用户信息失败',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n\r\n                // 调用支付接口\r\n                initiatePayment();\r\n            }).catch(error => {\r\n                console.error('获取openid失败:', error);\r\n                uni.hideLoading();\r\n                uni.showToast({\r\n                    title: '获取用户信息失败',\r\n                    icon: 'error'\r\n                });\r\n            });\r\n        },\r\n        fail: (error) => {\r\n            console.error('微信登录失败:', error);\r\n            uni.hideLoading();\r\n            uni.showToast({\r\n                title: '微信登录失败',\r\n                icon: 'error'\r\n            });\r\n        }\r\n    });\r\n};\r\n\r\n// 发起支付\r\nconst initiatePayment = () => {\r\n    const params = {\r\n        openId: openid.value,\r\n        gateNo: gateNo.value,\r\n        warehouseId: warehouse.value\r\n    };\r\n\r\n    console.log('支付参数:', params);\r\n\r\n    paymentTemporary(params).then(res => {\r\n        uni.hideLoading();\r\n\r\n        if (!res.data) {\r\n            uni.showToast({\r\n                title: '获取支付信息失败',\r\n                icon: 'error'\r\n            });\r\n            return;\r\n        }\r\n\r\n        // 发起微信支付\r\n        uni.requestPayment({\r\n            timeStamp: res.data.timeStamp,\r\n            nonceStr: res.data.nonceStr,\r\n            package: res.data.package,\r\n            signType: res.data.signType,\r\n            paySign: res.data.paySign,\r\n            success: function (result) {\r\n                console.log('支付成功:', result);\r\n                uni.showToast({\r\n                    title: '支付成功',\r\n                    icon: 'success',\r\n                    duration: 2000\r\n                    // complete: function () {\r\n                    //     setTimeout(() => {\r\n                    //         uni.navigateBack();\r\n                    //     }, 1000);\r\n                    // }\r\n                });\r\n\r\n                // 支付成功回调（如果有的话）\r\n                // if (res.data.tradeId) {\r\n                    // payParkingOrderCallBack({tradeId: res.data.tradeId}).then(item => {\r\n                    //     console.log('停车订单支付成功回调:', item);\r\n                    // });\r\n                // }\r\n            },\r\n            fail: function (err) {\r\n                console.error('支付失败:', err);\r\n                uni.showToast({\r\n                    title: '支付失败',\r\n                    icon: 'error'\r\n                });\r\n            }\r\n        });\r\n    }).catch(error => {\r\n        console.error('获取支付信息失败:', error);\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n            title: '获取支付信息失败',\r\n            icon: 'error'\r\n        });\r\n    });\r\n};\r\n\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.car-stop-container {\r\n    background-color: #f5f5f5;\r\n    height: 100vh;\r\n}\r\n\r\n.cell {\r\n    padding: 40rpx 32rpx 0;\r\n\r\n    .top-cell {\r\n        margin-bottom: 20rpx;\r\n\r\n        .top-cell-title {\r\n            margin-right: 8rpx;\r\n\r\n            .title {\r\n                font-size: 40rpx;\r\n                font-weight: bold;\r\n                color: #212121;\r\n                margin-bottom: 8rpx;\r\n            }\r\n\r\n            .desc {\r\n                font-size: 28rpx;\r\n                font-weight: 400;\r\n                color: #9e9e9e;\r\n            }\r\n        }\r\n\r\n        image {\r\n            width: 284rpx;\r\n            height: 200rpx;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/carStop/carStop.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getOpenid", "paymentTemporary"], "mappings": ";;;;;;;;AAmBA,UAAM,SAASA,cAAAA,IAAI,IAAI;AACvB,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,SAASA,cAAAA,IAAI,IAAI;AAEvBC,kBAAM,OAAC,CAAC,YAAY;AAChBC,oBAAY,MAAA,MAAA,OAAA,mCAAA,cAAc,OAAO;AAEjC,UAAI,cAAc;AAGlB,UAAI,QAAQ,QAAQ;AAChB,sBAAc,QAAQ;AACtBA,sBAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,WAAW;AAAA,MAC5C,WAEQ,QAAQ,GAAG;AAChB,YAAI;AAEA,gBAAM,aAAa,mBAAmB,QAAQ,CAAC;AAC/CA,wBAAY,MAAA,MAAA,OAAA,mCAAA,YAAY,UAAU;AAGlC,gBAAM,cAAc,WAAW,MAAM,GAAG,EAAE,CAAC;AAC3C,cAAI,aAAa;AACb,kBAAM,SAAS,YAAY,MAAM,GAAG;AACpC,qBAAS,SAAS,QAAQ;AACtB,oBAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,kBAAI,QAAQ,UAAU;AAClB,8BAAc;AACd;AAAA,cACH;AAAA,YACJ;AAAA,UACJ;AACDA,wBAAY,MAAA,MAAA,OAAA,mCAAA,sBAAsB,WAAW;AAAA,QAChD,SAAQ,OAAO;AACZA,wBAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AACD;AAAA,QACH;AAAA,MACJ;AAED,UAAI,aAAa;AAEb,YAAI,WAAW,YAAY,MAAM,KAAK;AACtC,YAAI,SAAS,WAAW,GAAG;AACvB,iBAAO,QAAQ,SAAS,CAAC;AACzB,oBAAU,QAAQ,SAAS,CAAC;AAE5BA,wBAAAA,sDAAY,SAAS;AAAA,YACjB,QAAQ,OAAO;AAAA,YACf,WAAW,UAAU;AAAA,UACrC,CAAa;AAGD;QACZ,OAAe;AACHA,wBAAA,MAAA,MAAA,SAAA,mCAAc,iBAAiB,WAAW;AAC1CA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AAAA,QACJ;AAAA,MACT,OAAW;AACHA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,YAAY;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL,CAAC;AAED,UAAM,qBAAqB,MAAM;AAE7BA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AAEDA,oBAAAA,MAAI,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACdC,oBAAS,UAAC,EAAE,QAAQ,IAAI,KAAI,CAAE,EAAE,KAAK,eAAa;AAC9C,mBAAO,QAAQ,UAAU;AAEzB,gBAAI,CAAC,OAAO,OAAO;AACfD,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAC9B,CAAqB;AACD;AAAA,YACH;AAGD;UAChB,CAAa,EAAE,MAAM,WAAS;AACdA,0BAAA,MAAA,MAAA,SAAA,oCAAc,eAAe,KAAK;AAClCA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,YAC1B,CAAiB;AAAA,UACjB,CAAa;AAAA,QACJ;AAAA,QACD,MAAM,CAAC,UAAU;AACbA,wBAAA,MAAA,MAAA,SAAA,oCAAc,WAAW,KAAK;AAC9BA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,SAAS;AAAA,QACX,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO;AAAA,QACf,aAAa,UAAU;AAAA,MAC/B;AAEIA,oBAAA,MAAA,MAAA,OAAA,oCAAY,SAAS,MAAM;AAE3BE,uBAAAA,iBAAiB,MAAM,EAAE,KAAK,SAAO;AACjCF,sBAAG,MAAC,YAAW;AAEf,YAAI,CAAC,IAAI,MAAM;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AACD;AAAA,QACH;AAGDA,sBAAAA,MAAI,eAAe;AAAA,UACf,WAAW,IAAI,KAAK;AAAA,UACpB,UAAU,IAAI,KAAK;AAAA,UACnB,SAAS,IAAI,KAAK;AAAA,UAClB,UAAU,IAAI,KAAK;AAAA,UACnB,SAAS,IAAI,KAAK;AAAA,UAClB,SAAS,SAAU,QAAQ;AACvBA,0BAAA,MAAA,MAAA,OAAA,oCAAY,SAAS,MAAM;AAC3BA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAM9B,CAAiB;AAAA,UAQJ;AAAA,UACD,MAAM,SAAU,KAAK;AACjBA,0BAAc,MAAA,MAAA,SAAA,oCAAA,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,YAC1B,CAAiB;AAAA,UACJ;AAAA,QACb,CAAS;AAAA,MACT,CAAK,EAAE,MAAM,WAAS;AACdA,sBAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAChCA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;ACxMA,GAAG,WAAW,eAAe;"}