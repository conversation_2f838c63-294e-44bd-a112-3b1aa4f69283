{"version": 3, "file": "home.js", "sources": ["pages/home/<USER>", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9ob21lLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"home-container\">\r\n\r\n    <!-- 地图容器 -->\r\n    <view class=\"map-container\">\r\n      <map id=\"myMap\" :style=\"{ width: '100%', height: '85%' }\" :latitude=\"center.latitude\"\r\n        :longitude=\"center.longitude\" :scale=\"scale\" :markers=\"markers\" show-location=\"true\"></map>\r\n\r\n      <!-- 场库类型切换按钮 -->\r\n      <view class=\"map-controls\" :style=\"controlsStyles\">\r\n        <view class=\"control-button\" :class=\"{ active: currentType === 'parking' }\" @click=\"switchToParking\">\r\n          <view class=\"button-text-container\">\r\n            <text class=\"button-text\">停车</text>\r\n            <text class=\"button-text\">场库</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"control-button\" :class=\"{ active: currentType === 'charging' }\" @click=\"switchToCharging\">\r\n          <view class=\"button-text-container\">\r\n            <text class=\"button-text\">充电</text>\r\n            <text class=\"button-text\">场库</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 回到我的位置按钮 -->\r\n      <view class=\"location-control\" :style=\"controlsStyles\" @click=\"backToMyLocation\">\r\n        <image src=\"/static/image/toMine.png\" mode=\"widthFix\" style=\"width: 38rpx; height: 38rpx;\"></image>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 浮动遮罩层 -->\r\n    <view class=\"floating-overlay\" :class=\"overlayClasses\" :style=\"overlayStyles\">\r\n\r\n      <!-- 拖拽手柄区域 -->\r\n      <view class=\"drag-handle-container\" @touchstart=\"handleTouchStart\" @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\" @touchcancel=\"handleTouchEnd\">\r\n        <view class=\"drag-handle-bar\"></view>\r\n      </view>\r\n\r\n      <!-- 功能按钮区域 -->\r\n      <view class=\"buttons-container\">\r\n        <view class=\"action-button\" @tap=\"handleParkingPayment\">\r\n          <image src=\"/static/image/parkPay.png\" mode=\"widthFix\" style=\"width: 100rpx; height: 100rpx;\"></image>\r\n          <text class=\"button-text\">停车缴费</text>\r\n        </view>\r\n        <view class=\"action-button\" @tap=\"handleChargingCode\">\r\n          <image src=\"/static/image/scanCharge​.png\" mode=\"widthFix\" style=\"width: 100rpx; height: 100rpx;\"></image>\r\n          <text class=\"button-text\">扫码充电</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <view class=\"overlay-content\" :class=\"[isMinimized ? 'content-hidden' : '']\">\r\n        <!-- 轮播图区域 -->\r\n        <view class=\"banner-section\" :class=\"[isMinimized ? 'content-hidden' : '']\">\r\n          <up-swiper :list=\"advertConfigList\" :autoplay=\"true\" :circular=\"true\" :interval=\"3000\" :duration=\"500\"\r\n            height=\"140\"></up-swiper>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <custom-tab-bar></custom-tab-bar>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport CustomTabBar from \"@/components/custom-tab-bar/index.vue\";\r\nimport { ref, computed } from \"vue\";\r\nimport { onShow, onReady } from \"@dcloudio/uni-app\";\r\nimport { AMapWX } from \"@/utils/amap.js\";\r\nimport { getParkWareHouseList } from \"@/api/warehouse\";\r\nimport { getAdvertConfigList } from \"@/api/advertConifg\";\r\n\r\n\r\nonShow(() => {\r\n  initMap();\r\n  initAdvertData();\r\n  initWarehouseData();\r\n});\r\n\r\nonReady(() => {\r\n  // 初始化浮动面板位置\r\n  initializePanelPositions();\r\n});\r\n\r\n// 广告数据\r\nconst advertConfigList = ref([]);\r\n\r\n// 场库类型管理\r\nconst currentType = ref('parking'); \r\n\r\n// 地图配置\r\nconst amap = ref(null);\r\nconst scale = ref(16);\r\nconst center = ref({ latitude: 30.88, longitude: 121.81 });\r\nconst markers = ref([]);\r\n\r\n// 停车场库数据\r\nconst wareHouseList = ref([]);\r\n\r\n// 充电场库示例数据\r\nconst chargingStations = ref([\r\n  {\r\n    id: \"charging_001\",\r\n    name: \"奕家江山路A区\",\r\n    latitude: 30.87,\r\n    longitude: 121.81,\r\n    type: 'charging'\r\n  }\r\n]);\r\n\r\n// 初始化广告数据\r\nconst initAdvertData = async () => {\r\n  try {\r\n    const res = await getAdvertConfigList();\r\n    advertConfigList.value = res.data.map(item => ({\r\n      url: item.picUrl\r\n    }));\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: '广告数据加载失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 初始化场库数据\r\nconst initWarehouseData = async () => {\r\n  try {\r\n    const res = await getParkWareHouseList();\r\n    wareHouseList.value = res.data.map(item => ({\r\n      id: item.id,\r\n      name: item.warehouseName,\r\n      latitude: item.latitude,\r\n      longitude: item.longitude\r\n    }));\r\n\r\n    // 更新地图标记，显示所有场库\r\n    updateMarkers();\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: '仓库数据加载失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 切换到停车场库\r\nconst switchToParking = () => {\r\n  currentType.value = 'parking';\r\n  updateMarkers();\r\n};\r\n\r\n// 切换到充电场库\r\nconst switchToCharging = () => {\r\n  currentType.value = 'charging';\r\n  updateMarkers();\r\n};\r\n\r\n// 初始化地图\r\nconst initMap = () => {\r\n  try {\r\n    amap.value = new AMapWX({\r\n      key: \"f361a38bbacd22fba61e2d661f977e95\",\r\n      hideCopyright: true,\r\n    });\r\n    checkLocationPermission();\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: '地图初始化失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 检查定位权限\r\nconst checkLocationPermission = async () => {\r\n  try {\r\n    const res = await uni.getSetting({});\r\n    if (!res.authSetting[\"scope.userLocation\"]) {\r\n      // 直接请求定位权限，系统会弹出权限请求弹窗\r\n      uni.authorize({\r\n        scope: \"scope.userLocation\",\r\n        success: () => getCurrentLocation(),\r\n        fail: () => {\r\n          // 用户拒绝授权后，引导用户去设置页面开启权限\r\n          uni.showModal({\r\n            title: '定位权限未开启',\r\n            content: '请在设置中开启定位权限以获取地图服务',\r\n            confirmText: '去设置',\r\n            success: (modalRes) => {\r\n              if (modalRes.confirm) {\r\n                uni.openSetting({\r\n                  success: (settingRes) => {\r\n                    // 用户从设置页面返回后，重新检查权限\r\n                    if (settingRes.authSetting[\"scope.userLocation\"]) {\r\n                      uni.showToast({\r\n                        title: '正在获取位置...',\r\n                        icon: 'loading',\r\n                        duration: 1500\r\n                      });\r\n                      getCurrentLocation();\r\n                    }\r\n                  }\r\n                });\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      getCurrentLocation();\r\n    }\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: '定位权限检查失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 获取当前位置\r\nconst getCurrentLocation = (showToast = false) => {\r\n  uni.getLocation({\r\n    type: 'gcj02',\r\n    isHighAccuracy: true,\r\n    success: (res) => {\r\n      center.value = {\r\n        latitude: res.latitude,\r\n        longitude: res.longitude\r\n      };\r\n      uni.setStorageSync(\"currentLocation\", center.value);\r\n      updateMarkers();\r\n      \r\n      // 只有主动点击定位按钮时才显示成功提示\r\n      if (showToast) {\r\n        uni.showToast({\r\n          title: '定位成功',\r\n          icon: 'success',\r\n          duration: 1500\r\n        });\r\n      }\r\n    },\r\n    fail: (error) => {\r\n      console.error('获取位置失败:', error);\r\n      uni.showToast({\r\n        title: \"定位失败，请检查定位权限\",\r\n        icon: 'none'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 更新标记\r\nconst updateMarkers = () => {\r\n  const markersList = [];\r\n  \r\n  // 根据当前类型显示对应的场库\r\n  if (currentType.value === 'parking') {\r\n    // 显示停车场库\r\n    wareHouseList.value.forEach((warehouse, index) => {\r\n      if (warehouse.latitude && warehouse.longitude) {\r\n        markersList.push({\r\n          id: index, // 使用索引作为id\r\n          latitude: warehouse.latitude,\r\n          longitude: warehouse.longitude,\r\n          iconPath: \"/static/image/parking-location.png\", // 使用停车场图标\r\n          width: 30,\r\n          height: 30,\r\n          callout: { \r\n            content: warehouse.name, \r\n            display: 'ALWAYS',\r\n            color: '#000000',\r\n            bgColor: '#ffffff',\r\n            fontSize: 14,\r\n            fontWeight: 'bold',\r\n            padding: 5,\r\n            borderRadius: 12\r\n          }\r\n        });\r\n      }\r\n    });\r\n  } else if (currentType.value === 'charging') {\r\n    // 显示充电场库\r\n    chargingStations.value.forEach((station, index) => {\r\n      if (station.latitude && station.longitude) {\r\n        markersList.push({\r\n          id: index, // 使用索引作为id\r\n          latitude: station.latitude,\r\n          longitude: station.longitude,\r\n          iconPath: \"/static/image/charging-location.png\", // 使用充电图标\r\n          width: 30,\r\n          height: 30,\r\n          callout: { \r\n            content: station.name, \r\n            display: 'ALWAYS',\r\n            color: '#000000',\r\n            bgColor: '#ffffff',\r\n            fontSize: 14,\r\n            fontWeight: 'bold',\r\n            padding: 5,\r\n            borderRadius: 12\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n  \r\n  markers.value = markersList;\r\n}\r\n\r\n// -------------------- 浮动面板状态管理 --------------------\r\nconst overlayClasses = computed(() => ({\r\n  'overlay-dragging': isDragging.value\r\n}))\r\n\r\nconst overlayStyles = computed(() => ({\r\n  transform: `translateY(${panelPosition.value}px)`,\r\n  transition: isDragging.value ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n  height: `${panelHeight.value}px`,\r\n  paddingBottom: `${tabBarHeight.value}px` // 内部为底部导航栏留出空间\r\n}))\r\n\r\nconst controlsStyles = computed(() => {\r\n  // 切换按钮的位置：面板高度 + 导航栏高度 + 按钮与面板的间距 - 拖拽偏移\r\n  const controlsBottom = panelHeight.value + tabBarHeight.value + 10 - panelPosition.value;\r\n  \r\n  return {\r\n    bottom: `${controlsBottom}px`\r\n  };\r\n})\r\n\r\nconst panelPosition = ref(0);\r\nconst isDragging = ref(false);\r\nconst startY = ref(0);\r\nconst startPosition = ref(0);\r\nconst isMinimized = ref(false);\r\nconst tabBarHeight = ref(50);\r\n\r\n// 面板位置常量  \r\nconst PANEL_POSITIONS = {\r\n  EXPANDED: 0,     // 完整显示按钮和广告（初始状态）\r\n  MINIMIZED: 0,    // 只显示按钮（下拉状态）\r\n};\r\n\r\n// 响应式高度值\r\nconst screenHeight = ref(0);\r\nconst panelHeight = ref(0);\r\n\r\n// 初始化面板位置\r\nconst initializePanelPositions = () => {\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  screenHeight.value = systemInfo.windowHeight; // 获取实际屏幕高度\r\n  \r\n  // 计算面板高度，调整为更矮的比例（约33%的屏幕高度）\r\n  panelHeight.value = Math.round(screenHeight.value * 0.38);\r\n  \r\n  const advertHeight = 160; // 轮播图高度（rpx转px约65px）\r\n  \r\n  // 立即设置面板位置常量，不等异步回调\r\n  PANEL_POSITIONS.EXPANDED = 0;\r\n  PANEL_POSITIONS.MINIMIZED = advertHeight;\r\n  \r\n  // 设置初始位置为完整显示状态\r\n  panelPosition.value = PANEL_POSITIONS.EXPANDED;\r\n  isMinimized.value = false;\r\n  \r\n  // 获取自定义底边栏的实际高度（异步操作，但不影响拖拽）\r\n  const query = uni.createSelectorQuery();\r\n  query.select('.custom-tab-bar').boundingClientRect(data => {\r\n    if (data) {\r\n      tabBarHeight.value = data.height;\r\n    }\r\n  }).exec();\r\n};\r\n\r\n// 拖拽处理函数\r\nconst handleTouchStart = (e) => {\r\n  if (!e.touches || !e.touches[0]) {\r\n    return;\r\n  }\r\n  \r\n  // 阻止事件冒泡和默认行为\r\n  e.preventDefault();\r\n  e.stopPropagation();\r\n  \r\n  console.log('拖拽开始', {\r\n    EXPANDED: PANEL_POSITIONS.EXPANDED,\r\n    MINIMIZED: PANEL_POSITIONS.MINIMIZED,\r\n    currentPosition: panelPosition.value\r\n  });\r\n  \r\n  isDragging.value = true;\r\n  startY.value = e.touches[0].clientY;\r\n  startPosition.value = panelPosition.value;\r\n};\r\n\r\nconst handleTouchMove = (e) => {\r\n  if (!isDragging.value) {\r\n    return;\r\n  }\r\n\r\n  // 阻止事件冒泡和默认行为\r\n  e.preventDefault();\r\n  e.stopPropagation();\r\n\r\n  // 确保触摸事件存在\r\n  if (!e.touches || !e.touches[0]) {\r\n    return;\r\n  }\r\n\r\n  const currentY = e.touches[0].clientY;\r\n  const deltaY = currentY - startY.value;\r\n  let newPosition = startPosition.value + deltaY;\r\n\r\n  // 限制拖动范围：EXPANDED(0) 到 MINIMIZED\r\n  newPosition = Math.max(\r\n    PANEL_POSITIONS.EXPANDED,  \r\n    Math.min(PANEL_POSITIONS.MINIMIZED, newPosition)\r\n  );\r\n  \r\n  panelPosition.value = newPosition;\r\n};\r\n\r\nconst handleTouchEnd = (e) => {\r\n  // 阻止事件冒泡和默认行为\r\n  e.preventDefault();\r\n  e.stopPropagation();\r\n  \r\n  isDragging.value = false;\r\n\r\n  // 计算当前位置到两个固定位置的距离\r\n  const distanceToExpanded = Math.abs(panelPosition.value - PANEL_POSITIONS.EXPANDED);\r\n  const distanceToMinimized = Math.abs(panelPosition.value - PANEL_POSITIONS.MINIMIZED);\r\n  const totalDistance = Math.abs(PANEL_POSITIONS.MINIMIZED - PANEL_POSITIONS.EXPANDED);\r\n\r\n  // 如果移动距离超过总距离的 1/3，则吸附到对应位置\r\n  if (distanceToExpanded > totalDistance / 3 && distanceToMinimized > totalDistance / 3) {\r\n    // 根据移动方向决定吸附位置\r\n    const isMovingDown = panelPosition.value > startPosition.value;\r\n    panelPosition.value = isMovingDown ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;\r\n    isMinimized.value = isMovingDown;\r\n  } else {\r\n    // 吸附到最近的位置\r\n    const shouldMinimize = distanceToMinimized < distanceToExpanded;\r\n    panelPosition.value = shouldMinimize ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;\r\n    isMinimized.value = shouldMinimize;\r\n  }\r\n};\r\n\r\n// 按钮点击事件\r\nconst handleParkingPayment = () => {\r\n  if (uni.getStorageSync(\"token\")) {\r\n    uni.navigateTo({\r\n      url: \"/pages/payPlateQuery/payPlateQuery\",\r\n    });\r\n  } else {\r\n    uni.showModal({\r\n      title: \"提示\",\r\n      content: \"请先登录\",\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\",\r\n          });\r\n        }\r\n      },\r\n    });\r\n  }\r\n};\r\n\r\nconst handleChargingCode = () => {\r\n  uni.showToast({\r\n    title: '暂未开放',\r\n    icon: 'none',\r\n    duration: 2000\r\n  });\r\n};\r\n\r\n// 回到我的位置按钮点击事件\r\nconst backToMyLocation = async () => {\r\n  try {\r\n    // 先检查权限状态\r\n    const res = await uni.getSetting({});\r\n    if (!res.authSetting[\"scope.userLocation\"]) {\r\n      // 没有权限，重新请求\r\n      checkLocationPermission();\r\n      return;\r\n    }\r\n    \r\n    // 创建地图上下文\r\n    const mapContext = uni.createMapContext('myMap');\r\n\r\n    // 使用moveToLocation回到当前定位点\r\n    mapContext.moveToLocation({\r\n      success: () => {\r\n        uni.showToast({\r\n          title: '已回到当前位置',\r\n          icon: 'success',\r\n          duration: 1500\r\n        });\r\n      },\r\n      fail: (error) => {\r\n        console.error('回到位置失败:', error);\r\n        // 如果moveToLocation失败，则重新获取位置\r\n        uni.showToast({\r\n          title: '正在重新定位...',\r\n          icon: 'loading',\r\n          duration: 1500\r\n        });\r\n        getCurrentLocation(true);\r\n      }\r\n    });\r\n  } catch (error) {\r\n    // 权限检查失败，直接尝试重新定位\r\n    uni.showToast({\r\n      title: '正在重新定位...',\r\n      icon: 'loading',\r\n      duration: 1500\r\n    });\r\n    getCurrentLocation(true);\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.map-container {\r\n  height: 100vh;\r\n}\r\n\r\n// 页面内容\r\n.page-content {\r\n  margin-top: 88rpx;\r\n  padding: 32rpx;\r\n  padding-bottom: 140rpx;\r\n}\r\n\r\n// 浮动遮罩层\r\n.floating-overlay {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #ffffff;\r\n  border-top-left-radius: 32rpx;\r\n  border-top-right-radius: 32rpx;\r\n  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 100;\r\n  will-change: transform;\r\n  transform: translateZ(0);\r\n  touch-action: none;\r\n  -webkit-touch-callout: none;\r\n  -webkit-user-select: none;\r\n  -webkit-overflow-scrolling: touch;\r\n\r\n  &.overlay-dragging {\r\n    transition: none !important;\r\n  }\r\n}\r\n\r\n// 拖拽手柄容器\r\n.drag-handle-container {\r\n  height: 50rpx; // 适中的触摸区域高度\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  touch-action: none;\r\n  -webkit-touch-callout: none;\r\n  -webkit-user-select: none;\r\n  cursor: grab;\r\n  \r\n  &:active {\r\n    cursor: grabbing;\r\n  }\r\n\r\n  .drag-handle-bar {\r\n    width: 60rpx;\r\n    height: 6rpx;\r\n    background: #cfcfcf;\r\n    border-radius: 3rpx;\r\n    pointer-events: none;\r\n  }\r\n}\r\n\r\n// 按钮容器\r\n.buttons-container {\r\n  padding: 5rpx 30rpx 25rpx;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  gap: 24rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n\r\n  .action-button {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 20rpx 16rpx;\r\n    border-radius: 24rpx;\r\n    box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.08);\r\n    border: 1rpx solid #f0f0f0;\r\n\r\n\r\n    .button-text {\r\n      margin-top: 12rpx;\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      color: #000000;\r\n    }\r\n  }\r\n}\r\n\r\n// 主要内容区域\r\n.overlay-content {\r\n  padding: 0 30rpx;\r\n  position: relative;\r\n  z-index: 0;\r\n\r\n  // 轮播图区域样式\r\n  .banner-section {\r\n    border-radius: 24rpx;\r\n    transition: opacity 0.3s ease;\r\n    \r\n    &.content-hidden {\r\n      opacity: 0;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n}\r\n\r\n// 场库类型切换按钮\r\n.map-controls {\r\n  position: absolute;\r\n  left: 20rpx;\r\n  display: flex;\r\n  flex-direction: column; // 竖向排列\r\n  gap: 16rpx;\r\n  z-index: 10;\r\n\r\n  .control-button {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border-radius: 14rpx;\r\n    background-color: #ffffff;\r\n    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &.active {\r\n      background: linear-gradient(135deg, #4BA1FC, #777efe);\r\n      color: #ffffff;\r\n\r\n      .button-text {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    &:active {\r\n      transform: scale(0.95);\r\n    }\r\n\r\n    .button-text-container {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 100%;\r\n      gap: 2rpx;\r\n    }\r\n\r\n    .button-text {\r\n      font-size: 24rpx;\r\n      font-weight: 500;\r\n      color: #333;\r\n      text-align: center;\r\n      line-height: 1.2;\r\n    }\r\n  }\r\n}\r\n\r\n// 回到我的位置按钮\r\n.location-control {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 14rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.2s ease, background-color 0.2s ease;\r\n  z-index: 10;\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n    background-color: #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "names": ["onShow", "onReady", "ref", "getAdvertConfigList", "uni", "getParkWareHouseList", "AMapWX", "computed"], "mappings": ";;;;;;;;;;;;;;AAkEA,MAAM,eAAe,MAAW;;;;AAQhCA,kBAAAA,OAAO,MAAM;AACX;AACA;AACA;IACF,CAAC;AAEDC,kBAAAA,QAAQ,MAAM;AAEZ;IACF,CAAC;AAGD,UAAM,mBAAmBC,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,cAAcA,cAAAA,IAAI,SAAS;AAGjC,UAAM,OAAOA,cAAAA,IAAI,IAAI;AACrB,UAAM,QAAQA,cAAAA,IAAI,EAAE;AACpB,UAAM,SAASA,cAAG,IAAC,EAAE,UAAU,OAAO,WAAW,OAAM,CAAE;AACzD,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAGtB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAG5B,UAAM,mBAAmBA,cAAAA,IAAI;AAAA,MAC3B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiB,YAAY;AACjC,UAAI;AACF,cAAM,MAAM,MAAMC,iBAAAA;AAClB,yBAAiB,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,UAC7C,KAAK,KAAK;AAAA,QACX,EAAC;AAAA,MACH,SAAQ,OAAO;AACdC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,oBAAoB,YAAY;AACpC,UAAI;AACF,cAAM,MAAM,MAAMC,cAAAA;AAClB,sBAAc,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,UAC1C,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,QACjB,EAAC;AAGF;MACD,SAAQ,OAAO;AACdD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,kBAAY,QAAQ;AACpB;IACF;AAGA,UAAM,mBAAmB,MAAM;AAC7B,kBAAY,QAAQ;AACpB;IACF;AAGA,UAAM,UAAU,MAAM;AACpB,UAAI;AACF,aAAK,QAAQ,IAAIE,kBAAO;AAAA,UACtB,KAAK;AAAA,UACL,eAAe;AAAA,QACrB,CAAK;AACD;MACD,SAAQ,OAAO;AACdF,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,0BAA0B,YAAY;AAC1C,UAAI;AACF,cAAM,MAAM,MAAMA,cAAAA,MAAI,WAAW,CAAE,CAAA;AACnC,YAAI,CAAC,IAAI,YAAY,oBAAoB,GAAG;AAE1CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,MAAM,mBAAoB;AAAA,YACnC,MAAM,MAAM;AAEVA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,aAAa;AAAA,gBACb,SAAS,CAAC,aAAa;AACrB,sBAAI,SAAS,SAAS;AACpBA,kCAAAA,MAAI,YAAY;AAAA,sBACd,SAAS,CAAC,eAAe;AAEvB,4BAAI,WAAW,YAAY,oBAAoB,GAAG;AAChDA,wCAAAA,MAAI,UAAU;AAAA,4BACZ,OAAO;AAAA,4BACP,MAAM;AAAA,4BACN,UAAU;AAAA,0BAClC,CAAuB;AACD;wBACD;AAAA,sBACF;AAAA,oBACnB,CAAiB;AAAA,kBACF;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACL;QACD;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,YAAY,UAAU;AAChDA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,gBAAgB;AAAA,QAChB,SAAS,CAAC,QAAQ;AAChB,iBAAO,QAAQ;AAAA,YACb,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA,UACvB;AACMA,wBAAAA,MAAI,eAAe,mBAAmB,OAAO,KAAK;AAClD;AAGA,cAAI,WAAW;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,2EAAc,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,YAAM,cAAc,CAAA;AAGpB,UAAI,YAAY,UAAU,WAAW;AAEnC,sBAAc,MAAM,QAAQ,CAAC,WAAW,UAAU;AAChD,cAAI,UAAU,YAAY,UAAU,WAAW;AAC7C,wBAAY,KAAK;AAAA,cACf,IAAI;AAAA;AAAA,cACJ,UAAU,UAAU;AAAA,cACpB,WAAW,UAAU;AAAA,cACrB,UAAU;AAAA;AAAA,cACV,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,gBACP,SAAS,UAAU;AAAA,gBACnB,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,SAAS;AAAA,gBACT,cAAc;AAAA,cACf;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACL,WAAa,YAAY,UAAU,YAAY;AAE3C,yBAAiB,MAAM,QAAQ,CAAC,SAAS,UAAU;AACjD,cAAI,QAAQ,YAAY,QAAQ,WAAW;AACzC,wBAAY,KAAK;AAAA,cACf,IAAI;AAAA;AAAA,cACJ,UAAU,QAAQ;AAAA,cAClB,WAAW,QAAQ;AAAA,cACnB,UAAU;AAAA;AAAA,cACV,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,gBACP,SAAS,QAAQ;AAAA,gBACjB,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,SAAS;AAAA,gBACT,cAAc;AAAA,cACf;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAED,cAAQ,QAAQ;AAAA,IAClB;AAGA,UAAM,iBAAiBG,cAAQ,SAAC,OAAO;AAAA,MACrC,oBAAoB,WAAW;AAAA,IACjC,EAAE;AAEF,UAAM,gBAAgBA,cAAQ,SAAC,OAAO;AAAA,MACpC,WAAW,cAAc,cAAc,KAAK;AAAA,MAC5C,YAAY,WAAW,QAAQ,SAAS;AAAA,MACxC,QAAQ,GAAG,YAAY,KAAK;AAAA,MAC5B,eAAe,GAAG,aAAa,KAAK;AAAA;AAAA,IACtC,EAAE;AAEF,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AAEpC,YAAM,iBAAiB,YAAY,QAAQ,aAAa,QAAQ,KAAK,cAAc;AAEnF,aAAO;AAAA,QACL,QAAQ,GAAG,cAAc;AAAA,MAC7B;AAAA,IACA,CAAC;AAED,UAAM,gBAAgBL,cAAAA,IAAI,CAAC;AAC3B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,SAASA,cAAAA,IAAI,CAAC;AACpB,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAC3B,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAG3B,UAAM,kBAAkB;AAAA,MACtB,UAAU;AAAA;AAAA,MACV,WAAW;AAAA;AAAA,IACb;AAGA,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,cAAcA,cAAAA,IAAI,CAAC;AAGzB,UAAM,2BAA2B,MAAM;AACrC,YAAM,aAAaE,oBAAI;AACvB,mBAAa,QAAQ,WAAW;AAGhC,kBAAY,QAAQ,KAAK,MAAM,aAAa,QAAQ,IAAI;AAExD,YAAM,eAAe;AAGrB,sBAAgB,WAAW;AAC3B,sBAAgB,YAAY;AAG5B,oBAAc,QAAQ,gBAAgB;AACtC,kBAAY,QAAQ;AAGpB,YAAM,QAAQA,oBAAI;AAClB,YAAM,OAAO,iBAAiB,EAAE,mBAAmB,UAAQ;AACzD,YAAI,MAAM;AACR,uBAAa,QAAQ,KAAK;AAAA,QAC3B;AAAA,MACL,CAAG,EAAE,KAAI;AAAA,IACT;AAGA,UAAM,mBAAmB,CAAC,MAAM;AAC9B,UAAI,CAAC,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;AAC/B;AAAA,MACD;AAGD,QAAE,eAAc;AAChB,QAAE,gBAAe;AAEjBA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,QAAQ;AAAA,QAClB,UAAU,gBAAgB;AAAA,QAC1B,WAAW,gBAAgB;AAAA,QAC3B,iBAAiB,cAAc;AAAA,MACnC,CAAG;AAED,iBAAW,QAAQ;AACnB,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC5B,oBAAc,QAAQ,cAAc;AAAA,IACtC;AAEA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,UAAI,CAAC,WAAW,OAAO;AACrB;AAAA,MACD;AAGD,QAAE,eAAc;AAChB,QAAE,gBAAe;AAGjB,UAAI,CAAC,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;AAC/B;AAAA,MACD;AAED,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAC9B,YAAM,SAAS,WAAW,OAAO;AACjC,UAAI,cAAc,cAAc,QAAQ;AAGxC,oBAAc,KAAK;AAAA,QACjB,gBAAgB;AAAA,QAChB,KAAK,IAAI,gBAAgB,WAAW,WAAW;AAAA,MACnD;AAEE,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAE5B,QAAE,eAAc;AAChB,QAAE,gBAAe;AAEjB,iBAAW,QAAQ;AAGnB,YAAM,qBAAqB,KAAK,IAAI,cAAc,QAAQ,gBAAgB,QAAQ;AAClF,YAAM,sBAAsB,KAAK,IAAI,cAAc,QAAQ,gBAAgB,SAAS;AACpF,YAAM,gBAAgB,KAAK,IAAI,gBAAgB,YAAY,gBAAgB,QAAQ;AAGnF,UAAI,qBAAqB,gBAAgB,KAAK,sBAAsB,gBAAgB,GAAG;AAErF,cAAM,eAAe,cAAc,QAAQ,cAAc;AACzD,sBAAc,QAAQ,eAAe,gBAAgB,YAAY,gBAAgB;AACjF,oBAAY,QAAQ;AAAA,MACxB,OAAS;AAEL,cAAM,iBAAiB,sBAAsB;AAC7C,sBAAc,QAAQ,iBAAiB,gBAAgB,YAAY,gBAAgB;AACnF,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AAGA,UAAM,uBAAuB,MAAM;AACjC,UAAIA,cAAG,MAAC,eAAe,OAAO,GAAG;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AAAA,MACL,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK;AAAA,cACjB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,YAAY;AACnC,UAAI;AAEF,cAAM,MAAM,MAAMA,cAAAA,MAAI,WAAW,CAAE,CAAA;AACnC,YAAI,CAAC,IAAI,YAAY,oBAAoB,GAAG;AAE1C;AACA;AAAA,QACD;AAGD,cAAM,aAAaA,cAAAA,MAAI,iBAAiB,OAAO;AAG/C,mBAAW,eAAe;AAAA,UACxB,SAAS,MAAM;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AAAA,UACF;AAAA,UACD,MAAM,CAAC,UAAU;AACfA,0BAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,KAAK;AAE9BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AACD,+BAAmB,IAAI;AAAA,UACxB;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,OAAO;AAEdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD,2BAAmB,IAAI;AAAA,MACxB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzgBA,GAAG,WAAW,eAAe;"}