{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 头部区域 -->\r\n    <view class=\"header\">\r\n      <image class=\"logo\" src=\"/static/image/logo.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"app-name\">智慧停车充电</text>\r\n      <text class=\"version\">v2.0</text>\r\n    </view>\r\n    \r\n    <!-- 主要内容 -->\r\n    <view class=\"content\">\r\n      <view class=\"maintenance-icon\">🔧</view>\r\n      <view class=\"title\">系统升级中</view>\r\n      <view class=\"subtitle\">我们正在为您带来更好的停车体验</view>\r\n      <view class=\"description\">\r\n        新版本将包含：\r\n        <text class=\"feature\">• 更快的缴费速度</text>\r\n        <text class=\"feature\">• 更简洁的操作界面</text>\r\n        <text class=\"feature\">• 更多的支付方式</text>\r\n        <text class=\"feature\">• 更稳定的系统性能</text>\r\n      </view>\r\n      <view class=\"time-info\">正在全力升级中，即将为您提供更优质的服务</view>\r\n    </view>\r\n    \r\n    <!-- 按钮区域 -->\r\n    <view class=\"button-area\">\r\n      <button class=\"main-btn\" @click=\"handleMainFunction\">停车缴费</button>\r\n      <button class=\"secondary-btn\" @click=\"handleContact\">联系客服</button>\r\n    </view>\r\n    \r\n    <!-- 底部信息 -->\r\n    <view class=\"footer\">\r\n      <text class=\"contact-info\">如有疑问，请联系客服</text>\r\n      <text class=\"company-info\">© 2025 智慧停车系统</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { onLoad, onShow } from '@dcloudio/uni-app'\r\n\r\nonLoad(() => {\r\n  console.log('占位版本加载')\r\n  \r\n  // 延迟显示升级提示\r\n  setTimeout(() => {\r\n    uni.showModal({\r\n      title: '系统升级通知',\r\n      content: '小程序正在进行重大升级，将为您带来全新的停车缴费体验！感谢您的耐心等待。',\r\n      showCancel: false,\r\n      confirmText: '期待新版本',\r\n      success: () => {\r\n        console.log('用户确认升级通知')\r\n      }\r\n    })\r\n  }, 1500)\r\n})\r\n\r\nonShow(() => {\r\n  // 每次显示页面时的处理\r\n  console.log('维护页面显示')\r\n})\r\n\r\n// 主要功能按钮 - 所有功能都显示维护中\r\nconst handleMainFunction = () => {\r\n  uni.showToast({\r\n    title: '功能升级中，敬请期待',\r\n    icon: 'none',\r\n    duration: 2000\r\n  })\r\n}\r\n\r\n\r\n// 联系客服 - 显示开发中\r\nconst handleContact = () => {\r\n  uni.showToast({\r\n    title: '功能开发中，敬请期待',\r\n    icon: 'none',\r\n    duration: 2000\r\n  })\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 40rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 80rpx;\r\n  margin-top: 60rpx;\r\n}\r\n\r\n.logo {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 20rpx;\r\n  background-color: white;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.app-name {\r\n  display: block;\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.version {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  display: inline-block;\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.maintenance-icon {\r\n  font-size: 100rpx;\r\n  margin-bottom: 40rpx;\r\n  animation: rotate 3s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.title {\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 28rpx;\r\n  margin-bottom: 40rpx;\r\n  opacity: 0.9;\r\n  line-height: 1.5;\r\n}\r\n\r\n.description {\r\n  text-align: left;\r\n  margin: 40rpx 0;\r\n  padding: 30rpx;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 20rpx;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.feature {\r\n  display: block;\r\n  margin: 15rpx 0;\r\n  font-size: 26rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.time-info {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n  margin-top: 40rpx;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 20rpx;\r\n  border-radius: 15rpx;\r\n}\r\n\r\n.button-area {\r\n  margin: 60rpx 0 40rpx 0;\r\n}\r\n\r\n.main-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50rpx;\r\n  margin-bottom: 20rpx;\r\n  font-size: 32rpx;\r\n  padding: 20rpx;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.main-btn:active {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  transform: scale(0.98);\r\n}\r\n\r\n\r\n.secondary-btn {\r\n  background: transparent;\r\n  color: white;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.5);\r\n  border-radius: 50rpx;\r\n  font-size: 28rpx;\r\n  padding: 15rpx;\r\n}\r\n\r\n.secondary-btn:active {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  transform: scale(0.98);\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  opacity: 0.7;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.contact-info {\r\n  display: block;\r\n  font-size: 24rpx;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.company-info {\r\n  font-size: 20rpx;\r\n  opacity: 0.6;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["onLoad", "uni", "onShow"], "mappings": ";;;;;;AAyCAA,kBAAAA,OAAO,MAAM;AACXC,oBAAAA,MAAY,MAAA,OAAA,+BAAA,QAAQ;AAGpB,iBAAW,MAAM;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,MAAM;AACbA,0BAAAA,MAAY,MAAA,OAAA,+BAAA,UAAU;AAAA,UACvB;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEXD,oBAAAA,MAAY,MAAA,OAAA,+BAAA,QAAQ;AAAA,IACtB,CAAC;AAGD,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAAA,IACH;AAIA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAAA,IACH;;;;;;;;;;;;AC/EA,GAAG,WAAW,eAAe;"}