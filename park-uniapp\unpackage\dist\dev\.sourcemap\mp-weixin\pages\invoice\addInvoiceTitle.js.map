{"version": 3, "file": "addInvoiceTitle.js", "sources": ["pages/invoice/addInvoiceTitle.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW52b2ljZS9hZGRJbnZvaWNlVGl0bGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"invoice-form-container\">\r\n    <!-- 表单区域 -->\r\n    <view class=\"form-content\">\r\n      <!-- 发票类型 -->\r\n      <view class=\"form-row\">\r\n        <view class=\"form-label\">发票类型</view>\r\n        <view class=\"form-options\">\r\n          <view class=\"option-item\" :class=\"{ active: formData.invoiceType === 0 }\" @tap=\"setInvoiceType(0)\">\r\n            <text class=\"option-text\">普通发票</text>\r\n          </view>\r\n          <view class=\"option-item\" :class=\"{ active: formData.invoiceType === 1 }\" @tap=\"setInvoiceType(1)\">\r\n            <text class=\"option-text\">专用发票</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 抬头类型（仅普通发票显示） -->\r\n      <view class=\"form-row\" v-if=\"formData.invoiceType === 0\">\r\n        <view class=\"form-label\">抬头类型</view>\r\n        <view class=\"form-options\">\r\n          <view class=\"option-item\" :class=\"{ active: formData.titleType === 0 }\" @tap=\"setTitleType(0)\">\r\n            <text class=\"option-text\">个人</text>\r\n          </view>\r\n          <view class=\"option-item\" :class=\"{ active: formData.titleType === 1 }\" @tap=\"setTitleType(1)\">\r\n            <text class=\"option-text\">单位</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 发票抬头 -->\r\n      <view class=\"form-row\">\r\n        <view class=\"form-label\">发票抬头</view>\r\n        <view class=\"form-input\">\r\n          <input v-model=\"formData.invoiceTitleContent\" :placeholder=\"(formData.invoiceType === 1 || formData.titleType === 1) ? '请输入发票抬头' : '请输入发票抬头'\" class=\"input-field\" />\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 单位信息（专用发票或普通发票单位类型时显示） -->\r\n      <template v-if=\"formData.invoiceType === 1 || formData.titleType === 1\">\r\n        <view class=\"form-row\">\r\n          <view class=\"form-label\">单位税号</view>\r\n          <view class=\"form-input\">\r\n            <input v-model=\"formData.unitDutyParagraph\" placeholder=\"请输入单位税号\" class=\"input-field\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"form-row\">\r\n          <view class=\"form-label\">注册地址</view>\r\n          <view class=\"form-input\">\r\n            <input v-model=\"formData.registerAddress\" placeholder=\"请输入注册地址\" class=\"input-field\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"form-row\">\r\n          <view class=\"form-label\">注册电话</view>\r\n          <view class=\"form-input\">\r\n            <input v-model=\"formData.registerPhone\" placeholder=\"请输入注册电话\" class=\"input-field\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"form-row\">\r\n          <view class=\"form-label\">开户银行</view>\r\n          <view class=\"form-input\">\r\n            <input v-model=\"formData.depositBank\" placeholder=\"请输入开户银行\" class=\"input-field\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"form-row\">\r\n          <view class=\"form-label\">银行账户</view>\r\n          <view class=\"form-input\">\r\n            <input v-model=\"formData.bankAccount\" placeholder=\"请输入银行账户\" class=\"input-field\" />\r\n          </view>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 保存按钮 -->\r\n      <view class=\"save-button\" @tap=\"handleSubmit\">\r\n        <text class=\"save-text\">{{ isEdit ? \"保存\" : \"添加\" }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from \"vue\";\r\nimport { onLoad } from \"@dcloudio/uni-app\";\r\nimport { addInvoiceTitle, editInvoiceTitle } from \"@/api/invoice\";\r\n\r\nconst isEdit = ref(false);\r\n\r\n// 表单数据，设置默认值\r\nconst formData = reactive({\r\n  id: null,\r\n  invoiceType: 0,  // 默认普通发票\r\n  titleType: 0,    // 默认个人\r\n  invoiceTitleContent: \"\",\r\n  unitDutyParagraph: \"\",\r\n  registerAddress: \"\",\r\n  registerPhone: \"\",\r\n  depositBank: \"\",\r\n  bankAccount: \"\",\r\n});\r\n\r\n// 临时存储单位信息，避免切换类型时数据丢失\r\nconst tempUnitData = reactive({\r\n  unitDutyParagraph: \"\",\r\n  registerAddress: \"\",\r\n  registerPhone: \"\",\r\n  depositBank: \"\",\r\n  bankAccount: \"\",\r\n});\r\n\r\nonLoad((options) => {\r\n  if (options.isEdit === \"true\") {\r\n    isEdit.value = true;\r\n    if (options.obj) {\r\n      const obj = JSON.parse(decodeURIComponent(options.obj));\r\n      Object.assign(formData, obj);\r\n      // 专用发票自动设置为单位类型\r\n      if (formData.invoiceType === 1) {\r\n        formData.titleType = 1;\r\n      }\r\n      // 编辑时保存单位数据到临时存储\r\n      if (formData.titleType === 1 || formData.invoiceType === 1) {\r\n        Object.assign(tempUnitData, {\r\n          unitDutyParagraph: formData.unitDutyParagraph,\r\n          registerAddress: formData.registerAddress,\r\n          registerPhone: formData.registerPhone,\r\n          depositBank: formData.depositBank,\r\n          bankAccount: formData.bankAccount,\r\n        });\r\n      }\r\n    }\r\n  }\r\n});\r\n\r\n// 设置发票类型\r\nconst setInvoiceType = (type) => {\r\n  // 在切换前保存当前的单位信息\r\n  if (formData.titleType === 1 || formData.invoiceType === 1) {\r\n    Object.assign(tempUnitData, {\r\n      unitDutyParagraph: formData.unitDutyParagraph,\r\n      registerAddress: formData.registerAddress,\r\n      registerPhone: formData.registerPhone,\r\n      depositBank: formData.depositBank,\r\n      bankAccount: formData.bankAccount,\r\n    });\r\n  }\r\n\r\n  formData.invoiceType = type;\r\n  if (type === 1) {\r\n    // 专用发票自动设置为单位类型\r\n    formData.titleType = 1;\r\n    // 恢复单位信息\r\n    Object.assign(formData, tempUnitData);\r\n  } else {\r\n    // 普通发票重置为个人类型，清空单位信息\r\n    formData.titleType = 0;\r\n    formData.unitDutyParagraph = \"\";\r\n    formData.registerAddress = \"\";\r\n    formData.registerPhone = \"\";\r\n    formData.depositBank = \"\";\r\n    formData.bankAccount = \"\";\r\n  }\r\n};\r\n\r\n// 设置抬头类型\r\nconst setTitleType = (type) => {\r\n  // 在切换前保存当前的单位信息\r\n  if (formData.titleType === 1) {\r\n    Object.assign(tempUnitData, {\r\n      unitDutyParagraph: formData.unitDutyParagraph,\r\n      registerAddress: formData.registerAddress,\r\n      registerPhone: formData.registerPhone,\r\n      depositBank: formData.depositBank,\r\n      bankAccount: formData.bankAccount,\r\n    });\r\n  }\r\n\r\n  formData.titleType = type;\r\n  // 切换到个人时清空单位相关信息\r\n  if (type === 0) {\r\n    formData.unitDutyParagraph = \"\";\r\n    formData.registerAddress = \"\";\r\n    formData.registerPhone = \"\";\r\n    formData.depositBank = \"\";\r\n    formData.bankAccount = \"\";\r\n  } else {\r\n    // 切换到单位时恢复之前保存的信息\r\n    Object.assign(formData, tempUnitData);\r\n  }\r\n};\r\n\r\n// 提交表单\r\nconst handleSubmit = async () => {\r\n  // 表单验证\r\n  if (!formData.invoiceTitleContent.trim()) {\r\n    uni.showToast({\r\n      title: (formData.invoiceType === 1 || formData.titleType === 1) ? \"请输入单位名称\" : \"请输入发票抬头\",\r\n      icon: \"none\",\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 专用发票或单位类型需要单位税号\r\n  if ((formData.invoiceType === 1 || formData.titleType === 1) && !formData.unitDutyParagraph.trim()) {\r\n    uni.showToast({\r\n      title: \"请输入单位税号\",\r\n      icon: \"none\",\r\n    });\r\n    return;\r\n  }\r\n\r\n  uni.showLoading({\r\n    title: isEdit.value ? \"保存中...\" : \"添加中...\",\r\n    mask: true\r\n  });\r\n\r\n  try {\r\n    const apiCall = isEdit.value ? editInvoiceTitle : addInvoiceTitle;\r\n    const res = await apiCall(formData);\r\n    \r\n    uni.hideLoading();\r\n    \r\n    if (res.code === 200) {\r\n      uni.showToast({\r\n        title: isEdit.value ? \"保存成功\" : \"添加成功\",\r\n        icon: \"success\",\r\n        duration: 1500\r\n      });\r\n      setTimeout(() => {\r\n        uni.navigateBack();\r\n      }, 1500);\r\n    } else {\r\n      // 显示后端返回的具体错误信息，不返回页面\r\n      uni.showToast({\r\n        title: res.msg || (isEdit.value ? \"保存失败\" : \"添加失败\"),\r\n        icon: \"none\",\r\n        duration: 3000\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"操作失败:\", error);\r\n    uni.hideLoading();\r\n    \r\n    // 显示捕获到的异常信息\r\n    const errorMsg = error.msg || error.message || (isEdit.value ? \"保存失败，请重试\" : \"添加失败，请重试\");\r\n    uni.showToast({\r\n      title: errorMsg,\r\n      icon: \"none\",\r\n      duration: 3000\r\n    });\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.invoice-form-container {\r\n  min-height: 100vh;\r\n  background-color: #f8f9fa;\r\n  padding: 32rpx;\r\n}\r\n\r\n.form-content {\r\n  background: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 48rpx 36rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 32rpx;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .form-label {\r\n    font-size: 30rpx;\r\n    color: #1f2937;\r\n    min-width: 160rpx;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .form-options {\r\n    display: flex;\r\n    gap: 16rpx;\r\n    flex: 1;\r\n    margin-left: 20rpx;\r\n\r\n    .option-item {\r\n      flex: 1;\r\n      height: 60rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: 2rpx solid #e5e7eb;\r\n      border-radius: 12rpx;\r\n      background: #ffffff;\r\n      transition: all 0.3s ease;\r\n\r\n      &.active {\r\n        border-color: #4BA1FC;\r\n        background: #f0f9ff;\r\n        \r\n        .option-text {\r\n          color: #4BA1FC;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      &:active {\r\n        transform: scale(0.98);\r\n      }\r\n\r\n      .option-text {\r\n        font-size: 26rpx;\r\n        color: #6b7280;\r\n        transition: all 0.3s ease;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-input {\r\n    flex: 1;\r\n    margin-left: 20rpx;\r\n\r\n    .input-field {\r\n      width: 100%;\r\n      height: 50rpx;\r\n      padding: 0 8rpx 5rpx 8rpx;\r\n      border: none;\r\n      border-bottom: 2rpx solid #e5e7eb;\r\n      background: transparent;\r\n      font-size: 28rpx;\r\n      color: #1f2937;\r\n      transition: all 0.3s ease;\r\n\r\n      &:focus {\r\n        border-bottom-color: #4BA1FC;\r\n        outline: none;\r\n      }\r\n\r\n      &::placeholder {\r\n        color: #9ca3af;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.save-button {\r\n  margin-top: 40rpx;\r\n  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);\r\n  border-radius: 16rpx;\r\n  padding: 20rpx 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 16rpx rgba(75, 161, 252, 0.25);\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: translateY(1rpx);\r\n    box-shadow: 0 2rpx 8rpx rgba(75, 161, 252, 0.2);\r\n  }\r\n\r\n  .save-text {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #ffffff;\r\n    letter-spacing: 1rpx;\r\n  }\r\n}\r\n\r\n// 美化单位信息部分的过渡效果\r\n.form-row {\r\n  transition: all 0.4s ease;\r\n  \r\n  &[data-v-enter-active],\r\n  &[data-v-leave-active] {\r\n    transition: all 0.4s ease;\r\n  }\r\n  \r\n  &[data-v-enter-from],\r\n  &[data-v-leave-to] {\r\n    opacity: 0;\r\n    transform: translateY(-20rpx);\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/invoice/addInvoiceTitle.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onLoad", "uni", "editInvoiceTitle", "addInvoiceTitle"], "mappings": ";;;;;;AAqFA,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,IAAI;AAAA,MACJ,aAAa;AAAA;AAAA,MACb,WAAW;AAAA;AAAA,MACX,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,eAAeA,cAAAA,SAAS;AAAA,MAC5B,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAClB,UAAI,QAAQ,WAAW,QAAQ;AAC7B,eAAO,QAAQ;AACf,YAAI,QAAQ,KAAK;AACf,gBAAM,MAAM,KAAK,MAAM,mBAAmB,QAAQ,GAAG,CAAC;AACtD,iBAAO,OAAO,UAAU,GAAG;AAE3B,cAAI,SAAS,gBAAgB,GAAG;AAC9B,qBAAS,YAAY;AAAA,UACtB;AAED,cAAI,SAAS,cAAc,KAAK,SAAS,gBAAgB,GAAG;AAC1D,mBAAO,OAAO,cAAc;AAAA,cAC1B,mBAAmB,SAAS;AAAA,cAC5B,iBAAiB,SAAS;AAAA,cAC1B,eAAe,SAAS;AAAA,cACxB,aAAa,SAAS;AAAA,cACtB,aAAa,SAAS;AAAA,YAChC,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiB,CAAC,SAAS;AAE/B,UAAI,SAAS,cAAc,KAAK,SAAS,gBAAgB,GAAG;AAC1D,eAAO,OAAO,cAAc;AAAA,UAC1B,mBAAmB,SAAS;AAAA,UAC5B,iBAAiB,SAAS;AAAA,UAC1B,eAAe,SAAS;AAAA,UACxB,aAAa,SAAS;AAAA,UACtB,aAAa,SAAS;AAAA,QAC5B,CAAK;AAAA,MACF;AAED,eAAS,cAAc;AACvB,UAAI,SAAS,GAAG;AAEd,iBAAS,YAAY;AAErB,eAAO,OAAO,UAAU,YAAY;AAAA,MACxC,OAAS;AAEL,iBAAS,YAAY;AACrB,iBAAS,oBAAoB;AAC7B,iBAAS,kBAAkB;AAC3B,iBAAS,gBAAgB;AACzB,iBAAS,cAAc;AACvB,iBAAS,cAAc;AAAA,MACxB;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,SAAS;AAE7B,UAAI,SAAS,cAAc,GAAG;AAC5B,eAAO,OAAO,cAAc;AAAA,UAC1B,mBAAmB,SAAS;AAAA,UAC5B,iBAAiB,SAAS;AAAA,UAC1B,eAAe,SAAS;AAAA,UACxB,aAAa,SAAS;AAAA,UACtB,aAAa,SAAS;AAAA,QAC5B,CAAK;AAAA,MACF;AAED,eAAS,YAAY;AAErB,UAAI,SAAS,GAAG;AACd,iBAAS,oBAAoB;AAC7B,iBAAS,kBAAkB;AAC3B,iBAAS,gBAAgB;AACzB,iBAAS,cAAc;AACvB,iBAAS,cAAc;AAAA,MAC3B,OAAS;AAEL,eAAO,OAAO,UAAU,YAAY;AAAA,MACrC;AAAA,IACH;AAGA,UAAM,eAAe,YAAY;AAE/B,UAAI,CAAC,SAAS,oBAAoB,QAAQ;AACxCC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAQ,SAAS,gBAAgB,KAAK,SAAS,cAAc,IAAK,YAAY;AAAA,UAC9E,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,WAAK,SAAS,gBAAgB,KAAK,SAAS,cAAc,MAAM,CAAC,SAAS,kBAAkB,KAAI,GAAI;AAClGA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,OAAO,QAAQ,WAAW;AAAA,QACjC,MAAM;AAAA,MACV,CAAG;AAED,UAAI;AACF,cAAM,UAAU,OAAO,QAAQC,YAAAA,mBAAmBC,YAAAA;AAClD,cAAM,MAAM,MAAM,QAAQ,QAAQ;AAElCF,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,SAAS,KAAK;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,QAAQ,SAAS;AAAA,YAC/B,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD,qBAAW,MAAM;AACfA,0BAAG,MAAC,aAAY;AAAA,UACjB,GAAE,IAAI;AAAA,QACb,OAAW;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,QAAQ,OAAO,QAAQ,SAAS;AAAA,YAC3C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,4CAAc,SAAS,KAAK;AAC5BA,sBAAG,MAAC,YAAW;AAGf,cAAM,WAAW,MAAM,OAAO,MAAM,YAAY,OAAO,QAAQ,aAAa;AAC5EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPA,GAAG,WAAW,eAAe;"}