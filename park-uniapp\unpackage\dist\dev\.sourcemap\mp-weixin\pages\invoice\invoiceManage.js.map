{"version": 3, "file": "invoiceManage.js", "sources": ["pages/invoice/invoiceManage.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW52b2ljZS9pbnZvaWNlTWFuYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"invoice-manage\">\r\n        <view class=\"invoice-content\">\r\n            <template v-if=\"invoiceList.length > 0\">\r\n                <view class=\"invoice-list\">\r\n                    <view \r\n                        class=\"invoice-item\" \r\n                        v-for=\"(item, index) in invoiceList\" \r\n                        :key=\"item.id\"\r\n                        :class=\"[index === invoiceList.length - 1 ? 'last-item' : '']\"\r\n                    >\r\n                        <view class=\"item-header\">\r\n                            <view class=\"order-type\">\r\n                                <!-- functionType 功能类型：1停车，2会员 -->\r\n                                <template v-if=\"item.functionType === 1\">\r\n                                    <image src=\"/static/order/car.png\" mode=\"aspectFit\"></image>\r\n                                    <text class=\"type-name\">停车订单</text>\r\n                                </template>\r\n                                <template v-else-if=\"item.functionType === 2\">\r\n                                    <image src=\"/static/package/taocantype.png\" mode=\"aspectFit\"></image>\r\n                                    <text class=\"type-name\">会员订单</text>\r\n                                </template>\r\n                                <template v-else>\r\n                                    <image src=\"/static/order/car.png\" mode=\"aspectFit\"></image>\r\n                                    <text class=\"type-name\">其他订单</text>\r\n                                </template>\r\n                            </view>\r\n                            <view class=\"status-badge\" :style=\"{ color: getStatusColor(item.status) }\">\r\n                                {{ getStatusText(item.status) }}\r\n                            </view>\r\n                        </view>\r\n\r\n                        <view class=\"item-content\">\r\n                            <view class=\"info-row\">\r\n                                <text class=\"info-label\">发票抬头：</text>\r\n                                <text class=\"info-value\">{{ item.invoiceTitleContent || '个人' }}</text>\r\n                            </view>\r\n                            <view class=\"info-row\">\r\n                                <text class=\"info-label\">发票类型：</text>\r\n                                <text class=\"info-value\">{{ item.invoiceType === 0 ? '普通发票' : '专用发票' }}</text>\r\n                            </view>\r\n                            <view class=\"info-row\">\r\n                                <text class=\"info-label\">接收邮箱：</text>\r\n                                <text class=\"info-value\">{{ item.notifyEmail || '-' }}</text>\r\n                            </view>\r\n                            <template v-if=\"item.status === 'CLOSED'\">\r\n                                <view class=\"info-row\">\r\n                                    <text class=\"info-label\">失败原因：</text>\r\n                                    <text class=\"info-value error\">{{ item.remark || '-' }}</text>\r\n                                </view>\r\n                            </template>\r\n                        </view>\r\n\r\n                        <view class=\"item-footer\">\r\n                            <view class=\"issue-time\">\r\n                                <up-icon name=\"clock\" color=\"#666666\" size=\"14\"></up-icon>\r\n                                <text>{{ item.issueDate || '-' }}</text>\r\n                            </view>\r\n                            <view class=\"invoice-amount\">\r\n                                <text class=\"amount-label\">开票金额：</text>\r\n                                <text class=\"amount-value\">¥{{ item.totalMoney || '0' }}</text>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n\r\n            </template>\r\n\r\n            <template v-else>\r\n                <view class=\"empty-state\">\r\n                    <up-empty text=\"暂无发票记录\" color=\"#64748b\"></up-empty>\r\n                </view>\r\n            </template>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { getInvoiceRecordList } from '@/api/invoice'\r\n\r\n// 响应式数据\r\nconst invoiceList = ref([])\r\nconst loading = ref(false)\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n    fetchInvoiceList()\r\n})\r\n\r\n// 获取发票列表\r\nconst fetchInvoiceList = async () => {\r\n    if (loading.value) return\r\n    \r\n    try {\r\n        loading.value = true\r\n        \r\n        uni.showLoading({\r\n            title: '加载中...',\r\n            mask: true\r\n        })\r\n\r\n        const res = await getInvoiceRecordList()\r\n        console.log('发票记录数据:', res)\r\n\r\n        // 适配后端返回格式\r\n        invoiceList.value = res.data || res.rows || []\r\n\r\n    } catch (error) {\r\n        console.error('获取发票列表失败:', error)\r\n        uni.showToast({\r\n            title: '获取发票列表失败',\r\n            icon: 'none'\r\n        })\r\n    } finally {\r\n        loading.value = false\r\n        uni.hideLoading()\r\n    }\r\n}\r\n\r\n\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n    switch (status) {\r\n        case 'PENDING':\r\n            return '待开具'\r\n        case 'ISSUING':\r\n            return '开具中'\r\n        case 'ISSUED':\r\n            return '已开具'\r\n        case 'REVERSING':\r\n            return '红冲中'\r\n        case 'REVERSED':\r\n            return '已红冲'\r\n        case 'CLOSED':\r\n            return '已关闭'\r\n        default:\r\n            return '-'\r\n    }\r\n}\r\n\r\n// 获取状态颜色\r\nconst getStatusColor = (status) => {\r\n    switch (status) {\r\n        case 'PENDING':\r\n            return '#3b82f6'\r\n        case 'ISSUING':\r\n            return '#f59e0b'\r\n        case 'ISSUED':\r\n            return '#16a34a'\r\n        case 'REVERSING':\r\n            return '#ef4444'\r\n        case 'REVERSED':\r\n            return '#6b7280'\r\n        case 'CLOSED':\r\n            return '#9ca3af'\r\n        default:\r\n            return '#6b7280'\r\n    }\r\n}\r\n\r\n// 下拉刷新\r\nconst onPullDownRefresh = async () => {\r\n    await fetchInvoiceList()\r\n    uni.stopPullDownRefresh()\r\n}\r\n\r\n// 暴露方法给页面使用\r\ndefineExpose({\r\n    onPullDownRefresh\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.invoice-manage {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.invoice-content {\r\n    padding: 32rpx;\r\n}\r\n\r\n.invoice-list {\r\n    .invoice-item {\r\n        background: white;\r\n        border-radius: 20rpx;\r\n        padding: 32rpx;\r\n        margin-bottom: 20rpx;\r\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n        \r\n        &.last-item {\r\n            margin-bottom: 0;\r\n        }\r\n        \r\n        .item-header {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-bottom: 24rpx;\r\n            \r\n            .order-type {\r\n                display: flex;\r\n                align-items: center;\r\n                \r\n                image {\r\n                    width: 32rpx;\r\n                    height: 32rpx;\r\n                    margin-right: 12rpx;\r\n                }\r\n                \r\n                .type-name {\r\n                    font-size: 32rpx;\r\n                    font-weight: bold;\r\n                    color: #333333;\r\n                }\r\n            }\r\n            \r\n            .status-badge {\r\n                font-size: 26rpx;\r\n                font-weight: 500;\r\n                padding: 8rpx 16rpx;\r\n                border-radius: 16rpx;\r\n                background: rgba(59, 130, 246, 0.1);\r\n            }\r\n        }\r\n        \r\n        .item-content {\r\n            border-bottom: 1rpx solid #f0f0f0;\r\n            padding-bottom: 24rpx;\r\n            margin-bottom: 24rpx;\r\n            \r\n            .info-row {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 16rpx;\r\n                \r\n                &:last-child {\r\n                    margin-bottom: 0;\r\n                }\r\n                \r\n                .info-label {\r\n                    font-size: 26rpx;\r\n                    color: #666666;\r\n                    min-width: 140rpx;\r\n                }\r\n                \r\n                .info-value {\r\n                    font-size: 26rpx;\r\n                    color: #333333;\r\n                    flex: 1;\r\n                    \r\n                    &.error {\r\n                        color: #ef4444;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        .item-footer {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            \r\n            .issue-time {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 24rpx;\r\n                color: #666666;\r\n                \r\n                text {\r\n                    margin-left: 8rpx;\r\n                }\r\n            }\r\n            \r\n            .invoice-amount {\r\n                display: flex;\r\n                align-items: center;\r\n                \r\n                .amount-label {\r\n                    font-size: 24rpx;\r\n                    color: #3b82f6;\r\n                }\r\n                \r\n                .amount-value {\r\n                    font-size: 28rpx;\r\n                    font-weight: bold;\r\n                    color: #3b82f6;\r\n                    margin-left: 8rpx;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n.empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 160rpx 40rpx;\r\n    margin-top: 60rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/invoice/invoiceManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "getInvoiceRecordList"], "mappings": ";;;;;;;;;;;;;;;;;AAmFA,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,UAAUA,cAAG,IAAC,KAAK;AAGzBC,kBAAAA,UAAU,MAAM;AACZ,uBAAkB;AAAA,IACtB,CAAC;AAGD,UAAM,mBAAmB,YAAY;AACjC,UAAI,QAAQ;AAAO;AAEnB,UAAI;AACA,gBAAQ,QAAQ;AAEhBC,sBAAAA,MAAI,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAED,cAAM,MAAM,MAAMC,iCAAsB;AACxCD,sBAAAA,MAAY,MAAA,OAAA,0CAAA,WAAW,GAAG;AAG1B,oBAAY,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAE;AAAA,MAEjD,SAAQ,OAAO;AACZA,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACT,UAAc;AACN,gBAAQ,QAAQ;AAChBA,sBAAAA,MAAI,YAAa;AAAA,MACpB;AAAA,IACL;AAKA,UAAM,gBAAgB,CAAC,WAAW;AAC9B,cAAQ,QAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACd;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,WAAW;AAC/B,cAAQ,QAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACd;AAAA,IACL;AAGA,UAAM,oBAAoB,YAAY;AAClC,YAAM,iBAAkB;AACxBA,oBAAAA,MAAI,oBAAqB;AAAA,IAC7B;AAGA,aAAa;AAAA,MACT;AAAA,IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3KD,GAAG,WAAW,eAAe;"}