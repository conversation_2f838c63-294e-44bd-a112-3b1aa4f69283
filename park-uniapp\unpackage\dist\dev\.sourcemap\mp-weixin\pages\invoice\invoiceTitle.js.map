{"version": 3, "file": "invoiceTitle.js", "sources": ["pages/invoice/invoiceTitle.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW52b2ljZS9pbnZvaWNlVGl0bGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"invoice-title-container\">\r\n    <!-- 发票抬头列表 -->\r\n    <view class=\"invoice-title-list\" v-if=\"invoiceTitleList.length > 0\">\r\n      <view\r\n        class=\"invoice-title-item\"\r\n        v-for=\"item in invoiceTitleList\"\r\n        :key=\"item.id\"\r\n      >\r\n        <view class=\"item-card\">\r\n          <view class=\"item-header\">\r\n            <view class=\"item-type\">\r\n              <up-icon\r\n                :name=\"getTypeIcon(item)\"\r\n                :color=\"getTypeIconColor(item)\"\r\n                size=\"16\"\r\n                class=\"type-icon\"\r\n              ></up-icon>\r\n              <text class=\"type-text\">{{ getTypeText(item) }}</text>\r\n            </view>\r\n            <view class=\"item-actions\">\r\n              <view class=\"action-btn edit-btn\" @tap=\"handleEdit(item)\">\r\n                <up-icon\r\n                  name=\"edit-pen\"\r\n                  color=\"#9e9e9e\"\r\n                  size=\"16\"\r\n                  class=\"action-icon\"\r\n                ></up-icon>\r\n                <text>编辑</text>\r\n              </view>\r\n              <view class=\"action-btn delete-btn\" @tap=\"handleDelete(item)\">\r\n                <up-icon\r\n                  name=\"trash\"\r\n                  color=\"#9e9e9e\"\r\n                  size=\"16\"\r\n                  class=\"action-icon\"\r\n                ></up-icon>\r\n                <text>删除</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"item-content\">\r\n            <view class=\"content-title\">{{ item.invoiceTitleContent }}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <up-empty text=\"暂无发票抬头信息\" />\r\n    </view>\r\n\r\n    <!-- 底部固定的添加按钮 -->\r\n    <view class=\"bottom-add-btn\" @tap=\"addInvoiceTitle\">\r\n      <up-icon name=\"plus\" color=\"#fff\" size=\"18\" class=\"add-icon\"></up-icon>\r\n      <text class=\"add-text\">添加发票抬头</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, nextTick } from \"vue\";\r\nimport { onShow } from \"@dcloudio/uni-app\";\r\nimport { getInvoiceTitleList, deleteInvoiceTitle } from \"@/api/invoice\";\r\n\r\nconst invoiceTitleList = ref([]);\r\n\r\nonShow(() => {\r\n  fetchInvoiceTitleList();\r\n});\r\n\r\n// 获取类型图标\r\nconst getTypeIcon = (item) => {\r\n  if (item.invoiceType === 1) {\r\n    return \"file-text\"; // 专用发票使用文件图标\r\n  }\r\n  return item.titleType === 1 ? \"home\" : \"account\"; // 公司用房子图标，个人用用户图标\r\n};\r\n\r\n// 获取类型图标颜色\r\nconst getTypeIconColor = (item) => {\r\n  if (item.invoiceType === 1) {\r\n    return \"#4BA1FC\"; // 专用发票用蓝色\r\n  }\r\n  return item.titleType === 1 ? \"#FF9500\" : \"#34C759\"; // 公司用橙色，个人用绿色\r\n};\r\n\r\n// 获取类型文本\r\nconst getTypeText = (item) => {\r\n  if (item.invoiceType === 1) {\r\n    return \"专用发票抬头\";\r\n  }\r\n  const typeText = item.titleType === 1 ? \"公司\" : \"个人\";\r\n  return `${typeText} · 普通发票抬头`;\r\n};\r\n\r\n// 获取发票抬头列表\r\nconst fetchInvoiceTitleList = () => {\r\n  getInvoiceTitleList()\r\n    .then((res) => {\r\n      console.log(res);\r\n      // 确保数据正确更新\r\n      invoiceTitleList.value = [];\r\n      nextTick(() => {\r\n        invoiceTitleList.value = res.data || [];\r\n      });\r\n    })\r\n    .catch((err) => {\r\n      console.error(\"获取发票抬头列表失败:\", err);\r\n      uni.showToast({\r\n        title: \"获取数据失败\",\r\n        icon: \"none\",\r\n      });\r\n    });\r\n};\r\n\r\n// 添加发票抬头\r\nconst addInvoiceTitle = () => {\r\n  uni.navigateTo({\r\n    url: \"/pages/invoice/addInvoiceTitle?isEdit=false\",\r\n  });\r\n};\r\n\r\n// 编辑发票抬头\r\nconst handleEdit = (item) => {\r\n  const obj = JSON.stringify(item);\r\n  uni.navigateTo({\r\n    url:\r\n      \"/pages/invoice/addInvoiceTitle?isEdit=true&obj=\" +\r\n      encodeURIComponent(obj),\r\n  });\r\n};\r\n\r\n// 删除发票抬头\r\nconst handleDelete = (item) => {\r\n  uni.showModal({\r\n    content: \"确定要删除这个发票抬头吗？\",\r\n    cancelText: \"取消\",\r\n    confirmText: \"确认\",\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 显示加载提示\r\n        uni.showLoading({\r\n          title: \"删除中...\",\r\n        });\r\n\r\n        deleteInvoiceTitle({ id: item.id })\r\n          .then((res) => {\r\n            uni.hideLoading();\r\n            if (res.code === 200) {\r\n              uni.showToast({\r\n                title: \"删除成功\",\r\n                icon: \"success\",\r\n              });\r\n              // 重新获取列表\r\n              fetchInvoiceTitleList();\r\n            } else {\r\n              uni.showToast({\r\n                title: res.msg || \"删除失败\",\r\n                icon: \"none\",\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            uni.hideLoading();\r\n            console.error(\"删除发票抬头失败:\", err);\r\n            uni.showToast({\r\n              title: \"删除失败，请重试\",\r\n              icon: \"none\",\r\n            });\r\n          });\r\n      }\r\n    },\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.invoice-title-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  padding: 24rpx;\r\n  padding-bottom: 140rpx;\r\n  position: relative;\r\n}\r\n\r\n.invoice-title-list {\r\n  .invoice-title-item {\r\n    margin-bottom: 24rpx;\r\n\r\n    .item-card {\r\n      background: #fff;\r\n      border-radius: 24rpx;\r\n      padding: 32rpx;\r\n      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .item-header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding-bottom: 20rpx;\r\n        border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);\r\n\r\n        .item-type {\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .type-icon {\r\n            margin-right: 8rpx;\r\n          }\r\n\r\n          .type-text {\r\n            font-size: 24rpx;\r\n            font-weight: bold;\r\n            color: #616161;\r\n            line-height: 44rpx;\r\n          }\r\n        }\r\n\r\n        .item-actions {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 24rpx;\r\n\r\n          .action-btn {\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 28rpx;\r\n            font-weight: 400;\r\n            color: #9e9e9e;\r\n            line-height: 44rpx;\r\n\r\n            .action-icon {\r\n              margin-right: 8rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-content {\r\n        padding-top: 20rpx;\r\n\r\n        .content-title {\r\n          font-size: 32rpx;\r\n          font-weight: bold;\r\n          color: #212121;\r\n          padding-bottom: 8rpx;\r\n          line-height: 44rpx;\r\n        }\r\n\r\n        .content-subtitle {\r\n          font-size: 24rpx;\r\n          font-weight: 400;\r\n          color: #9e9e9e;\r\n          line-height: 44rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 0;\r\n}\r\n\r\n/* 底部固定的添加按钮 */\r\n.bottom-add-btn {\r\n  position: fixed;\r\n  bottom: 40rpx;\r\n  width: 85%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);\r\n  color: #fff;\r\n  padding: 24rpx;\r\n  border-radius: 50rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n\r\n  .add-icon {\r\n    margin-right: 12rpx;\r\n  }\r\n\r\n  .add-text {\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/invoice/invoiceTitle.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShow", "getInvoiceTitleList", "uni", "nextTick", "deleteInvoiceTitle", "res"], "mappings": ";;;;;;;;;;;;;;;;AAkEA,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAE/BC,kBAAAA,OAAO,MAAM;AACX;IACF,CAAC;AAGD,UAAM,cAAc,CAAC,SAAS;AAC5B,UAAI,KAAK,gBAAgB,GAAG;AAC1B,eAAO;AAAA,MACR;AACD,aAAO,KAAK,cAAc,IAAI,SAAS;AAAA,IACzC;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,KAAK,gBAAgB,GAAG;AAC1B,eAAO;AAAA,MACR;AACD,aAAO,KAAK,cAAc,IAAI,YAAY;AAAA,IAC5C;AAGA,UAAM,cAAc,CAAC,SAAS;AAC5B,UAAI,KAAK,gBAAgB,GAAG;AAC1B,eAAO;AAAA,MACR;AACD,YAAM,WAAW,KAAK,cAAc,IAAI,OAAO;AAC/C,aAAO,GAAG,QAAQ;AAAA,IACpB;AAGA,UAAM,wBAAwB,MAAM;AAClCC,sCAAqB,EAClB,KAAK,CAAC,QAAQ;AACbC,sBAAAA,MAAY,MAAA,OAAA,yCAAA,GAAG;AAEf,yBAAiB,QAAQ;AACzBC,sBAAAA,WAAS,MAAM;AACb,2BAAiB,QAAQ,IAAI,QAAQ,CAAA;AAAA,QAC7C,CAAO;AAAA,MACP,CAAK,EACA,MAAM,CAAC,QAAQ;AACdD,sBAAc,MAAA,MAAA,SAAA,yCAAA,eAAe,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACd,CAAO;AAAA,MACP,CAAK;AAAA,IACL;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,YAAM,MAAM,KAAK,UAAU,IAAI;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KACE,oDACA,mBAAmB,GAAG;AAAA,MAC5B,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACjB,CAAS;AAEDE,wBAAAA,mBAAmB,EAAE,IAAI,KAAK,GAAE,CAAE,EAC/B,KAAK,CAACC,SAAQ;AACbH,4BAAG,MAAC,YAAW;AACf,kBAAIG,KAAI,SAAS,KAAK;AACpBH,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACtB,CAAe;AAED;cACd,OAAmB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAOG,KAAI,OAAO;AAAA,kBAClB,MAAM;AAAA,gBACtB,CAAe;AAAA,cACF;AAAA,YACb,CAAW,EACA,MAAM,CAAC,QAAQ;AACdH,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,GAAG;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACpB,CAAa;AAAA,YACb,CAAW;AAAA,UACJ;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9KA,GAAG,WAAW,eAAe;"}