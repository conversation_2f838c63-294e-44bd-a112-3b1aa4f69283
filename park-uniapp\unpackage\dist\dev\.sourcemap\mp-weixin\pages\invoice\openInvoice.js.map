{"version": 3, "file": "openInvoice.js", "sources": ["pages/invoice/openInvoice.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW52b2ljZS9vcGVuSW52b2ljZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"open-invoice\">\r\n    <view class=\"cell\">\r\n      <view class=\"cell-title\">发票详情</view>\r\n      <view class=\"content\">\r\n        <view class=\"content_item\" @tap=\"showPopup = true\">\r\n          <view class=\"title\">发票类型</view>\r\n          <view class=\"word\">\r\n            {{\r\n              invoiceInfo.id\r\n                ? invoiceInfo.invoiceType === 1\r\n                  ? '专票'\r\n                  : invoiceInfo.titleType === 1\r\n                  ? '公司'\r\n                  : '个人'\r\n                : '请选择抬头'\r\n            }}\r\n          </view>\r\n          <up-icon name=\"arrow-right\" color=\"#BDBDBD\" size=\"16\"></up-icon>\r\n        </view>\r\n        \r\n        <view class=\"content_item\">\r\n          <view class=\"title\">发票抬头</view>\r\n          <view class=\"word\">{{ invoiceInfo.invoiceTitleContent || '' }}</view>\r\n        </view>\r\n        \r\n        <view class=\"content_item\">\r\n          <view class=\"title\">总金额</view>\r\n          <view class=\"word\">\r\n            <text class=\"money\">{{ money || '0' }}</text> 元\r\n          </view>\r\n        </view>\r\n        \r\n        <template v-if=\"invoiceInfo.titleType === 1 || invoiceInfo.invoiceType === 1\">\r\n          <view class=\"content_item\">\r\n            <view class=\"title\">单位税号</view>\r\n            <view class=\"word\">{{ invoiceInfo.unitDutyParagraph || '' }}</view>\r\n          </view>\r\n          <view class=\"content_item\">\r\n            <view class=\"title\">注册地址</view>\r\n            <view class=\"word\">{{ invoiceInfo.registerAddress || '' }}</view>\r\n          </view>\r\n          <view class=\"content_item\">\r\n            <view class=\"title\">注册电话</view>\r\n            <view class=\"word\">{{ invoiceInfo.registerPhone || '' }}</view>\r\n          </view>\r\n          <view class=\"content_item\">\r\n            <view class=\"title\">开户银行</view>\r\n            <view class=\"word\">{{ invoiceInfo.depositBank || '' }}</view>\r\n          </view>\r\n          <view class=\"content_item\">\r\n            <view class=\"title\">银行账户</view>\r\n            <view class=\"word\">{{ invoiceInfo.bankAccount || '' }}</view>\r\n          </view>\r\n        </template>\r\n      </view>\r\n      \r\n      <view class=\"cell-title\">接收方式</view>\r\n      <view class=\"email\">\r\n        <view class=\"content_item\">\r\n          <view class=\"title\">电子邮箱</view>\r\n          <view class=\"email-input\">\r\n            <up-input\r\n              v-model=\"email\"\r\n              border=\"none\"\r\n              placeholder=\"请输入您的电子邮箱\"\r\n              clearable\r\n              fontSize=\"28rpx\"\r\n              color=\"#616161\"\r\n              :placeholderStyle=\"{ color: '#616161' }\"\r\n            ></up-input>\r\n          </view>\r\n        </view>\r\n        <view class=\"desc\">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>\r\n      </view>\r\n      \r\n      <view class=\"btn\" @tap=\"handleAddInvoice\" :class=\"{ disabled: isLoading }\">\r\n        <up-loading-icon v-if=\"isLoading\" color=\"#ffffff\" size=\"20\"></up-loading-icon>\r\n        <text v-if=\"!isLoading\">{{ isResume ? '发票重开' : '提交开票' }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"tips_word\">\r\n      <view class=\"tips_word_title\">注意事项：</view>\r\n      <view class=\"tips_word_desc\">\r\n        1、开票完成，请前往【我的】-【开票管理】查看对应结果。邮件请耐心等待3-10分钟。\r\n      </view>\r\n      <view class=\"tips_word_desc\">\r\n        2、仅本软件支付订单能开具发票，其他请前往对应软件或联系场地运营单位开具发票。\r\n      </view>\r\n      <view class=\"tips_word_desc\">\r\n        3、若发票信息错误，请【申请换开】，同一订单发票仅支持重开一次。\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 发票抬头选择弹窗 -->\r\n    <up-popup\r\n      :show=\"showPopup\"\r\n      :safeAreaInsetBottom=\"false\"\r\n      :round=\"20\"\r\n      closeOnClickOverlay\r\n      @close=\"showPopup = false\"\r\n    >\r\n      <view class=\"popup-cell\">\r\n        <view class=\"popup-title\">发票类型</view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"invoice-item\"\r\n            v-for=\"item in invoiceTitleList\"\r\n            :key=\"item.id\"\r\n            @tap=\"chooseInvoice(item)\"\r\n          >\r\n            <view class=\"item-header\">\r\n              <template v-if=\"item.invoiceType === 1\">\r\n                <up-icon name=\"file-text\" color=\"#4BA1FC\" size=\"16\"></up-icon>\r\n                <text class=\"header-text\">专用发票抬头</text>\r\n              </template>\r\n              <template v-else>\r\n                <template v-if=\"item.titleType === 1\">\r\n                  <up-icon name=\"home\" color=\"#FF9500\" size=\"16\"></up-icon>\r\n                  <text class=\"header-text\">公司 · 普通发票抬头</text>\r\n                </template>\r\n                <template v-else>\r\n                  <up-icon name=\"account\" color=\"#34C759\" size=\"16\"></up-icon>\r\n                  <text class=\"header-text\">个人 · 普通发票抬头</text>\r\n                </template>\r\n              </template>\r\n            </view>\r\n            \r\n            <view class=\"item-title\">\r\n              {{ item.invoiceTitleContent || '-' }}\r\n            </view>\r\n            \r\n            <view class=\"item-bottom\">\r\n              <view class=\"item-desc\">\r\n                {{ item.unitDutyParagraph || '' }}\r\n              </view>\r\n              <view class=\"item-edit\" @tap.stop=\"handleEdit(item)\">\r\n                <up-icon name=\"edit-pen\" color=\"#9e9e9e\" size=\"14\"></up-icon>\r\n                <text>编辑</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"add-btn\" @tap=\"addInvoiceTitle\">\r\n          <up-icon name=\"plus\" color=\"#ffffff\" size=\"16\"></up-icon>\r\n          <text>添加发票抬头</text>\r\n        </view>\r\n      </view>\r\n    </up-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive} from 'vue'\r\nimport { onLoad, onShow } from '@dcloudio/uni-app'\r\nimport { getInvoiceTitleList, postSaveInvoiceRecord, postResumeInvoice } from '@/api/invoice'\r\n\r\n// 响应式数据\r\nconst showPopup = ref(false)\r\nconst invoiceTitleList = ref([])\r\nconst invoiceInfo = reactive({})\r\n// 订单金额\r\nconst money = ref(null)\r\n// 订单号\r\nconst functionId = ref(null)\r\n// 功能类型\r\nconst functionType = ref(null)\r\n// 电子邮箱\r\nconst email = ref('')\r\nconst isLoading = ref(false)\r\n// 是否重开\r\nconst isResume = ref(false)\r\n// 发票id\r\nconst invoiceId = ref('')\r\n\r\nonShow(() => {\r\n  fetchInvoiceTitleList()\r\n})\r\n\r\nonLoad((options) => {\r\n  console.log(options)\r\n  money.value = options.money\r\n  functionId.value = options.functionId\r\n  functionType.value = options.functionType\r\n  \r\n  if (options.invoiceId) {\r\n    invoiceId.value = options.invoiceId\r\n  }\r\n  \r\n  if (options.isResume) {\r\n    isResume.value = true\r\n  } else {\r\n    isResume.value = false\r\n  }\r\n})\r\n\r\n// 提交开票\r\nconst handleAddInvoice = async () => {\r\n  if (!functionId.value) {\r\n    uni.showToast({\r\n      title: '订单id不存在~',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n  \r\n  if (!functionType.value) {\r\n    uni.showToast({\r\n      title: '功能类型不存在~',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n  \r\n  if (!invoiceInfo.invoiceTitleContent) {\r\n    uni.showToast({\r\n      title: '请选择抬头~',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n  \r\n  if (!email.value) {\r\n    uni.showToast({\r\n      title: '电子邮箱必填~',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  const params = {\r\n    invoiceType: invoiceInfo.invoiceType,\r\n    functionType: functionType.value,\r\n    functionId: functionId.value,\r\n    invoiceTitleContent: invoiceInfo.invoiceTitleContent,\r\n    unitDutyParagraph: invoiceInfo.unitDutyParagraph,\r\n    registerAddress: invoiceInfo.registerAddress,\r\n    registerPhone: invoiceInfo.registerPhone,\r\n    depositBank: invoiceInfo.depositBank,\r\n    bankAccount: invoiceInfo.bankAccount,\r\n    notifyEmail: email.value,\r\n    titleId: invoiceInfo.id,\r\n    id: invoiceId.value\r\n  }\r\n  console.log(params)\r\n\r\n  isLoading.value = true\r\n\r\n  try {\r\n    const apiCall = isResume.value ? postResumeInvoice : postSaveInvoiceRecord\r\n    const res = await apiCall(params)\r\n    \r\n    uni.showModal({\r\n      content: res.msg,\r\n      showCancel: false,\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          uni.navigateBack()\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('开票失败:', error)\r\n    uni.showToast({\r\n      title: '开票失败，请重试',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    isLoading.value = false\r\n  }\r\n}\r\n\r\n// 获取发票抬头列表\r\nconst fetchInvoiceTitleList = async () => {\r\n  try {\r\n    const res = await getInvoiceTitleList()\r\n    invoiceTitleList.value = res.data || []\r\n  } catch (error) {\r\n    console.error('获取发票抬头列表失败:', error)\r\n    uni.showToast({\r\n      title: '获取数据失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\n// 选择发票抬头\r\nconst chooseInvoice = (item) => {\r\n  showPopup.value = false\r\n  Object.assign(invoiceInfo, item)\r\n}\r\n\r\n// 编辑发票抬头\r\nconst handleEdit = (item) => {\r\n  const obj = JSON.stringify(item)\r\n  uni.navigateTo({\r\n    url: '/pages/invoice/addInvoiceTitle?isEdit=true&obj=' + encodeURIComponent(obj)\r\n  })\r\n}\r\n\r\n// 添加发票抬头\r\nconst addInvoiceTitle = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/invoice/addInvoiceTitle?isEdit=false'\r\n  })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.open-invoice {\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n\r\n  .cell {\r\n    padding: 32rpx;\r\n\r\n    .cell-title {\r\n      font-size: 32rpx;\r\n      font-weight:600;\r\n      color: #000000;\r\n      margin-bottom: 16rpx;\r\n    }\r\n\r\n    .content {\r\n      padding: 32rpx;\r\n      background: #ffffff;\r\n      border-radius: 20rpx;\r\n      margin-bottom: 32rpx;\r\n\r\n      .content_item {\r\n        display: flex;\r\n        align-items: center;\r\n        position: relative;\r\n        border-bottom: 1rpx solid rgba(189, 189, 189, 0.2);\r\n        padding: 26rpx 0;\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .title {\r\n          font-size: 28rpx;\r\n          color: #212121;\r\n          font-weight: 500;\r\n          margin-right: 74rpx;\r\n          min-width: 112rpx;\r\n          word-break: keep-all;\r\n        }\r\n\r\n        .word {\r\n          flex: 1;\r\n          font-size: 28rpx;\r\n          color: #616161;\r\n          line-height: 1.5;\r\n\r\n          .money {\r\n            font-weight: 600;\r\n            color: #246bfd;\r\n            margin-right: 8rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .email {\r\n      padding: 32rpx;\r\n      background: #ffffff;\r\n      border-radius: 20rpx;\r\n      margin-bottom: 56rpx;\r\n\r\n      .content_item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20rpx;\r\n\r\n        .title {\r\n          font-size: 28rpx;\r\n          color: #212121;\r\n          font-weight: 500;\r\n          margin-right: 74rpx;\r\n          min-width: 112rpx;\r\n          word-break: keep-all;\r\n        }\r\n\r\n        .email-input {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .desc {\r\n        font-size: 24rpx;\r\n        color: #f5820e;\r\n        margin-top: 35rpx;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n      width: 100%;\r\n      height: 88rpx;\r\n      background: #246bfd;\r\n      border-radius: 70rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 36rpx;\r\n      font-weight: 600;\r\n      color: #ffffff;\r\n\r\n      &.disabled {\r\n        opacity: 0.6;\r\n      }\r\n\r\n      text {\r\n        margin-left: 8rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .tips_word {\r\n    padding: 0 42rpx 60rpx;\r\n\r\n    .tips_word_title {\r\n      font-weight: 700;\r\n      font-size: 36rpx;\r\n      color: #2a304a;\r\n      margin-bottom: 20rpx;\r\n    }\r\n\r\n    .tips_word_desc {\r\n      font-weight: 400;\r\n      font-size: 28rpx;\r\n      color: #a0a7c2;\r\n      line-height: 1.4;\r\n      margin-bottom: 16rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.popup-cell {\r\n  padding: 32rpx;\r\n  background: #ebeef6;\r\n  border-radius: 20rpx;\r\n  max-height: 80vh;\r\n\r\n  .popup-title {\r\n    font-weight: 600;\r\n    font-size: 30rpx;\r\n    color: #333333;\r\n    border-left: 4rpx solid #246bfd;\r\n    padding-left: 12rpx;\r\n    margin-bottom: 32rpx;\r\n  }\r\n\r\n  .popup-content {\r\n    max-height: 600rpx;\r\n    overflow-y: scroll;\r\n\r\n    .invoice-item {\r\n      padding: 32rpx;\r\n      background: #ffffff;\r\n      border-radius: 20rpx;\r\n      margin-bottom: 20rpx;\r\n\r\n      .item-header {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 20rpx;\r\n        border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);\r\n\r\n        .header-text {\r\n          font-size: 24rpx;\r\n          font-weight: bold;\r\n          color: #616161;\r\n          margin-left: 8rpx;\r\n        }\r\n      }\r\n\r\n      .item-title {\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n        color: #212121;\r\n        margin: 20rpx 0 8rpx;\r\n      }\r\n\r\n      .item-bottom {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        .item-desc {\r\n          font-size: 24rpx;\r\n          color: #9e9e9e;\r\n          flex: 1;\r\n        }\r\n\r\n        .item-edit {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 28rpx;\r\n          color: #9e9e9e;\r\n\r\n          text {\r\n            margin-left: 6rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .add-btn {\r\n    height: 88rpx;\r\n    background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);\r\n    border-radius: 16rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #ffffff;\r\n    margin-top: 20rpx;\r\n\r\n    text {\r\n      margin-left: 8rpx;\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/invoice/openInvoice.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onShow", "onLoad", "uni", "postResumeInvoice", "postSaveInvoiceRecord", "res", "getInvoiceTitleList"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgKA,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,mBAAmBA,cAAG,IAAC,EAAE;AAC/B,UAAM,cAAcC,cAAQ,SAAC,EAAE;AAE/B,UAAM,QAAQD,cAAG,IAAC,IAAI;AAEtB,UAAM,aAAaA,cAAG,IAAC,IAAI;AAE3B,UAAM,eAAeA,cAAG,IAAC,IAAI;AAE7B,UAAM,QAAQA,cAAG,IAAC,EAAE;AACpB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAE3B,UAAM,WAAWA,cAAG,IAAC,KAAK;AAE1B,UAAM,YAAYA,cAAG,IAAC,EAAE;AAExBE,kBAAAA,OAAO,MAAM;AACX,4BAAuB;AAAA,IACzB,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAClBC,oBAAAA,MAAY,MAAA,OAAA,wCAAA,OAAO;AACnB,YAAM,QAAQ,QAAQ;AACtB,iBAAW,QAAQ,QAAQ;AAC3B,mBAAa,QAAQ,QAAQ;AAE7B,UAAI,QAAQ,WAAW;AACrB,kBAAU,QAAQ,QAAQ;AAAA,MAC3B;AAED,UAAI,QAAQ,UAAU;AACpB,iBAAS,QAAQ;AAAA,MACrB,OAAS;AACL,iBAAS,QAAQ;AAAA,MAClB;AAAA,IACH,CAAC;AAGD,UAAM,mBAAmB,YAAY;AACnC,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,aAAa,OAAO;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,YAAY,qBAAqB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,MAAM,OAAO;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,YAAM,SAAS;AAAA,QACb,aAAa,YAAY;AAAA,QACzB,cAAc,aAAa;AAAA,QAC3B,YAAY,WAAW;AAAA,QACvB,qBAAqB,YAAY;AAAA,QACjC,mBAAmB,YAAY;AAAA,QAC/B,iBAAiB,YAAY;AAAA,QAC7B,eAAe,YAAY;AAAA,QAC3B,aAAa,YAAY;AAAA,QACzB,aAAa,YAAY;AAAA,QACzB,aAAa,MAAM;AAAA,QACnB,SAAS,YAAY;AAAA,QACrB,IAAI,UAAU;AAAA,MACf;AACDA,oBAAAA,2DAAY,MAAM;AAElB,gBAAU,QAAQ;AAElB,UAAI;AACF,cAAM,UAAU,SAAS,QAAQC,YAAiB,oBAAGC,YAAqB;AAC1E,cAAM,MAAM,MAAM,QAAQ,MAAM;AAEhCF,sBAAAA,MAAI,UAAU;AAAA,UACZ,SAAS,IAAI;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAACG,SAAQ;AAChB,gBAAIA,KAAI,SAAS;AACfH,4BAAAA,MAAI,aAAc;AAAA,YACnB;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,6DAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,wBAAwB,YAAY;AACxC,UAAI;AACF,cAAM,MAAM,MAAMI,gCAAqB;AACvC,yBAAiB,QAAQ,IAAI,QAAQ,CAAE;AAAA,MACxC,SAAQ,OAAO;AACdJ,sBAAAA,MAAc,MAAA,SAAA,wCAAA,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,gBAAU,QAAQ;AAClB,aAAO,OAAO,aAAa,IAAI;AAAA,IACjC;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,YAAM,MAAM,KAAK,UAAU,IAAI;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oDAAoD,mBAAmB,GAAG;AAAA,MACnF,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClTA,GAAG,WAAW,eAAe;"}