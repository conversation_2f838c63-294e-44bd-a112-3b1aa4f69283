{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"login-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\" :style=\"{ paddingTop: menuButtonTop + 'px', height: menuButtonHeight + 'px' }\">\r\n\t\t\t<view class=\"nav-back\" @tap=\"handleBack\">\r\n\t\t\t\t<up-icon name=\"arrow-left\" size=\"20\" color=\"#fff\"></up-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!--登录头部图片-->\r\n\t\t<view class=\"login-header\">\r\n\t\t\t<image src=\"/static/image/login.png\" mode=\"aspectFill\" class=\"login-header-image\" />\r\n\t\t</view>\r\n\r\n\t\t<!--登录内容区域-->\r\n\t\t<view class=\"login-content\">\r\n\t\t\t<!--登录表单-->\r\n\t\t\t<view class=\"login-form\">\r\n\t\t\t\t<view class=\"login-form-phone\">\r\n\t\t\t\t\t<text class=\"login-form-text\">手机号</text>\r\n\t\t\t\t\t<text class=\"login-form-text\">|</text>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入手机号\" class=\"login-form-input\" maxlength=\"11\"\r\n\t\t\t\t\t\tv-model=\"loginForm.phoneNumber\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"login-form-code\">\r\n\t\t\t\t\t<text class=\"login-form-text\">验证码</text>\r\n\t\t\t\t\t<text class=\"login-form-text\">|</text>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入验证码\" class=\"login-form-input\" maxlength=\"6\"\r\n\t\t\t\t\t\tv-model=\"loginForm.msgCode\" />\r\n\t\t\t\t\t<button class=\"login-form-code-button\" @tap=\"fetchCode\" :disabled=\"buttonDisabled\">{{\r\n\t\t\t\t\t\tbuttonText }}</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!--登录按钮-->\r\n\t\t\t<view class=\"login-button\" @tap=\"fetchLogin\">\r\n\t\t\t\t<text class=\"login-button-text\">登录</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 用户协议 -->\r\n\t\t\t<view class=\"agreement\">\r\n\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: isAgree }\" @tap=\"toggleAgreement\">\r\n\t\t\t\t\t<up-icon v-if=\"isAgree\" name=\"checkmark\" size=\"22\" color=\"#fff\"></up-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"agreement-text\">我已阅读并同意</text>\r\n\t\t\t\t<text class=\"agreement-link\" @tap=\"showAgreement\">《用户协议》</text>\r\n\t\t\t\t<text class=\"agreement-text\">和</text>\r\n\t\t\t\t<text class=\"agreement-link\" @tap=\"showPrivacy\">《隐私政策》</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 微信登录按钮 -->\r\n\t\t\t<button class=\"wechat-login\" v-if=\"!isAgree\" @click=\"checkBeforeLogin\">\r\n\t\t\t\t<view class=\"wechat-icon\">\r\n\t\t\t\t\t<up-icon name=\"weixin-fill\" size=\"32\" color=\"#fff\"></up-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <text class=\"wechat-text\">手机号快捷登录</text> -->\r\n\t\t\t</button>\r\n\t\t\t<button class=\"wechat-login\" v-else open-type=\"getPhoneNumber\" @getphonenumber=\"handleWechatLogin\">\r\n\t\t\t\t<view class=\"wechat-icon\">\r\n\t\t\t\t\t<up-icon name=\"weixin-fill\" size=\"32\" color=\"#fff\"></up-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <text class=\"wechat-text\">手机号快捷登录</text> -->\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onUnmounted, onMounted } from 'vue'\r\nimport { setStorageSync } from '@/utils/utils'\r\nimport { getCode, login, loginWx } from '@/api/login'\r\n\r\n// 系统信息相关\r\nconst menuButtonHeight = ref(0);\r\nconst menuButtonTop = ref(0);\r\n\r\n// 初始化系统信息\r\nconst initSystemInfo = () => {\r\n\ttry {\r\n\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n\t\tmenuButtonHeight.value = menuButtonInfo.height;\r\n\t\tmenuButtonTop.value = menuButtonInfo.top;\r\n\t} catch (error) {\r\n\t\tconsole.error('获取系统信息失败:', error);\r\n\t}\r\n};\r\n\r\n// 回退处理\r\nconst handleBack = () => {\r\n\tuni.navigateBack({\r\n\t\tdelta: 1,\r\n\t\tfail: () => {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/home/<USER>'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 登录表单\r\nconst loginForm = ref({\r\n\tphoneNumber: '',\r\n\tmsgCode: '',\r\n\twxCode: ''\r\n})\r\n\r\n// 获取验证码\r\nconst buttonText = ref('获取验证码')\r\nconst buttonDisabled = ref(false)\r\nlet countdownInterval = null;\r\nconst fetchCode = async () => {\r\n\tif (loginForm.value.phoneNumber === '' || loginForm.value.phoneNumber.length !== 11) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tbuttonDisabled.value = true\r\n\tlet timeLeft = 60;\r\n\tbuttonText.value = ` ${timeLeft} s`\r\n\tcountdownInterval = setInterval(() => {\r\n\t\ttimeLeft--;\r\n\t\tif (timeLeft > 0) {\r\n\t\t\tbuttonText.value = ` ${timeLeft} s`\r\n\t\t} else {\r\n\t\t\tclearInterval(countdownInterval)\r\n\t\t\tbuttonText.value = '获取验证码'\r\n\t\t\tbuttonDisabled.value = false\r\n\t\t}\r\n\t}, 1000);\r\n\tawait getCode({\r\n\t\tphoneNumber: loginForm.value.phoneNumber\r\n\t})\r\n\tuni.showToast({\r\n\t\ttitle: '验证码已发送',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\n// 用户协议勾选状态\r\nconst isAgree = ref(false)\r\n\r\n// 切换协议勾选状态\r\nconst toggleAgreement = () => {\r\n\tisAgree.value = !isAgree.value\r\n}\r\n\r\n// 查看用户协议\r\nconst showAgreement = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/aggrement/user-aggrement'\r\n\t})\r\n}\r\n\r\n// 查看隐私政策\r\nconst showPrivacy = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/aggrement/privacy-aggrement'\r\n\t})\r\n}\r\n// 检查是否勾选协议\r\nconst checkBeforeLogin = () => {\r\n\tif (!isAgree.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请阅读并同意用户协议和隐私政策',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\treturn true\r\n}\r\n// -------------------- 手机号验证码登录处理 --------------------\r\nconst fetchLogin = async () => {\r\n\tif (loginForm.value.phoneNumber === '' || loginForm.value.phoneNumber.length !== 11\r\n\t\t|| loginForm.value.msgCode === '' || loginForm.value.msgCode.length !== 6) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入正确的手机号或者验证码',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!checkBeforeLogin()) return\r\n\r\n\tconst loginRes = await getWxCode()\r\n\tloginForm.value.wxCode = loginRes.code\r\n\tif (!loginForm.value.wxCode) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '获取微信code失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tuni.showLoading({\r\n\t\ttitle: '登录中...',\r\n\t\tmask: true\r\n\t});\r\n\ttry {\r\n\t\tconst res = await login(loginForm.value)\r\n\t\tsetStorageSync('token', res.data.token)\r\n\t\tsetStorageSync('wxUser', res.data.wxUser)\r\n\t\tuni.hideLoading();\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '登录成功',\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 1500\r\n\t\t});\r\n\t\tsetTimeout(() => {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/home/<USER>'\r\n\t\t\t});\r\n\t\t}, 1500);\r\n\t} catch (error) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: error.msg,\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// -------------------- 微信登录处理 --------------------\r\nconst handleWechatLogin = async (e) => {\r\n\tcheckBeforeLogin();\r\n\tif (e.detail.errMsg === 'getPhoneNumber:ok') {\r\n\t\tconst phoneCode = e.detail.code;\r\n\t\tuni.login({\r\n\t\t\tprovider: 'weixin',\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tconst code = res.code;\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\twxCode: code,\r\n\t\t\t\t\tphoneCode: phoneCode\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tloginWx(params).then(res => {\r\n\t\t\t\t\t// 存储登录信息\r\n\t\t\t\t\tsetStorageSync('token', res.data.token)\r\n\t\t\t\t\tsetStorageSync('wxUser', res.data.wxUser)\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '/pages/home/<USER>'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取微信授权失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t})\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '获取手机号失败',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}\r\n}\r\n\r\n// 获取微信临时code\r\nconst getWxCode = () => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tuni.login({\r\n\t\t\tprovider: 'weixin',\r\n\t\t\tsuccess: resolve,\r\n\t\t\tfail: reject\r\n\t\t})\r\n\t})\r\n}\r\n\r\n// 组件卸载时清除定时器\r\nonUnmounted(() => {\r\n\tif (countdownInterval) {\r\n\t\tclearInterval(countdownInterval)\r\n\t}\r\n})\r\n\r\n// 生命周期\r\nonMounted(() => {\r\n\tinitSystemInfo();\r\n});\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tmin-height: 100vh;\r\n\tposition: relative;\r\n\tbackground: #f5f9ff;\r\n\toverflow: hidden;\r\n}\r\n\r\n// 自定义导航栏\r\n.custom-navbar {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 999;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding-left: 15rpx;\r\n\r\n\t.nav-back {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\tborder-radius: 50%;\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\t-webkit-backdrop-filter: blur(10px);\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t\tbackground: rgba(0, 0, 0, 0.3);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 登录头部图片\r\n.login-header {\r\n\twidth: 100%;\r\n\theight: 40vh;\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\r\n\t.login-header-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tobject-fit: cover;\r\n\t}\r\n}\r\n\r\n// 登录内容区域\r\n.login-content {\r\n\tposition: relative;\r\n\tmargin-top: -60rpx;\r\n\tbackground: #ffffff;\r\n\tborder-radius: 40rpx 40rpx 0 0;\r\n\tpadding: 60rpx 40rpx 40rpx;\r\n\tz-index: 2;\r\n\tflex: 1;\r\n\tbox-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n// 登录表单\r\n.login-form {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tz-index: 1;\r\n\r\n\t.login-form-phone,\r\n\t.login-form-code {\r\n\t\twidth: 90%;\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 32rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);\r\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.08);\r\n\r\n\t\t.login-form-text {\r\n\t\t\tcolor: #333333;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\r\n\t\t.login-form-input {\r\n\t\t\tflex: 1;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333333;\r\n\r\n\t\t\t&::placeholder {\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.login-form-code {\r\n\t\t.login-form-code-button {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 56rpx;\r\n\t\t\tborder-radius: 32rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground: linear-gradient(135deg, #60adff, #8677fc);\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(96, 173, 255, 0.2);\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tpadding: 0;\r\n\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(96, 173, 255, 0.15);\r\n\t\t\t}\r\n\r\n\t\t\t&[disabled] {\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t\tbackground: #cccccc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 用户协议\r\n.agreement {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin: 30rpx 40rpx;\r\n\tz-index: 1;\r\n\r\n\t.checkbox {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tborder: 2rpx solid #ddd;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 12rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&.checked {\r\n\t\t\tbackground: #60adff;\r\n\t\t\tborder-color: #60adff;\r\n\t\t}\r\n\t}\r\n\r\n\t.agreement-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.agreement-link {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #60adff;\r\n\t\tmargin: 0 4rpx;\r\n\r\n\t\t&:active {\r\n\t\t\topacity: 0.8;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 登录按钮\r\n.login-button {\r\n\twidth: 100%;\r\n\tmargin-top: 40rpx;\r\n\tpadding: 32rpx 0;\r\n\tbackground: linear-gradient(135deg, #60adff, #8677fc);\r\n\tborder-radius: 16rpx;\r\n\ttext-align: center;\r\n\tbox-shadow: 0 12rpx 24rpx rgba(96, 173, 255, 0.25);\r\n\ttransition: all 0.3s ease;\r\n\tz-index: 1;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\r\n\t&::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n\t\ttransform: translateX(-100%);\r\n\t}\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 6rpx 12rpx rgba(96, 173, 255, 0.2);\r\n\r\n\t\t&::before {\r\n\t\t\ttransform: translateX(100%);\r\n\t\t\ttransition: transform 0.6s ease;\r\n\t\t}\r\n\t}\r\n\r\n\t.login-button-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tletter-spacing: 3rpx;\r\n\t}\r\n}\r\n\r\n// 微信登录\r\n.wechat-login {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmargin-top: 60rpx;\r\n\tz-index: 1;\r\n\tbackground: none;\r\n\tborder: none;\r\n\tpadding: 0;\r\n\tline-height: 1;\r\n\r\n\t&::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.wechat-icon {\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx;\r\n\t\tbackground-color: #40c27f;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 8rpx 16rpx rgba(64, 194, 127, 0.2);\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(64, 194, 127, 0.15);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 自定义弹窗样式\r\n.custom-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 999;\r\n\r\n\t// 遮罩层\r\n\t.popup-mask {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t}\r\n\r\n\t// 弹窗内容\r\n\t.popup-content {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 24rpx 24rpx 0 0;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\ttransform: translateY(0);\r\n\r\n\t\t.popup-header {\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-bottom: 40rpx;\r\n\r\n\t\t\t.popup-title {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.popup-desc {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.popup-body {\r\n\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t.phone-number {\r\n\t\t\t\tbackground: #ffffff;\r\n\t\t\t\tpadding: 24rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.phone-tip {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.cancel-btn {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "getCode", "login", "setStorageSync", "loginWx", "res", "onUnmounted", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;AAyEA,UAAM,mBAAmBA,cAAAA,IAAI,CAAC;AAC9B,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAG3B,UAAM,iBAAiB,MAAM;AAC5B,UAAI;AACH,cAAM,iBAAiBC,oBAAI;AAC3B,yBAAiB,QAAQ,eAAe;AACxC,sBAAc,QAAQ,eAAe;AAAA,MACrC,SAAQ,OAAO;AACfA,0EAAc,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAGA,UAAM,aAAa,MAAM;AACxBA,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,QACP,MAAM,MAAM;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAK;AAAA,UACT,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,YAAYD,cAAAA,IAAI;AAAA,MACrB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,IACT,CAAC;AAGD,UAAM,aAAaA,cAAG,IAAC,OAAO;AAC9B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAChC,QAAI,oBAAoB;AACxB,UAAM,YAAY,YAAY;AAC7B,UAAI,UAAU,MAAM,gBAAgB,MAAM,UAAU,MAAM,YAAY,WAAW,IAAI;AACpFC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AACD,qBAAe,QAAQ;AACvB,UAAI,WAAW;AACf,iBAAW,QAAQ,IAAI,QAAQ;AAC/B,0BAAoB,YAAY,MAAM;AACrC;AACA,YAAI,WAAW,GAAG;AACjB,qBAAW,QAAQ,IAAI,QAAQ;AAAA,QAClC,OAAS;AACN,wBAAc,iBAAiB;AAC/B,qBAAW,QAAQ;AACnB,yBAAe,QAAQ;AAAA,QACvB;AAAA,MACD,GAAE,GAAI;AACP,YAAMC,kBAAQ;AAAA,QACb,aAAa,UAAU,MAAM;AAAA,MAC/B,CAAE;AACDD,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AAAA,IACF;AAGA,UAAM,UAAUD,cAAG,IAAC,KAAK;AAGzB,UAAM,kBAAkB,MAAM;AAC7B,cAAQ,QAAQ,CAAC,QAAQ;AAAA,IAC1B;AAGA,UAAM,gBAAgB,MAAM;AAC3BC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGA,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,mBAAmB,MAAM;AAC9B,UAAI,CAAC,QAAQ,OAAO;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AACD,aAAO;AAAA,IACR;AAEA,UAAM,aAAa,YAAY;AAC9B,UAAI,UAAU,MAAM,gBAAgB,MAAM,UAAU,MAAM,YAAY,WAAW,MAC7E,UAAU,MAAM,YAAY,MAAM,UAAU,MAAM,QAAQ,WAAW,GAAG;AAC3EA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAED,UAAI,CAAC,iBAAgB;AAAI;AAEzB,YAAM,WAAW,MAAM,UAAW;AAClC,gBAAU,MAAM,SAAS,SAAS;AAClC,UAAI,CAAC,UAAU,MAAM,QAAQ;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD,UAAI;AACH,cAAM,MAAM,MAAME,gBAAM,UAAU,KAAK;AACvCC,oBAAAA,eAAe,SAAS,IAAI,KAAK,KAAK;AACtCA,oBAAAA,eAAe,UAAU,IAAI,KAAK,MAAM;AACxCH,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AACD,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAK;AAAA,UACT,CAAI;AAAA,QACD,GAAE,IAAI;AAAA,MACP,SAAQ,OAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM;AAAA,UACb,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,oBAAoB,OAAO,MAAM;AACtC;AACA,UAAI,EAAE,OAAO,WAAW,qBAAqB;AAC5C,cAAM,YAAY,EAAE,OAAO;AAC3BA,sBAAAA,MAAI,MAAM;AAAA,UACT,UAAU;AAAA,UACV,SAAS,CAAC,QAAQ;AACjB,kBAAM,OAAO,IAAI;AACjB,kBAAM,SAAS;AAAA,cACd,QAAQ;AAAA,cACR;AAAA,YACA;AACDA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AACDI,sBAAAA,QAAQ,MAAM,EAAE,KAAK,CAAAC,SAAO;AAE3BF,0BAAAA,eAAe,SAASE,KAAI,KAAK,KAAK;AACtCF,0BAAAA,eAAe,UAAUE,KAAI,KAAK,MAAM;AACxCL,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cAChB,CAAM;AACD,yBAAW,MAAM;AAChBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,KAAK;AAAA,gBACZ,CAAO;AAAA,cACD,GAAE,IAAI;AAAA,YACZ,CAAK,EAAE,MAAM,SAAO;AACfA,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACN,CAAK;AAAA,UACD;AAAA,UACD,MAAM,CAAC,QAAQ;AACdA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACH,OAAQ;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,YAAY,MAAM;AACvB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,sBAAAA,MAAI,MAAM;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,MAAM;AAAA,QACT,CAAG;AAAA,MACH,CAAE;AAAA,IACF;AAGAM,kBAAAA,YAAY,MAAM;AACjB,UAAI,mBAAmB;AACtB,sBAAc,iBAAiB;AAAA,MAC/B;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACf;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzSD,GAAG,WAAW,eAAe;"}