{"version": 3, "file": "mine.js", "sources": ["pages/mine/mine.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWluZS9taW5lLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"mine-container\">\r\n        <!-- 头部卡片 -->\r\n        <view class=\"user-info-section\">\r\n            <!-- 用户信息区域 -->\r\n            <view class=\"user-info\" @tap=\"routeEdit\">\r\n                <image class=\"avatar\" :src=\"userInfo && userInfo.img ? userInfo.img : '/static/mine/avatar.png'\"\r\n                    mode=\"aspectFill\">\r\n                </image>\r\n                <view class=\"user-detail\">\r\n                    <view class=\"nickname\">{{ userInfo && userInfo.nickName ? userInfo.nickName : '未登录' }}</view>\r\n                    <view class=\"user-tags\" v-if=\"specialUser && specialUser.userType\">\r\n                        <view class=\"tag\" :class=\"getTagClass(specialUser.userType)\">\r\n                            {{ judgeUserType(specialUser.userType) }}\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <u-icon name=\"arrow-right\" size=\"24\" color=\"#666666\" class=\"arrow-right\"></u-icon>\r\n            </view>\r\n            <!-- 充电账户信息 -->\r\n            <view class=\"account-card\">\r\n                <view class=\"card-content\">\r\n                    <view class=\"left\">\r\n                        <view class=\"title\">累计充电总金额</view>\r\n                        <view class=\"amount\">\r\n                            <text class=\"symbol\">￥</text>\r\n                            <text class=\"number\">0.00</text>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"right\">\r\n                        <view class=\"view-btn\" @click=\"showDevelopingTip\">查看</view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 功能列表 -->\r\n        <view class=\"function-list\">\r\n            <!-- 车辆管理 -->\r\n            <view class=\"section-title\">车辆管理</view>\r\n            <view class=\"car-card\" @click=\"routePage('/pages/myCar/myCar')\">\r\n                <image src=\"/static/image/carLeft.png\" mode=\"widthFix\" class=\"car-image\"></image>\r\n                <view class=\"car-info\">\r\n                    <view class=\"label\">我的车辆</view>\r\n                    <view class=\"plate-number\">{{ defaultCar == null ? '--' : defaultCar }}</view>\r\n                </view>\r\n                <view class=\"manage-btn\">去管理</view>\r\n            </view>\r\n\r\n            <!-- 其他功能 -->\r\n            <view class=\"section-title\">其他功能</view>\r\n            <view class=\"grid-list\">\r\n                <view class=\"grid-item\" v-for=\"(item, index) in functionList\" :key=\"index\"\r\n                    @click=\"handleFunctionClick(item)\">\r\n                    <image :src=\"item.icon\" mode=\"aspectFit\" class=\"icon\"></image>\r\n                    <text class=\"name\">{{ item.name }}</text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <custom-tab-bar></custom-tab-bar>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad, onShow } from '@dcloudio/uni-app';\r\nimport { getCarList } from '@/api/car';\r\nimport { getSpecialUser } from '@/api/specialUser';\r\nimport CustomTabBar from \"@/components/custom-tab-bar/index.vue\";\r\n\r\nconst userInfo = ref(null);\r\nconst isLogin = ref(false);\r\nconst defaultCar = ref(null);\r\nconst specialUser = ref({});\r\n\r\n// 初始化用户数据\r\nconst initUserData = () => {\r\n    userInfo.value = uni.getStorageSync('wxUser');\r\n    console.log(userInfo.value);\r\n    if (uni.getStorageSync('token')) {\r\n        isLogin.value = true;\r\n    } else {\r\n        isLogin.value = false;\r\n    }\r\n    if (isLogin.value) {\r\n        // 获取车辆列表\r\n        getCarList().then(res => {\r\n            console.log(res);\r\n            defaultCar.value = res.data?.length > 0 ? res.data[0].plateNo : '--';\r\n        });\r\n        \r\n        // 获取用户类型信息\r\n        getSpecialUser().then(res => {\r\n            specialUser.value = res.data || {};\r\n            console.log('specialUser:', specialUser.value);\r\n        });\r\n    } else {\r\n        // 未登录时重置数据\r\n        defaultCar.value = '--';\r\n        specialUser.value = {};\r\n    }\r\n};\r\n\r\nonShow(() => {\r\n    initUserData();\r\n});\r\n\r\nconst judgeUserType = (userType) => {\r\n    if (userType === 'VIP客户') {\r\n        return 'VIP客户';\r\n    } else if (userType === '集团客户') {\r\n        return '集团客户';\r\n    } else {\r\n        return '普通用户';\r\n    }\r\n};\r\n\r\n// 根据用户类型返回对应的标签样式类名\r\nconst getTagClass = (userType) => {\r\n    if (userType === 'VIP客户') {\r\n        return 'tag-vip';        // VIP客户\r\n    } else if (userType === '集团客户') {\r\n        return 'tag-enterprise';  // 集团客户\r\n    } else {\r\n        return 'tag-normal';      // 普通用户\r\n    }\r\n};\r\n\r\n// 功能列表\r\nconst functionList = ref([\r\n    // {\r\n    //     name: '充电订单',\r\n    //     icon: '/static/mine/chargeOrder.png',\r\n    //     path: '/pages/chargeOrder'\r\n    // },\r\n     {\r\n         name: '抬头管理',\r\n        icon: '/static/mine/invoiceTitle​.png',\r\n         path: '/pages/invoice/invoiceTitle'\r\n     },\r\n     {\r\n         name: '停车发票',\r\n         icon: '/static/mine/invoiceManage.png',\r\n         path: '/pages/invoice/invoiceManage'\r\n     },\r\n    // {\r\n    //     name: '充电开票',\r\n    //     icon: '/static/mine/chargeInvoice.png',\r\n    //     path: '/pages/chargeInvoice'\r\n    // },\r\n    // {\r\n    //     name: '帮人购买',\r\n    //     icon: '/static/mine/vipGift.png',\r\n    //     path: '/pages/vipGift'\r\n    // },\r\n    // {\r\n    //     name: '我的卡包',\r\n    //     icon: '/static/mine/​​couponMine.png',\r\n    //     path: '/pages/couponMine'\r\n    // },\r\n    // {\r\n    //     name: '用户设置',\r\n    //     icon: '/static/mine/userSet.png',\r\n    //     path: '/pages/userSet'\r\n    // },\r\n    {\r\n        name: '临停订单',\r\n        icon: '/static/mine/chargeOrder.png',\r\n        path: '/pages/parkingOrder/parkingOrder'\r\n    },\r\n    {\r\n        name: '停车套餐订单',\r\n        icon: '/static/mine/chargeOrder.png',\r\n        path: '/pages/package/packageRecord'\r\n    },\r\n    {\r\n        name: '退出登录',\r\n        icon: '/static/mine/logout.png',\r\n        action: 'logout'\r\n    }\r\n]);\r\n\r\n// 页面跳转\r\nconst routePage = (url) => {\r\n    uni.navigateTo({\r\n        url: url\r\n    });\r\n};\r\n\r\n// 显示开发中提示\r\nconst showDevelopingTip = () => {\r\n    uni.showToast({\r\n        title: '开发中...',\r\n        icon: 'none',\r\n        duration: 2000\r\n    });\r\n};\r\n\r\n// 跳转到登录/编辑页面\r\nconst routeEdit = () => {\r\n    uni.navigateTo({\r\n        url: '/pages/mine/mineEdit'\r\n    });\r\n};\r\n\r\n// 处理功能点击\r\nconst handleFunctionClick = (item) => {\r\n    if (item.action === 'logout') {\r\n        if (isLogin.value) {\r\n            // 已登录用户的退出逻辑\r\n            uni.showModal({\r\n                content: '确定要退出登录吗？',\r\n                cancelText: '取消',\r\n                confirmText: '确认',\r\n                success: res => {\r\n                    if (res.confirm) {\r\n                        uni.removeStorageSync(\"token\");\r\n                        uni.removeStorageSync(\"wxUser\");\r\n                        uni.removeStorageSync(\"currentWarehouse\");\r\n                        uni.showToast({\r\n                            title: '退出登录成功',\r\n                            duration: 2000\r\n                        });\r\n                        // 重新初始化用户数据\r\n                        initUserData();\r\n                    }\r\n                }\r\n            });\r\n        } else {\r\n            // 未登录用户的提示逻辑\r\n            uni.showModal({\r\n                title: '温馨提示',\r\n                content: '您还未登录，是否先去登录？',\r\n                cancelText: '取消',\r\n                confirmText: '确定',\r\n                success: res => {\r\n                    if (res.confirm) {\r\n                        uni.navigateTo({\r\n                            url: '/pages/login/login'\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        }\r\n    } else if (item.path) {\r\n        routePage(item.path);\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mine-container {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n    padding: 24rpx 24rpx;\r\n}\r\n\r\n.user-info-section {\r\n    border-radius: 24rpx;\r\n    background-color: #fff;\r\n    padding: 28rpx;\r\n    margin-bottom: 24rpx;\r\n    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .user-info {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 20rpx 0;\r\n\r\n        .avatar {\r\n            width: 120rpx;\r\n            height: 120rpx;\r\n            border-radius: 50%;\r\n            border: 4rpx solid #fff;\r\n            margin-right: 24rpx;\r\n        }\r\n\r\n        .user-detail {\r\n            .nickname {\r\n                font-size: 36rpx;\r\n                color: #000000;\r\n                margin-bottom: 12rpx;\r\n            }\r\n\r\n            .user-tags {\r\n                padding-top: 10rpx;\r\n                display: flex;\r\n\r\n                .tag {\r\n                    padding: 6rpx 16rpx;\r\n                    border-radius: 24rpx;\r\n                    font-size: 24rpx;\r\n                    \r\n                    // 普通用户 - 蓝色\r\n                    &.tag-normal {\r\n                        background-color: #E3F2FD;\r\n                        color: #1976D2;\r\n                    }\r\n                    \r\n                    // 集团客户 - 橙色\r\n                    &.tag-enterprise {\r\n                        background-color: #FFF3E0;\r\n                        color: #F57C00;\r\n                    }\r\n                    \r\n                    // VIP客户 - 金色\r\n                    &.tag-vip {\r\n                        background-color: #FFF8E1;\r\n                        color: #F9A825;\r\n                    }\r\n                    \r\n                    // 未知类型 - 灰色\r\n                    &.tag-unknown {\r\n                        background-color: #F5F5F5;\r\n                        color: #757575;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-right {\r\n            margin-left: auto;\r\n        }\r\n    }\r\n}\r\n\r\n.account-card {\r\n    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);\r\n    border-radius: 24rpx;\r\n    padding: 24rpx;\r\n    color: #fff;\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .card-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        .left {\r\n            .title {\r\n                font-size: 28rpx;\r\n                margin-bottom: 16rpx;\r\n            }\r\n\r\n            .amount {\r\n                .symbol {\r\n                    font-size: 32rpx;\r\n                }\r\n\r\n                .number {\r\n                    font-size: 48rpx;\r\n                    font-weight: 500;\r\n                }\r\n            }\r\n        }\r\n\r\n        .right {\r\n            .view-btn {\r\n                background: rgba(255, 255, 255, 0.2);\r\n                padding: 12rpx 32rpx;\r\n                border-radius: 32rpx;\r\n                font-size: 28rpx;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.function-list {\r\n    background: #fff;\r\n    border-radius: 24rpx;\r\n    padding: 32rpx;\r\n\r\n    .section-title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 24rpx;\r\n    }\r\n\r\n    .car-card {\r\n        background: #eaf3ff;\r\n        border-radius: 16rpx;\r\n        padding: 24rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 32rpx;\r\n\r\n        .car-image {\r\n            width: 140rpx;\r\n        }\r\n\r\n        .car-info {\r\n            margin-left: 50rpx;\r\n            .label {\r\n                font-size: 28rpx;\r\n                color: #666666;\r\n                margin-bottom: 12rpx;\r\n            }\r\n\r\n            .plate-number {\r\n                font-size: 32rpx;\r\n                font-weight: bold;\r\n                color: #333;\r\n            }\r\n        }\r\n\r\n        .manage-btn {\r\n            background: linear-gradient(90deg, #4BA1FC 0%, hsl(240, 100%, 78%) 100%);\r\n            color: #fff;\r\n            padding: 12rpx 32rpx;\r\n            border-radius: 32rpx;\r\n            font-size: 28rpx;\r\n            margin-left: auto;\r\n        }\r\n    }\r\n\r\n    .grid-list {\r\n        display: grid;\r\n        grid-template-columns: repeat(4, 1fr);\r\n        gap: 32rpx;\r\n\r\n        .grid-item {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            gap: 12rpx;\r\n            padding: 16rpx 0;\r\n            border-radius: 12rpx;\r\n            transition: all 0.2s ease;\r\n\r\n            &:active {\r\n                background-color: #f5f5f5;\r\n            }\r\n            \r\n            .icon {\r\n                width: 64rpx;\r\n                height: 64rpx;\r\n                transition: transform 0.2s ease;\r\n            }\r\n\r\n            .name {\r\n                font-size: 24rpx;\r\n                color: #333;\r\n                transition: color 0.2s ease;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/mine/mine.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "getCarList", "getSpecialUser", "onShow"], "mappings": ";;;;;;;;;;;;;AAqEA,MAAM,eAAe,MAAW;;;;AAEhC,UAAM,WAAWA,cAAAA,IAAI,IAAI;AACzB,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,aAAaA,cAAAA,IAAI,IAAI;AAC3B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,eAAe,MAAM;AACvB,eAAS,QAAQC,cAAAA,MAAI,eAAe,QAAQ;AAC5CA,oBAAA,MAAA,MAAA,OAAA,6BAAY,SAAS,KAAK;AAC1B,UAAIA,cAAG,MAAC,eAAe,OAAO,GAAG;AAC7B,gBAAQ,QAAQ;AAAA,MACxB,OAAW;AACH,gBAAQ,QAAQ;AAAA,MACnB;AACD,UAAI,QAAQ,OAAO;AAEfC,2BAAY,EAAC,KAAK,SAAO;;AACrBD,wBAAAA,MAAY,MAAA,OAAA,6BAAA,GAAG;AACf,qBAAW,UAAQ,SAAI,SAAJ,mBAAU,UAAS,IAAI,IAAI,KAAK,CAAC,EAAE,UAAU;AAAA,QAC5E,CAAS;AAGDE,uCAAgB,EAAC,KAAK,SAAO;AACzB,sBAAY,QAAQ,IAAI,QAAQ,CAAA;AAChCF,wBAAY,MAAA,MAAA,OAAA,6BAAA,gBAAgB,YAAY,KAAK;AAAA,QACzD,CAAS;AAAA,MACT,OAAW;AAEH,mBAAW,QAAQ;AACnB,oBAAY,QAAQ;MACvB;AAAA,IACL;AAEAG,kBAAAA,OAAO,MAAM;AACT;IACJ,CAAC;AAED,UAAM,gBAAgB,CAAC,aAAa;AAChC,UAAI,aAAa,SAAS;AACtB,eAAO;AAAA,MACf,WAAe,aAAa,QAAQ;AAC5B,eAAO;AAAA,MACf,OAAW;AACH,eAAO;AAAA,MACV;AAAA,IACL;AAGA,UAAM,cAAc,CAAC,aAAa;AAC9B,UAAI,aAAa,SAAS;AACtB,eAAO;AAAA,MACf,WAAe,aAAa,QAAQ;AAC5B,eAAO;AAAA,MACf,OAAW;AACH,eAAO;AAAA,MACV;AAAA,IACL;AAGA,UAAM,eAAeJ,cAAAA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMpB;AAAA,QACI,MAAM;AAAA,QACP,MAAM;AAAA,QACL,MAAM;AAAA,MACT;AAAA,MACD;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBF;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACT;AAAA,MACD;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACT;AAAA,MACD;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACX;AAAA,IACL,CAAC;AAGD,UAAM,YAAY,CAAC,QAAQ;AACvBC,oBAAAA,MAAI,WAAW;AAAA,QACX;AAAA,MACR,CAAK;AAAA,IACL;AAGA,UAAM,oBAAoB,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAK;AAAA,IACL;AAGA,UAAM,YAAY,MAAM;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,MACb,CAAK;AAAA,IACL;AAGA,UAAM,sBAAsB,CAAC,SAAS;AAClC,UAAI,KAAK,WAAW,UAAU;AAC1B,YAAI,QAAQ,OAAO;AAEfA,wBAAAA,MAAI,UAAU;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,SAAO;AACZ,kBAAI,IAAI,SAAS;AACbA,oCAAI,kBAAkB,OAAO;AAC7BA,oCAAI,kBAAkB,QAAQ;AAC9BA,oCAAI,kBAAkB,kBAAkB;AACxCA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,UAAU;AAAA,gBACtC,CAAyB;AAED;cACH;AAAA,YACJ;AAAA,UACjB,CAAa;AAAA,QACb,OAAe;AAEHA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,SAAO;AACZ,kBAAI,IAAI,SAAS;AACbA,8BAAAA,MAAI,WAAW;AAAA,kBACX,KAAK;AAAA,gBACjC,CAAyB;AAAA,cACJ;AAAA,YACJ;AAAA,UACjB,CAAa;AAAA,QACJ;AAAA,MACT,WAAe,KAAK,MAAM;AAClB,kBAAU,KAAK,IAAI;AAAA,MACtB;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvPA,GAAG,WAAW,eAAe;"}