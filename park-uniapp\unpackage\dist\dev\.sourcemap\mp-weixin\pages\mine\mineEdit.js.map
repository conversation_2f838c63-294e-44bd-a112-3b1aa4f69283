{"version": 3, "file": "mineEdit.js", "sources": ["pages/mine/mineEdit.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWluZS9taW5lRWRpdC52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"mine-edit-container\">\r\n        <!-- 用户信息编辑卡片 -->\r\n        <view class=\"edit-card\">\r\n            <!-- 头像编辑 -->\r\n            <view class=\"edit-item-avatar\">\r\n                <view class=\"edit-label-avatar\">我的头像</view>\r\n                <view class=\"avatar-wrapper\">\r\n                    <button class=\"avatar-btn\" open-type=\"chooseAvatar\" @chooseavatar=\"chooseavatar\">\r\n                        <image class=\"avatar-image\" :src=\"userEdit.img || '/static/mine/avatar.png'\" mode=\"aspectFill\">\r\n                        </image>\r\n                    </button>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 昵称编辑 -->\r\n            <view class=\"edit-item-horizontal\">\r\n                <view class=\"edit-label-horizontal\">我的昵称</view>\r\n                <view class=\"content-wrapper\">\r\n                    <input type=\"nickname\" v-model=\"userEdit.nickName\" placeholder=\"请输入昵称\" maxlength=\"12\"\r\n                        class=\"content-text\" />\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 手机号码编辑 -->\r\n            <view class=\"edit-item-horizontal\">\r\n                <view class=\"edit-label-horizontal\">手机号码</view>\r\n                <view class=\"phone-wrapper\">\r\n                    <!-- 普通用户可以点击修改 -->\r\n                    <template v-if=\"userEdit.userType === 0 || userEdit.userType === '0'\">\r\n                        <button class=\"phone-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\"\r\n                            @tap.stop=\"\">\r\n                            <text class=\"phone-text\">{{ userEdit.phoneNumber || '请选择手机号码' }}</text>\r\n                        </button>\r\n                    </template>\r\n                    <!-- 集团客户和VIP用户只显示文本 -->\r\n                    <template v-else>\r\n                        <text class=\"phone-text\" @tap=\"handlePhoneEdit\">{{ userEdit.phoneNumber || '请选择手机号码' }}</text>\r\n                    </template>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 保存按钮 -->\r\n        <view class=\"save-section\">\r\n            <button @tap=\"handleSave\" class=\"save-btn\">保存</button>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { ref } from 'vue';\r\nimport { getPhoneNumberByCode, updateUserInfo } from '@/api/user';\r\nimport { uploadAvatar } from '@/utils/utils';\r\n\r\nconst userInfo = ref(null);\r\nconst isLogin = ref(false);\r\nconst isAvatarChanged = ref(false); // 标记头像是否被修改\r\n\r\nconst userEdit = ref({\r\n    img: '',\r\n    nickName: '',\r\n    phoneNumber: '',\r\n    userType: 0\r\n});\r\n\r\nonLoad(() => {\r\n    userInfo.value = uni.getStorageSync('wxUser');\r\n\r\n    // 初始化编辑数据\r\n    if (userInfo.value) {\r\n        userEdit.value.img = userInfo.value.img || '';\r\n        userEdit.value.nickName = userInfo.value.nickName || '';\r\n        userEdit.value.phoneNumber = userInfo.value.phoneNumber || '';\r\n        userEdit.value.userType = userInfo.value.userType || 0;  \r\n    }\r\n\r\n    if (uni.getStorageSync('token')) {\r\n        isLogin.value = true;\r\n    }\r\n});\r\n\r\n// 选择头像\r\nconst chooseavatar = (e) => {\r\n    console.log(e);\r\n    // 获取选择的头像URL\r\n    const avatarUrl = e.detail.avatarUrl;\r\n    if (avatarUrl) {\r\n        userEdit.value.img = avatarUrl;\r\n        isAvatarChanged.value = true; // 标记头像已被修改\r\n    }\r\n};\r\n\r\n// 获取手机号码\r\nconst getPhoneNumber = (e) => {\r\n    const phoneCode = e.detail.code;\r\n    if (phoneCode) {\r\n        getPhoneNumberByCode(phoneCode).then(res => {\r\n            userEdit.value.phoneNumber = res.data\r\n        })\r\n    } else {\r\n        console.log('验证手机号失败')\r\n    }\r\n};\r\n\r\n// 处理手机号编辑（非普通用户）\r\nconst handlePhoneEdit = () => {\r\n    const userType = userEdit.value.userType;\r\n    \r\n    if (userType === 1) {\r\n        // 集团客户\r\n        uni.showModal({\r\n            title: '温馨提示',\r\n            content: '集团客户修改手机号，请联系客服',\r\n            showCancel: false,\r\n            confirmText: '我知道了'\r\n        });\r\n    } else if (userType === 2) {\r\n        // VIP用户\r\n        uni.showModal({\r\n            title: '温馨提示',\r\n            content: 'VIP客户修改手机号，请联系客服',\r\n            showCancel: false,\r\n            confirmText: '我知道了'\r\n        });\r\n    } else {\r\n        // 未知用户类型，默认不允许修改\r\n        uni.showModal({\r\n            title: '温馨提示',\r\n            content: '当前用户类型无法修改手机号，请联系客服',\r\n            showCancel: false,\r\n            confirmText: '我知道了'\r\n        });\r\n    }\r\n};\r\n\r\n// 保存用户信息\r\nconst handleSave = async () => {\r\n    // 获取系统缓存中的用户信息\r\n    const cachedUserInfo = uni.getStorageSync('wxUser') || {};\r\n\r\n    // 检查是否有变化\r\n    const hasChanges = (\r\n        (userEdit.value.img && userEdit.value.img !== cachedUserInfo.img) ||\r\n        userEdit.value.nickName !== cachedUserInfo.nickName ||\r\n        userEdit.value.phoneNumber !== cachedUserInfo.phoneNumber\r\n    );\r\n\r\n    if (!hasChanges) {\r\n        uni.showToast({\r\n            title: '信息未更新',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n        return;\r\n    }\r\n\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: '保存中...',\r\n        mask: true\r\n    });\r\n\r\n    try {\r\n        // 如果头像被修改且是临时文件，先上传头像\r\n        let finalImgUrl = userEdit.value.img;\r\n        if (isAvatarChanged.value && userEdit.value.img && (userEdit.value.img.startsWith('wxfile://') || userEdit.value.img.includes('tmp'))) {\r\n            try {\r\n                // 上传头像文件\r\n                const uploadResult = await uploadAvatar(userEdit.value.img);\r\n                finalImgUrl = uploadResult;\r\n                console.log('头像上传成功:', finalImgUrl);\r\n            } catch (error) {\r\n                console.error('头像上传失败:', error);\r\n                uni.hideLoading();\r\n                uni.showToast({\r\n                    title: '头像上传失败',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                });\r\n                return;\r\n            }\r\n        }\r\n\r\n        // 准备保存的数据\r\n        const saveData = {\r\n            img: finalImgUrl,\r\n            nickName: userEdit.value.nickName,\r\n            phoneNumber: userEdit.value.phoneNumber,\r\n            userName: userEdit.value.phoneNumber\r\n        };\r\n        console.log(saveData);\r\n\r\n        // 这里调用后端接口更新用户信息\r\n        const res = await updateUserInfo(saveData);\r\n\r\n        uni.setStorageSync('token', res.data.token)\r\n        uni.setStorageSync('wxUser', res.data.wxUser)\r\n        uni.hideLoading();\r\n        \r\n        // 重置头像修改标记\r\n        isAvatarChanged.value = false;\r\n\r\n        uni.showToast({\r\n            title: '保存成功',\r\n            icon: 'success',\r\n            duration: 1500\r\n        });\r\n\r\n        // 延迟返回上一页\r\n        setTimeout(() => {\r\n            uni.navigateBack();\r\n        }, 1500);\r\n\r\n    } catch (error) {\r\n        console.error('保存失败:', error);\r\n        uni.hideLoading();\r\n        \r\n        // 显示后端返回的具体错误信息\r\n        const errorMsg = error.msg || error.message || '保存失败，请重试';\r\n        uni.showToast({\r\n            title: errorMsg,\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mine-edit-container {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n    padding: 24rpx;\r\n}\r\n\r\n// 编辑卡片\r\n.edit-card {\r\n    background-color: #fff;\r\n    border-radius: 24rpx;\r\n    padding: 20rpx 36rpx 20rpx 28rpx;\r\n    margin-bottom: 24rpx;\r\n}\r\n\r\n// 头像编辑\r\n.edit-item-avatar {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    min-height: 120rpx;\r\n    padding: 16rpx 0;\r\n    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);\r\n\r\n    .edit-label-avatar {\r\n        font-size: 32rpx;\r\n        color: #000000;\r\n    }\r\n\r\n    .avatar-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12rpx;\r\n    }\r\n\r\n    .avatar-btn {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        border: none;\r\n        background: transparent;\r\n        padding: 0;\r\n        margin: 0;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n\r\n        &::after {\r\n            border: none;\r\n        }\r\n    }\r\n\r\n    .avatar-image {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid #f0f0f0;\r\n    }\r\n}\r\n\r\n// 左右布局的编辑项\r\n.edit-item-horizontal {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    min-height: 120rpx;\r\n    padding: 16rpx 0;\r\n    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);\r\n\r\n    .edit-label-horizontal {\r\n        font-size: 32rpx;\r\n        color: #000000;\r\n        flex-shrink: 0;\r\n        min-width: 160rpx;\r\n    }\r\n\r\n    .content-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        flex: 1;\r\n        margin-left: 20rpx;\r\n        gap: 12rpx;\r\n    }\r\n\r\n    .content-text {\r\n        font-size: 32rpx;\r\n        color: #000000;\r\n        text-align: right;\r\n        border: none;\r\n        background: transparent;\r\n        flex: 1;\r\n        \r\n        &::placeholder {\r\n            color: #999999;\r\n        }\r\n    }\r\n\r\n    .phone-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        flex: 1;\r\n        margin-left: 20rpx;\r\n        gap: 12rpx;\r\n    }\r\n\r\n    .phone-btn {\r\n        background: transparent;\r\n        padding: 0;\r\n        margin: 0;\r\n        font-size: 32rpx;\r\n        color: #333333;\r\n        line-height: 42rpx;\r\n        border: none;\r\n        text-align: right;\r\n\r\n        &::after {\r\n            border: none;\r\n        }\r\n\r\n        .phone-text {\r\n            color: #000000;\r\n        }\r\n    }\r\n\r\n    .phone-text {\r\n        color: #000000;\r\n        font-size: 32rpx;\r\n        text-align: right;\r\n    }\r\n}\r\n\r\n\r\n\r\n.save-section {\r\n    padding-top: 20rpx;\r\n}\r\n\r\n.save-btn {\r\n    width: 100%;\r\n    height: 88rpx;\r\n    // background: linear-gradient(135deg, #246bfd 0%, #6f9eff 100%);\r\n    background: linear-gradient(90deg, #4BA1FC 0%, hsl(240, 100%, 78%) 100%);\r\n    color: #fff;\r\n    border-radius: 44rpx;\r\n    font-size: 32rpx;\r\n    border: none;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/mine/mineEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getPhoneNumberByCode", "uploadAvatar", "updateUserInfo"], "mappings": ";;;;;;;AAwDA,UAAM,WAAWA,cAAAA,IAAI,IAAI;AACzB,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAEjC,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACjB,KAAK;AAAA,MACL,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,IACd,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AACT,eAAS,QAAQC,cAAAA,MAAI,eAAe,QAAQ;AAG5C,UAAI,SAAS,OAAO;AAChB,iBAAS,MAAM,MAAM,SAAS,MAAM,OAAO;AAC3C,iBAAS,MAAM,WAAW,SAAS,MAAM,YAAY;AACrD,iBAAS,MAAM,cAAc,SAAS,MAAM,eAAe;AAC3D,iBAAS,MAAM,WAAW,SAAS,MAAM,YAAY;AAAA,MACxD;AAED,UAAIA,cAAG,MAAC,eAAe,OAAO,GAAG;AAC7B,gBAAQ,QAAQ;AAAA,MACnB;AAAA,IACL,CAAC;AAGD,UAAM,eAAe,CAAC,MAAM;AACxBA,oBAAAA,oDAAY,CAAC;AAEb,YAAM,YAAY,EAAE,OAAO;AAC3B,UAAI,WAAW;AACX,iBAAS,MAAM,MAAM;AACrB,wBAAgB,QAAQ;AAAA,MAC3B;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC1B,YAAM,YAAY,EAAE,OAAO;AAC3B,UAAI,WAAW;AACXC,iBAAAA,qBAAqB,SAAS,EAAE,KAAK,SAAO;AACxC,mBAAS,MAAM,cAAc,IAAI;AAAA,QAC7C,CAAS;AAAA,MACT,OAAW;AACHD,sBAAAA,MAAY,MAAA,OAAA,kCAAA,SAAS;AAAA,MACxB;AAAA,IACL;AAGA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,WAAW,SAAS,MAAM;AAEhC,UAAI,aAAa,GAAG;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,QACzB,CAAS;AAAA,MACT,WAAe,aAAa,GAAG;AAEvBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,QACzB,CAAS;AAAA,MACT,OAAW;AAEHA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,QACzB,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,aAAa,YAAY;AAE3B,YAAM,iBAAiBA,cAAG,MAAC,eAAe,QAAQ,KAAK,CAAA;AAGvD,YAAM,aACD,SAAS,MAAM,OAAO,SAAS,MAAM,QAAQ,eAAe,OAC7D,SAAS,MAAM,aAAa,eAAe,YAC3C,SAAS,MAAM,gBAAgB,eAAe;AAGlD,UAAI,CAAC,YAAY;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AAED,UAAI;AAEA,YAAI,cAAc,SAAS,MAAM;AACjC,YAAI,gBAAgB,SAAS,SAAS,MAAM,QAAQ,SAAS,MAAM,IAAI,WAAW,WAAW,KAAK,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI;AACnI,cAAI;AAEA,kBAAM,eAAe,MAAME,YAAY,aAAC,SAAS,MAAM,GAAG;AAC1D,0BAAc;AACdF,0BAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,WAAW;AAAA,UACrC,SAAQ,OAAO;AACZA,0BAAc,MAAA,MAAA,SAAA,kCAAA,WAAW,KAAK;AAC9BA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AACD;AAAA,UACH;AAAA,QACJ;AAGD,cAAM,WAAW;AAAA,UACb,KAAK;AAAA,UACL,UAAU,SAAS,MAAM;AAAA,UACzB,aAAa,SAAS,MAAM;AAAA,UAC5B,UAAU,SAAS,MAAM;AAAA,QACrC;AACQA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,QAAQ;AAGpB,cAAM,MAAM,MAAMG,wBAAe,QAAQ;AAEzCH,sBAAAA,MAAI,eAAe,SAAS,IAAI,KAAK,KAAK;AAC1CA,sBAAAA,MAAI,eAAe,UAAU,IAAI,KAAK,MAAM;AAC5CA,sBAAG,MAAC,YAAW;AAGf,wBAAgB,QAAQ;AAExBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAGD,mBAAW,MAAM;AACbA,wBAAG,MAAC,aAAY;AAAA,QACnB,GAAE,IAAI;AAAA,MAEV,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,kCAAc,SAAS,KAAK;AAC5BA,sBAAG,MAAC,YAAW;AAGf,cAAM,WAAW,MAAM,OAAO,MAAM,WAAW;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAAA,MACJ;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;AClOA,GAAG,WAAW,eAAe;"}