{"version": 3, "file": "myCar.js", "sources": ["pages/myCar/myCar.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXlDYXIvbXlDYXIudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"my-car-container\">\r\n        <!-- 车辆列表 -->\r\n        <view class=\"my-car-list\">\r\n            <view class=\"my-car-item\" v-for=\"car in carList\" :key=\"car.id\">\r\n                <view class=\"my-car-content\">\r\n                    <view class=\"my-car-content-left\">\r\n                        <view class=\"plateNumber\">{{ car.plateNo }}</view>\r\n                        <view class=\"car-info\">\r\n                            <text class=\"carType\">{{ car.carType }}</text>\r\n                            <text class=\"divider\">·</text>\r\n                            <text class=\"energyType\">{{ getEnergyTypeLabel(car.energyType) }}</text>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"my-car-content-right\">\r\n                        <image src=\"/static/image/carLeft.png\" class=\"car-image\" mode=\"widthFix\"></image>\r\n                    </view>\r\n                </view>\r\n                <view class=\"my-car-item-bottom\">\r\n                    <view class=\"default-section\" @tap=\"setDefaultCar(car)\">\r\n                        <radio :checked=\"car.isDefault\" color=\"#246bfd\" class=\"radio-custom\"></radio>\r\n                        <text class=\"default-text\">设置为默认车辆</text>\r\n                    </view>\r\n                    <view class=\"action-section\">\r\n                        <u-icon name=\"edit-pen\" size=\"30\" color=\"#999\" @tap=\"editCarNav(car.id)\"></u-icon>\r\n                        <u-icon name=\"trash\" size=\"30\" color=\"#999\" @tap=\"deleteCarNav(car.id)\"></u-icon>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-if=\"carList.length === 0\">\r\n            <up-empty text=\"暂无车辆信息\" />\r\n        </view>\r\n\r\n        <!-- 底部固定的添加车辆按钮 -->\r\n        <view class=\"bottom-add-btn\" @tap=\"addCarNav\">\r\n            <text class=\"add-icon\">+</text>\r\n            <text class=\"add-text\">添加车辆</text>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, nextTick } from 'vue';\r\nimport { onLoad, onShow } from '@dcloudio/uni-app';\r\nimport { getCarList, deleteCar, editCar } from '@/api/car';\r\n\r\nconst carList = ref([]);\r\n\r\nonShow(() => {\r\n    getCarList().then(res => {\r\n        console.log(res);\r\n        // 确保数据正确更新\r\n        carList.value = [];\r\n        nextTick(() => {\r\n            carList.value = res.data;\r\n        });\r\n    });\r\n});\r\n\r\n// 能源类型选项\r\nconst energyTypeOptions = [\r\n    { label: '燃油', value: 1 },\r\n    { label: '纯电', value: 2 },\r\n    { label: '混动', value: 3 }\r\n];\r\n\r\n// 能源类型显示映射\r\nconst getEnergyTypeLabel = (value) => {\r\n    const option = energyTypeOptions.find(item => item.value === value);\r\n    return option ? option.label : '';\r\n};\r\n\r\n// 添加车辆\r\nconst addCarNav = () => {\r\n    uni.navigateTo({\r\n        url: '/pages/myCar/myCarAdd?isEdit=false'\r\n    });\r\n};\r\n\r\n// 编辑车辆\r\nconst editCarNav = (id) => {\r\n    uni.navigateTo({\r\n        url: '/pages/myCar/myCarAdd?isEdit=true&id=' + id\r\n    });\r\n};\r\n\r\n// 删除车辆\r\nconst deleteCarNav = (id) => {\r\n    console.log(id);\r\n    uni.showModal({\r\n        content: `确定要删除车辆吗？`,\r\n        success: (res) => {\r\n            if (res.confirm) {\r\n                // 显示加载提示\r\n                uni.showLoading({\r\n                    title: '删除中...'\r\n                });\r\n\r\n                deleteCar({ id: id }).then(res => {\r\n                    uni.hideLoading();\r\n                    if (res.code === 200) {\r\n                        uni.showToast({\r\n                            title: '删除成功',\r\n                            icon: 'success'\r\n                        });\r\n                        getCarList().then(res => {\r\n                            if (res.data) {\r\n                                // 先清空数组，然后重新赋值，确保触发响应式更新\r\n                                carList.value = [];\r\n                                nextTick(() => {\r\n                                    carList.value = res.data;\r\n                                });\r\n                            }\r\n                        }).catch(err => {\r\n                            console.error('重新获取车辆列表失败:', err);\r\n                        });\r\n                    } else {\r\n                        uni.showToast({\r\n                            title: res.msg || '删除失败',\r\n                            icon: 'none'\r\n                        });\r\n                    }\r\n                }).catch(err => {\r\n                    uni.hideLoading();\r\n                    console.error('删除车辆失败:', err);\r\n                    uni.showToast({\r\n                        title: '删除失败，请重试',\r\n                        icon: 'none'\r\n                    });\r\n                });\r\n            }\r\n        }\r\n    });\r\n};\r\n\r\n// 设置默认车辆\r\nconst setDefaultCar = (car) => {\r\n    if (car.isDefault) {\r\n        uni.showToast({\r\n            title: '该车已经是默认车辆',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n        title: '设置中...'\r\n    });\r\n\r\n    editCar({ id: car.id, isDefault: 1 }).then(res => {\r\n        uni.hideLoading();\r\n        if (res.code === 200) {\r\n            uni.showToast({\r\n                title: '设置成功',\r\n                icon: 'success'\r\n            });\r\n            getCarList().then(res => {\r\n                if (res.data) {\r\n                    // 先清空数组，然后重新赋值，确保触发响应式更新\r\n                    carList.value = [];\r\n                    nextTick(() => {\r\n                        carList.value = res.data;\r\n                    });\r\n                }\r\n            }).catch(err => {\r\n                console.error('重新获取车辆列表失败:', err);\r\n            });\r\n        } else {\r\n            uni.showToast({\r\n                title: res.msg || '设置失败',\r\n                icon: 'none'\r\n            });\r\n        }\r\n    }).catch(err => {\r\n        uni.hideLoading();\r\n        console.error('设置默认车辆失败:', err);\r\n        uni.showToast({\r\n            title: '设置失败，请重试',\r\n            icon: 'none'\r\n        });\r\n    });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-car-container {\r\n    background-color: #f5f5f5;\r\n    padding: 25rpx 25rpx 140rpx 25rpx;\r\n    min-height: 100vh;\r\n    position: relative;\r\n}\r\n\r\n.my-car-list {\r\n    .my-car-item {\r\n        background: #fff;\r\n        border-radius: 24rpx;\r\n        padding: 32rpx;\r\n        margin-bottom: 20rpx;\r\n        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\r\n        .my-car-content {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .my-car-content-left {\r\n                margin-right: 150rpx;\r\n\r\n                .plateNumber {\r\n                    font-size: 36rpx;\r\n                    font-weight: bold;\r\n                    color: #333;\r\n                    margin-bottom: 12rpx;\r\n                }\r\n\r\n                .car-info {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 8rpx;\r\n\r\n                    .carType,\r\n                    .energyType {\r\n                        font-size: 28rpx;\r\n                        color: #666;\r\n                    }\r\n\r\n                    .divider {\r\n                        color: #ccc;\r\n                        font-size: 24rpx;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .my-car-content-right {\r\n                .car-image {\r\n                    width: 250rpx;\r\n                    height: 140rpx;\r\n                }\r\n            }\r\n        }\r\n\r\n        .my-car-item-bottom {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            padding-top: 24rpx;\r\n            border-top: 2rpx solid #f0f0f0;\r\n\r\n            .default-section {\r\n                display: flex;\r\n                align-items: center;\r\n\r\n                .radio-custom {\r\n                    transform: scale(0.9);\r\n                }\r\n\r\n                .default-text {\r\n                    font-size: 28rpx;\r\n                    color: #666;\r\n                }\r\n            }\r\n\r\n            .action-section {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 24rpx;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 120rpx 0;\r\n\r\n    .empty-image {\r\n        width: 200rpx;\r\n        height: 200rpx;\r\n        margin-bottom: 32rpx;\r\n        opacity: 0.6;\r\n    }\r\n\r\n    .empty-text {\r\n        font-size: 32rpx;\r\n        color: #999;\r\n        margin-bottom: 48rpx;\r\n    }\r\n}\r\n\r\n/* 底部固定的添加车辆按钮 */\r\n.bottom-add-btn {\r\n    position: fixed;\r\n    bottom: 40rpx;\r\n    width: 85%;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);\r\n    color: #fff;\r\n    padding: 22rpx;\r\n    border-radius: 50rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 999;\r\n\r\n    .add-icon {\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n        margin-right: 12rpx;\r\n    }\r\n\r\n    .add-text {\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/myCar/myCar.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShow", "getCarList", "uni", "nextTick", "deleteCar", "res", "editCar"], "mappings": ";;;;;;;;;;;;;;;;;AAiDA,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAEtBC,kBAAAA,OAAO,MAAM;AACTC,yBAAY,EAAC,KAAK,SAAO;AACrBC,sBAAAA,MAAA,MAAA,OAAA,+BAAY,GAAG;AAEf,gBAAQ,QAAQ;AAChBC,sBAAAA,WAAS,MAAM;AACX,kBAAQ,QAAQ,IAAI;AAAA,QAChC,CAAS;AAAA,MACT,CAAK;AAAA,IACL,CAAC;AAGD,UAAM,oBAAoB;AAAA,MACtB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,IAC7B;AAGA,UAAM,qBAAqB,CAAC,UAAU;AAClC,YAAM,SAAS,kBAAkB,KAAK,UAAQ,KAAK,UAAU,KAAK;AAClE,aAAO,SAAS,OAAO,QAAQ;AAAA,IACnC;AAGA,UAAM,YAAY,MAAM;AACpBD,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,MACb,CAAK;AAAA,IACL;AAGA,UAAM,aAAa,CAAC,OAAO;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,0CAA0C;AAAA,MACvD,CAAK;AAAA,IACL;AAGA,UAAM,eAAe,CAAC,OAAO;AACzBA,oBAAAA,MAAA,MAAA,OAAA,+BAAY,EAAE;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AAEbA,0BAAAA,MAAI,YAAY;AAAA,cACZ,OAAO;AAAA,YAC3B,CAAiB;AAEDE,oBAAS,UAAC,EAAE,GAAQ,CAAA,EAAE,KAAK,CAAAC,SAAO;AAC9BH,4BAAG,MAAC,YAAW;AACf,kBAAIG,KAAI,SAAS,KAAK;AAClBH,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,gBAClC,CAAyB;AACDD,mCAAY,EAAC,KAAK,CAAAI,SAAO;AACrB,sBAAIA,KAAI,MAAM;AAEV,4BAAQ,QAAQ;AAChBF,kCAAAA,WAAS,MAAM;AACX,8BAAQ,QAAQE,KAAI;AAAA,oBACxD,CAAiC;AAAA,kBACJ;AAAA,gBAC7B,CAAyB,EAAE,MAAM,SAAO;AACZH,qFAAc,eAAe,GAAG;AAAA,gBAC5D,CAAyB;AAAA,cACzB,OAA2B;AACHA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAOG,KAAI,OAAO;AAAA,kBAClB,MAAM;AAAA,gBAClC,CAAyB;AAAA,cACJ;AAAA,YACrB,CAAiB,EAAE,MAAM,SAAO;AACZH,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,cAC9B,CAAqB;AAAA,YACrB,CAAiB;AAAA,UACJ;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,gBAAgB,CAAC,QAAQ;AAC3B,UAAI,IAAI,WAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,MACf,CAAK;AAEDI,sBAAQ,EAAE,IAAI,IAAI,IAAI,WAAW,EAAG,CAAA,EAAE,KAAK,SAAO;AAC9CJ,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AACDD,6BAAY,EAAC,KAAK,CAAAI,SAAO;AACrB,gBAAIA,KAAI,MAAM;AAEV,sBAAQ,QAAQ;AAChBF,4BAAAA,WAAS,MAAM;AACX,wBAAQ,QAAQE,KAAI;AAAA,cAC5C,CAAqB;AAAA,YACJ;AAAA,UACjB,CAAa,EAAE,MAAM,SAAO;AACZH,+EAAc,eAAe,GAAG;AAAA,UAChD,CAAa;AAAA,QACb,OAAe;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACtB,CAAa;AAAA,QACJ;AAAA,MACT,CAAK,EAAE,MAAM,SAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,gCAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxLA,GAAG,WAAW,eAAe;"}