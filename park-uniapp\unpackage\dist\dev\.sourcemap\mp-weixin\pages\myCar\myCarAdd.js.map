{"version": 3, "file": "myCarAdd.js", "sources": ["pages/myCar/myCarAdd.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXlDYXIvbXlDYXJBZGQudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"my-car-add-container\">\r\n        <!-- 头部区域 -->\r\n        <view class=\"header-section\">\r\n            <view class=\"header-content u-flex u-flex-y-center\">\r\n                <view class=\"header-title\">\r\n                    <view class=\"title\">{{ isEdit ? '编辑车辆信息' : '添加车辆信息' }}</view>\r\n                    <view class=\"desc\">{{ isEdit ? 'Edit Vehicle Information' : 'Add Vehicle Information' }}</view>\r\n                </view>\r\n                <image src=\"/static/image/carRight.png\" mode=\"aspectFit\" class=\"header-image\"></image>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 表单区域 -->\r\n        <view class=\"form-section\">\r\n            <!-- 车牌号 -->\r\n            <view class=\"form-item\">\r\n                <view class=\"form-label\">车牌号</view>\r\n                <view class=\"form-input\" @tap=\"showPlateInput\">\r\n                    <view class=\"plate-display\">\r\n                        {{ formData.plateNo || '请选择车牌号' }}\r\n                    </view>\r\n                    <u-icon name=\"arrow-right\" size=\"22\" color=\"#999\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 车型 -->\r\n            <view class=\"form-item\">\r\n                <view class=\"form-label\">车型</view>\r\n                <view class=\"form-input\" @tap=\"showCarTypePicker\">\r\n                    <view class=\"picker-input\">\r\n                        {{ formData.carType || '请选择车型' }}\r\n                    </view>\r\n                    <u-icon name=\"arrow-right\" size=\"22\" color=\"#999\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 能源类型 -->\r\n            <view class=\"form-item\">\r\n                <view class=\"form-label\">能源类型</view>\r\n                <view class=\"form-input\" @tap=\"showEnergyTypePicker\">\r\n                    <view class=\"picker-input\">\r\n                        {{ getEnergyTypeLabel(formData.energyType) || '请选择能源类型' }}\r\n                    </view>\r\n                    <u-icon name=\"arrow-right\" size=\"22\" color=\"#999\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 品牌 -->\r\n            <view class=\"form-item\">\r\n                <view class=\"form-label\">品牌</view>\r\n                <view class=\"form-input\">\r\n                    <input v-model=\"formData.carBrand\" placeholder=\"请输入品牌\" class=\"input-field\" maxlength=\"20\" />\r\n                    <u-icon name=\"arrow-right\" size=\"22\" color=\"#999\"></u-icon>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 底部按钮 -->\r\n        <view class=\"bottom-section\">\r\n            <view class=\"submit-btn\" @click=\"handleSubmit\">\r\n                <text class=\"submit-text\">{{ isEdit ? '保存修改' : '添加车辆' }}</text>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 车型选择器 -->\r\n        <u-picker :show=\"showCarType\" :columns=\"[carTypeOptions]\" @confirm=\"onCarTypeConfirm\"\r\n            @cancel=\"showCarType = false\"></u-picker>\r\n\r\n        <!-- 能源类型选择器 -->\r\n        <u-picker :show=\"showEnergyType\" :columns=\"[energyTypeOptions.map(item => item.label)]\"\r\n            @confirm=\"onEnergyTypeConfirm\" @cancel=\"showEnergyType = false\" :default-index=\"[0]\"></u-picker>\r\n\r\n        <!-- 车牌号输入弹窗 -->\r\n        <uni-plate-input v-if=\"showPlateInputFlag\" :plate=\"formData.plateNo\" @close=\"closePlateInput\"\r\n            @export=\"onPlateConfirm\" />\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport UniPlateInput from '@/components/uni-plate-input/uni-plate-input.vue';\r\nimport { addCar, editCar, getCarDetailById } from '@/api/car';\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n    plateNo: '',\r\n    carType: '',\r\n    energyType: 0,\r\n    carBrand: ''\r\n});\r\n\r\n// 编辑模式标识\r\nconst isEdit = ref(false);\r\n\r\nonLoad((options) => {\r\n    if (options.isEdit === 'true') {\r\n        isEdit.value = true;\r\n    }\r\n    if (options.id) {\r\n        loadCarInfo(options.id);\r\n    }\r\n});\r\n\r\n// 选择器显示状态\r\nconst showCarType = ref(false);\r\nconst showEnergyType = ref(false);\r\nconst showPlateInputFlag = ref(false);\r\n\r\n// 车型选项\r\nconst carTypeOptions = [\r\n    '微型车',\r\n    '轿车',\r\n    'SUV',\r\n    '其他'\r\n];\r\n\r\n// 能源类型选项\r\nconst energyTypeOptions = [\r\n    { label: '燃油', value: 1 },\r\n    { label: '纯电', value: 2 },\r\n    { label: '混动', value: 3 }\r\n];\r\n\r\n// 能源类型显示映射\r\nconst getEnergyTypeLabel = (value) => {\r\n    const option = energyTypeOptions.find(item => item.value === value);\r\n    return option ? option.label : '';\r\n};\r\n\r\n// 加载车辆信息\r\nconst loadCarInfo = (id) => {\r\n    getCarDetailById({ id: id }).then((res) => {\r\n        if (res.code === 200) {\r\n            formData.value = res.data;\r\n        }\r\n    });\r\n};\r\n\r\n// 显示车型选择器\r\nconst showCarTypePicker = () => {\r\n    showCarType.value = true;\r\n};\r\n\r\n// 显示能源类型选择器\r\nconst showEnergyTypePicker = () => {\r\n    showEnergyType.value = true;\r\n};\r\n\r\n// 车型选择确认\r\nconst onCarTypeConfirm = (e) => {\r\n    formData.value.carType = e.value[0];\r\n    showCarType.value = false;\r\n};\r\n\r\n// 能源类型选择确认\r\nconst onEnergyTypeConfirm = (e) => {\r\n    const selectedLabel = e.value[0];\r\n    const selectedOption = energyTypeOptions.find(item => item.label === selectedLabel);\r\n    formData.value.energyType = selectedOption ? selectedOption.value : null;\r\n    showEnergyType.value = false;\r\n};\r\n\r\n// 显示车牌号输入\r\nconst showPlateInput = () => {\r\n    showPlateInputFlag.value = true;\r\n};\r\n\r\n// 关闭车牌号输入\r\nconst closePlateInput = () => {\r\n    showPlateInputFlag.value = false;\r\n};\r\n\r\n// 车牌号确认\r\nconst onPlateConfirm = (plate) => {\r\n    formData.value.plateNo = plate;\r\n    showPlateInputFlag.value = false;\r\n};\r\n\r\n// 提交表单\r\nconst handleSubmit = async () => {\r\n    // 表单验证\r\n    if (!formData.value.plateNo) {\r\n        uni.showToast({\r\n            title: '请输入车牌号',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n\r\n    if (!formData.value.carType) {\r\n        uni.showToast({\r\n            title: '请选择车型',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n\r\n    if (!formData.value.energyType) {\r\n        uni.showToast({\r\n            title: '请选择能源类型',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n    // 显示加载状态\r\n    uni.showLoading({\r\n        title: isEdit.value ? '保存中...' : '添加中...',\r\n        mask: true\r\n    });\r\n\r\n    try {\r\n        let res;\r\n        // 编辑模式\r\n        if (isEdit.value) {\r\n            res = await editCar(formData.value);\r\n        }\r\n        // 添加模式\r\n        else {\r\n            res = await addCar(formData.value);\r\n        }\r\n\r\n        uni.hideLoading();\r\n\r\n        if (res.code === 200) {\r\n            uni.showToast({\r\n                title: isEdit.value ? '编辑成功' : '添加成功',\r\n                icon: 'success',\r\n                duration: 1500\r\n            });\r\n            \r\n            // 只有成功时才返回上一页\r\n            setTimeout(() => {\r\n                uni.navigateBack();\r\n            }, 1500);\r\n        } else {\r\n            // 显示后端返回的具体错误信息，不返回页面\r\n            uni.showToast({\r\n                title: res.msg || '操作失败',\r\n                icon: 'none',\r\n                duration: 3000\r\n            });\r\n        }\r\n    } catch (error) {\r\n        console.error('操作失败:', error);\r\n        uni.hideLoading();\r\n        \r\n        // 显示捕获到的异常信息\r\n        const errorMsg = error.msg || error.message || '操作失败，请重试';\r\n        uni.showToast({\r\n            title: errorMsg,\r\n            icon: 'none',\r\n            duration: 3000\r\n        });\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-car-add-container {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.header-section {\r\n    padding: 20rpx 32rpx 0;\r\n\r\n    .header-content {\r\n        .header-title {\r\n            margin-right: 20rpx;\r\n\r\n            .title {\r\n                font-size: 38rpx;\r\n                font-weight: bold;\r\n                margin-bottom: 16rpx;\r\n            }\r\n\r\n            .desc {\r\n                font-size: 26rpx;\r\n                opacity: 0.5;\r\n            }\r\n        }\r\n\r\n        .header-image {\r\n            width: 280rpx;\r\n            height: 200rpx;\r\n        }\r\n    }\r\n}\r\n\r\n.form-section {\r\n    background: #fff;\r\n    margin: 0 24rpx 24rpx 24rpx;\r\n    border-radius: 24rpx;\r\n    padding: 32rpx;\r\n\r\n    .form-item {\r\n        margin-bottom: 40rpx;\r\n\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n        }\r\n\r\n        .form-label {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #000000;\r\n            margin-bottom: 20rpx;\r\n        }\r\n\r\n        .form-input {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 20rpx 0;\r\n            border-bottom: 1rpx solid #dbdbdb;\r\n\r\n            .picker-input {\r\n                flex: 1;\r\n                font-size: 32rpx;\r\n                color: #555;\r\n            }\r\n\r\n            .plate-display {\r\n                flex: 1;\r\n                font-size: 32rpx;\r\n                color: #555;\r\n\r\n            }\r\n\r\n            .input-field {\r\n                flex: 1;\r\n                font-size: 32rpx;\r\n                color: #555;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.bottom-section {\r\n    margin: 32rpx 24rpx;\r\n\r\n    .submit-btn {\r\n        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);\r\n        border-radius: 50rpx;\r\n        padding: 28rpx;\r\n        text-align: center;\r\n\r\n        .submit-text {\r\n            color: #fff;\r\n            font-size: 32rpx;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n}\r\n\r\n.my-car-add-container {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/myCar/myCarAdd.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "getCarDetailById", "uni", "editCar", "addCar"], "mappings": ";;;;;;;;;;;;;;AAkFA,MAAM,gBAAgB,MAAW;;;;AAIjC,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACjB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,IACd,CAAC;AAGD,UAAM,SAASA,cAAAA,IAAI,KAAK;AAExBC,kBAAM,OAAC,CAAC,YAAY;AAChB,UAAI,QAAQ,WAAW,QAAQ;AAC3B,eAAO,QAAQ;AAAA,MAClB;AACD,UAAI,QAAQ,IAAI;AACZ,oBAAY,QAAQ,EAAE;AAAA,MACzB;AAAA,IACL,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAC7B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AAGpC,UAAM,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAGA,UAAM,oBAAoB;AAAA,MACtB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,IAC7B;AAGA,UAAM,qBAAqB,CAAC,UAAU;AAClC,YAAM,SAAS,kBAAkB,KAAK,UAAQ,KAAK,UAAU,KAAK;AAClE,aAAO,SAAS,OAAO,QAAQ;AAAA,IACnC;AAGA,UAAM,cAAc,CAAC,OAAO;AACxBE,cAAgB,iBAAC,EAAE,GAAQ,CAAA,EAAE,KAAK,CAAC,QAAQ;AACvC,YAAI,IAAI,SAAS,KAAK;AAClB,mBAAS,QAAQ,IAAI;AAAA,QACxB;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,oBAAoB,MAAM;AAC5B,kBAAY,QAAQ;AAAA,IACxB;AAGA,UAAM,uBAAuB,MAAM;AAC/B,qBAAe,QAAQ;AAAA,IAC3B;AAGA,UAAM,mBAAmB,CAAC,MAAM;AAC5B,eAAS,MAAM,UAAU,EAAE,MAAM,CAAC;AAClC,kBAAY,QAAQ;AAAA,IACxB;AAGA,UAAM,sBAAsB,CAAC,MAAM;AAC/B,YAAM,gBAAgB,EAAE,MAAM,CAAC;AAC/B,YAAM,iBAAiB,kBAAkB,KAAK,UAAQ,KAAK,UAAU,aAAa;AAClF,eAAS,MAAM,aAAa,iBAAiB,eAAe,QAAQ;AACpE,qBAAe,QAAQ;AAAA,IAC3B;AAGA,UAAM,iBAAiB,MAAM;AACzB,yBAAmB,QAAQ;AAAA,IAC/B;AAGA,UAAM,kBAAkB,MAAM;AAC1B,yBAAmB,QAAQ;AAAA,IAC/B;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAC9B,eAAS,MAAM,UAAU;AACzB,yBAAmB,QAAQ;AAAA,IAC/B;AAGA,UAAM,eAAe,YAAY;AAE7B,UAAI,CAAC,SAAS,MAAM,SAAS;AACzBC,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI,CAAC,SAAS,MAAM,SAAS;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI,CAAC,SAAS,MAAM,YAAY;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO,OAAO,QAAQ,WAAW;AAAA,QACjC,MAAM;AAAA,MACd,CAAK;AAED,UAAI;AACA,YAAI;AAEJ,YAAI,OAAO,OAAO;AACd,gBAAM,MAAMC,QAAAA,QAAQ,SAAS,KAAK;AAAA,QACrC,OAEI;AACD,gBAAM,MAAMC,QAAAA,OAAO,SAAS,KAAK;AAAA,QACpC;AAEDF,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,SAAS,KAAK;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,OAAO,QAAQ,SAAS;AAAA,YAC/B,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAGD,qBAAW,MAAM;AACbA,0BAAG,MAAC,aAAY;AAAA,UACnB,GAAE,IAAI;AAAA,QACnB,OAAe;AAEHA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,mCAAc,SAAS,KAAK;AAC5BA,sBAAG,MAAC,YAAW;AAGf,cAAM,WAAW,MAAM,OAAO,MAAM,WAAW;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAAA,MACJ;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/PA,GAAG,WAAW,eAAe;"}