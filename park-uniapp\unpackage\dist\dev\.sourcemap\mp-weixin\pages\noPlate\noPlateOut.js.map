{"version": 3, "file": "noPlateOut.js", "sources": ["pages/noPlate/noPlateOut.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm9QbGF0ZS9ub1BsYXRlT3V0LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"no-plate-in-container\">\r\n        <view class=\"content\">\r\n            <view class=\"content-top\">\r\n                <view class=\"content-top-title\">\r\n                    <view class=\"title\"> 无牌车辆出场 </view>\r\n                    <view class=\"desc\"> car register </view>\r\n                </view>\r\n                <image src=\"/static/image/carRight.png\" class=\"car-image\" mode=\"aspectFit\"></image>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { getOpenid } from \"@/api/login\";\r\nimport { noPlateOut, payParkingOrderCallBack } from \"@/api/parkingOrder\";\r\n\r\nconst gateNo = ref('');\r\nconst warehouseId = ref('');\r\nconst openid = ref('');\r\nconst scanTime = ref(0);\r\n\r\n// 页面加载时执行\r\nonLoad((options) => {\r\n    console.log('页面参数:', options);\r\n\r\n    // 获取二维码原始链接内容\r\n    const qrUrl = decodeURIComponent(options.q || '');\r\n\r\n    // 获取用户扫码时间 (UNIX时间戳，单位秒)\r\n    const scancode_time = parseInt(options.scancode_time || 0);\r\n\r\n    console.log('二维码原始链接:', qrUrl);\r\n    console.log('扫码时间:', scancode_time);\r\n\r\n    if (qrUrl) {\r\n        // 解析URL参数\r\n        parseQrParamsManually(qrUrl);\r\n    } else {\r\n        // 兼容直接传参的方式（开发调试用）\r\n        gateNo.value = options.gateNo || '';\r\n        warehouseId.value = options.warehouseId || '';\r\n\r\n        if (gateNo.value && warehouseId.value) {\r\n            handleScanParams(gateNo.value, warehouseId.value);\r\n        }\r\n    }\r\n\r\n    // 保存扫码时间\r\n    scanTime.value = scancode_time;\r\n});\r\n\r\n/**\r\n * 手动解析URL参数方法\r\n * @param {string} url - 二维码原始URL\r\n */\r\nconst parseQrParamsManually = (url) => {\r\n    try {\r\n        // 提取查询参数部分\r\n        const queryString = url.split('?')[1];\r\n        if (!queryString) {\r\n            throw new Error('URL中没有查询参数');\r\n        }\r\n\r\n        // 解析参数\r\n        const params = {};\r\n        queryString.split('&').forEach(param => {\r\n            const [key, value] = param.split('=');\r\n            if (key && value) {\r\n                params[decodeURIComponent(key)] = decodeURIComponent(value);\r\n            }\r\n        });\r\n\r\n        console.log('手动解析的参数:', params);\r\n\r\n        // 更新页面数据\r\n        gateNo.value = params.gateNo || '';\r\n        warehouseId.value = params.warehouseId || '';\r\n        \r\n        // 调用业务处理方法\r\n        handleScanParams(gateNo.value, warehouseId.value);\r\n\r\n    } catch (error) {\r\n        console.error('手动解析URL参数也失败:', error);\r\n        uni.showToast({\r\n            title: '二维码格式错误,请重新扫码～',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n    }\r\n};\r\n\r\n/**\r\n * 处理扫码参数\r\n * @param {string} gateNoParam - 闸门号\r\n * @param {string} warehouseIdParam - 场库ID\r\n */\r\nconst handleScanParams = (gateNoParam, warehouseIdParam) => {\r\n    if (gateNoParam && warehouseIdParam) {\r\n        // 参数正确，显示确认弹窗\r\n        setTimeout(() => {\r\n            showConfirmModal();\r\n        }, 500);\r\n    } else {\r\n        uni.showToast({\r\n            title: '参数错误,请重新扫码～',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n    }\r\n};\r\n\r\n\r\nconst noPlateOutPost = () => {\r\n    uni.showLoading({\r\n        title: '出场中...',\r\n        mask: true\r\n    })\r\n    uni.login({\r\n        provider: 'weixin',\r\n        success: (res) => {\r\n            getOpenid({wxCode: res.code}).then(res => {\r\n                openid.value = res.data\r\n                \r\n                if (!openid.value) {\r\n                    uni.hideLoading()\r\n                    uni.showToast({\r\n                        title: '网络异常,请重新扫码～',\r\n                        icon: 'none',\r\n                        duration: 2000\r\n                    })\r\n                    return\r\n                }\r\n                \r\n                let params = {\r\n                    openId: openid.value,\r\n                    gateNo: gateNo.value,\r\n                    warehouseId: warehouseId.value\r\n                }\r\n                noPlateOut(params).then(res => {\r\n                    if (res.code == 200 && res.msg == \"000\") {\r\n                        uni.hideLoading()\r\n                        uni.showToast({\r\n                            title: \"金额为0,出场成功\",\r\n                            icon: 'success',\r\n                            duration: 2000\r\n                        })\r\n                        return\r\n                    }\r\n                    uni.requestPayment({\r\n                        timeStamp: res.data.timeStamp,\r\n                        nonceStr: res.data.nonceStr,\r\n                        package: res.data.package,\r\n                        signType: res.data.signType,\r\n                        paySign: res.data.paySign,\r\n                        success: function (result) {\r\n                            uni.hideLoading()\r\n                            uni.showToast({\r\n                                title: '支付成功~',\r\n                                icon: 'success',\r\n                                duration: 2000,\r\n                                complete: function () {\r\n                                    setTimeout(() => {\r\n                                        uni.navigateBack()\r\n                                    }, 1000)\r\n                                }\r\n                            })\r\n                            // let params = {\r\n                            //     tradeId: res.data.tradeId\r\n                            // }\r\n                            // payParkingOrderCallBack(params).then(item => {\r\n                            //     console.log('停车订单 支付成功回调 item: ', item)\r\n                            // })\r\n                        },\r\n                        fail: function (err) {\r\n                            console.log('支付失败的回调：', err)\r\n                            uni.showToast({\r\n                                title: '支付失败，请重试',\r\n                                icon: 'none',\r\n                                duration: 2000\r\n                            })\r\n                        },\r\n                        complete: function (res) {\r\n                            uni.hideLoading()\r\n                        }\r\n                    })\r\n                }).catch(err => {\r\n                    console.error('无牌车出场失败:', err)\r\n                    uni.hideLoading()\r\n                    uni.showToast({\r\n                        title: err.msg || '出场失败,请重新扫码～',\r\n                        icon: 'none',\r\n                        duration: 2000\r\n                    })\r\n                })\r\n            }).catch(err => {\r\n                console.error('获取openid失败:', err)\r\n                uni.hideLoading()\r\n                uni.showToast({\r\n                    title: '网络异常,请重新扫码～',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            })\r\n        },\r\n        fail: (err) => {\r\n            console.error('微信登录失败:', err)\r\n            uni.hideLoading()\r\n            uni.showToast({\r\n                title: '微信登录失败，请重试～',\r\n                icon: 'none',\r\n                duration: 2000\r\n            })\r\n        },\r\n        complete: () => {\r\n            uni.hideLoading()\r\n        }\r\n    })\r\n}\r\n// 显示确认弹窗\r\nconst showConfirmModal = () => {\r\n    uni.showModal({\r\n        title: '确认出场',\r\n        content: '您确定是无牌车，且要出场吗？',\r\n        cancelText: '取消',\r\n        confirmText: '确认',\r\n        success: (res) => {\r\n            if (res.confirm) {\r\n                noPlateOutPost();\r\n            } else if (res.cancel) {\r\n                // 点击取消\r\n                uni.showToast({\r\n                    title: '已经取消出场',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                });\r\n            }\r\n        }\r\n    });\r\n};\r\n\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.no-plate-in-container {\r\n    background-color: #f5f5f5;\r\n    height: 100vh;\r\n    padding: 20rpx;\r\n}\r\n\r\n.content {\r\n    background: #fff;\r\n    padding: 40rpx;\r\n    border-radius: 20rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    .content-top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .content-top-title {\r\n            display: flex;\r\n            flex-direction: column;\r\n            gap: 8rpx;\r\n\r\n            .title {\r\n                font-size: 40rpx;\r\n                font-weight: bold;\r\n                color: #212121;\r\n            }\r\n\r\n            .desc {\r\n                font-size: 28rpx;\r\n                font-weight: 400;\r\n                color: #9e9e9e;\r\n            }\r\n        }\r\n\r\n        .car-image {\r\n            width: 284rpx;\r\n            height: 200rpx;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/noPlate/noPlateOut.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getOpenid", "res", "noPlateOut"], "mappings": ";;;;;;;;AAmBA,UAAM,SAASA,cAAAA,IAAI,EAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,SAASA,cAAAA,IAAI,EAAE;AACrB,UAAM,WAAWA,cAAAA,IAAI,CAAC;AAGtBC,kBAAM,OAAC,CAAC,YAAY;AAChBC,oBAAA,MAAA,MAAA,OAAA,sCAAY,SAAS,OAAO;AAG5B,YAAM,QAAQ,mBAAmB,QAAQ,KAAK,EAAE;AAGhD,YAAM,gBAAgB,SAAS,QAAQ,iBAAiB,CAAC;AAEzDA,oBAAA,MAAA,MAAA,OAAA,sCAAY,YAAY,KAAK;AAC7BA,oBAAA,MAAA,MAAA,OAAA,sCAAY,SAAS,aAAa;AAElC,UAAI,OAAO;AAEP,8BAAsB,KAAK;AAAA,MACnC,OAAW;AAEH,eAAO,QAAQ,QAAQ,UAAU;AACjC,oBAAY,QAAQ,QAAQ,eAAe;AAE3C,YAAI,OAAO,SAAS,YAAY,OAAO;AACnC,2BAAiB,OAAO,OAAO,YAAY,KAAK;AAAA,QACnD;AAAA,MACJ;AAGD,eAAS,QAAQ;AAAA,IACrB,CAAC;AAMD,UAAM,wBAAwB,CAAC,QAAQ;AACnC,UAAI;AAEA,cAAM,cAAc,IAAI,MAAM,GAAG,EAAE,CAAC;AACpC,YAAI,CAAC,aAAa;AACd,gBAAM,IAAI,MAAM,YAAY;AAAA,QAC/B;AAGD,cAAM,SAAS,CAAA;AACf,oBAAY,MAAM,GAAG,EAAE,QAAQ,WAAS;AACpC,gBAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,cAAI,OAAO,OAAO;AACd,mBAAO,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK;AAAA,UAC7D;AAAA,QACb,CAAS;AAEDA,sBAAA,MAAA,MAAA,OAAA,sCAAY,YAAY,MAAM;AAG9B,eAAO,QAAQ,OAAO,UAAU;AAChC,oBAAY,QAAQ,OAAO,eAAe;AAG1C,yBAAiB,OAAO,OAAO,YAAY,KAAK;AAAA,MAEnD,SAAQ,OAAO;AACZA,sBAAc,MAAA,MAAA,SAAA,sCAAA,iBAAiB,KAAK;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAAA,MACJ;AAAA,IACL;AAOA,UAAM,mBAAmB,CAAC,aAAa,qBAAqB;AACxD,UAAI,eAAe,kBAAkB;AAEjC,mBAAW,MAAM;AACb;QACH,GAAE,GAAG;AAAA,MACd,OAAW;AACHA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,iBAAiB,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AACDA,oBAAAA,MAAI,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACdC,oBAAS,UAAC,EAAC,QAAQ,IAAI,KAAI,CAAC,EAAE,KAAK,CAAAC,SAAO;AACtC,mBAAO,QAAQA,KAAI;AAEnB,gBAAI,CAAC,OAAO,OAAO;AACfF,4BAAAA,MAAI,YAAa;AACjBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cAClC,CAAqB;AACD;AAAA,YACH;AAED,gBAAI,SAAS;AAAA,cACT,QAAQ,OAAO;AAAA,cACf,QAAQ,OAAO;AAAA,cACf,aAAa,YAAY;AAAA,YAC5B;AACDG,6BAAAA,WAAW,MAAM,EAAE,KAAK,CAAAD,SAAO;AAC3B,kBAAIA,KAAI,QAAQ,OAAOA,KAAI,OAAO,OAAO;AACrCF,8BAAAA,MAAI,YAAa;AACjBA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AACD;AAAA,cACH;AACDA,4BAAAA,MAAI,eAAe;AAAA,gBACf,WAAWE,KAAI,KAAK;AAAA,gBACpB,UAAUA,KAAI,KAAK;AAAA,gBACnB,SAASA,KAAI,KAAK;AAAA,gBAClB,UAAUA,KAAI,KAAK;AAAA,gBACnB,SAASA,KAAI,KAAK;AAAA,gBAClB,SAAS,SAAU,QAAQ;AACvBF,gCAAAA,MAAI,YAAa;AACjBA,gCAAAA,MAAI,UAAU;AAAA,oBACV,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,UAAU;AAAA,oBACV,UAAU,WAAY;AAClB,iCAAW,MAAM;AACbA,sCAAAA,MAAI,aAAc;AAAA,sBACrB,GAAE,GAAI;AAAA,oBACV;AAAA,kBACjC,CAA6B;AAAA,gBAOJ;AAAA,gBACD,MAAM,SAAU,KAAK;AACjBA,gCAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY,GAAG;AAC3BA,gCAAAA,MAAI,UAAU;AAAA,oBACV,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,UAAU;AAAA,kBAC1C,CAA6B;AAAA,gBACJ;AAAA,gBACD,UAAU,SAAUE,MAAK;AACrBF,gCAAAA,MAAI,YAAa;AAAA,gBACpB;AAAA,cACzB,CAAqB;AAAA,YACrB,CAAiB,EAAE,MAAM,SAAO;AACZA,4BAAAA,MAAA,MAAA,SAAA,uCAAc,YAAY,GAAG;AAC7BA,4BAAAA,MAAI,YAAa;AACjBA,4BAAAA,MAAI,UAAU;AAAA,gBACV,OAAO,IAAI,OAAO;AAAA,gBAClB,MAAM;AAAA,gBACN,UAAU;AAAA,cAClC,CAAqB;AAAA,YACrB,CAAiB;AAAA,UACjB,CAAa,EAAE,MAAM,SAAO;AACZA,0BAAAA,MAAc,MAAA,SAAA,uCAAA,eAAe,GAAG;AAChCA,0BAAAA,MAAI,YAAa;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACjB,CAAa;AAAA,QACJ;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAAA,MAAc,MAAA,SAAA,uCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,YAAa;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ;AAAA,QACD,UAAU,MAAM;AACZA,wBAAAA,MAAI,YAAa;AAAA,QACpB;AAAA,MACT,CAAK;AAAA,IACL;AAEA,UAAM,mBAAmB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACb;UAChB,WAAuB,IAAI,QAAQ;AAEnBA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACJ;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;AChPA,GAAG,WAAW,eAAe;"}