{"version": 3, "file": "ChargingFastPackage.js", "sources": ["pages/package/components/ChargingFastPackage.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9wYWdlcy9wYWNrYWdlL2NvbXBvbmVudHMvQ2hhcmdpbmdGYXN0UGFja2FnZS52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"charging-package-component\">\r\n        <view class=\"empty-state\">\r\n            <up-icon name=\"file-text\" size=\"100\" color=\"#d0d0d0\"></up-icon>\r\n            <text class=\"empty-text\">暂无充电套餐</text>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\n\r\n// 初始化数据方法\r\nconst initData = async () => {\r\n    console.log('充电套餐组件初始化 - 暂无数据');\r\n};\r\n\r\n// 暴露方法给父组件调用\r\ndefineExpose({\r\n    initData\r\n});\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.charging-package-component {\r\n    padding: 20rpx;\r\n    min-height: 80vh;\r\n    \r\n    .empty-state {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 60rpx 40rpx;\r\n        \r\n        .empty-text {\r\n            font-size: 32rpx;\r\n            color: #5f5f5f;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n}\r\n</style>", "import Component from 'F:/parking/park-uniapp/pages/package/components/ChargingFastPackage.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;AAYA,UAAM,WAAW,YAAY;AACzBA,oBAAAA,MAAY,MAAA,OAAA,0DAAA,kBAAkB;AAAA,IAClC;AAGA,aAAa;AAAA,MACT;AAAA,IACJ,CAAC;;;;;;;;;;;;;;AClBD,GAAG,gBAAgB,SAAS;"}