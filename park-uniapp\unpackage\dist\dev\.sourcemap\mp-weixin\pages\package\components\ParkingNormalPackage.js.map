{"version": 3, "file": "ParkingNormalPackage.js", "sources": ["pages/package/components/ParkingNormalPackage.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9wYWdlcy9wYWNrYWdlL2NvbXBvbmVudHMvUGFya2luZ05vcm1hbFBhY2thZ2UudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"vip-page\">\r\n        <image class=\"vip-bg\" src=\"https://test-parknew.lgfw24hours.com:3443/statics/wx/vipHeader.png\" \r\n        mode=\"widthFix\">\r\n        </image>\r\n        <!-- 顶部卡片 -->\r\n        <view class=\"vip-card\">\r\n            <view class=\"vip-info\">\r\n                <!-- 场库选择 -->\r\n                <view class=\"vip-title-container\" @tap=\"showWarehouseSelector\">\r\n                    <text class=\"vip-title\">{{ currentWarehouse.name }}</text>\r\n                    <up-icon name=\"arrow-right\" size=\"12\" color=\"#fff\"></up-icon>\r\n                </view>\r\n                <!-- 车辆选择 -->\r\n                <view class=\"vip-plate\" @tap=\"openCarSelector\">\r\n                    <text>车牌号：</text>\r\n                    <text class=\"plate-number\">{{ selectedCar.plateNo || '点击选择' }}</text>\r\n                    <up-icon name=\"arrow-right\" size=\"12\" color=\"#fff\"></up-icon>\r\n                </view>\r\n                <!-- 开始结束时间 -->\r\n                <view class=\"vip-expire\">开始：{{ userPackagePlate?.beginVipTime || '--' }}</view>\r\n                <view class=\"vip-expire\">结束：{{ userPackagePlate?.endVipTime || '--' }}</view>\r\n                <view class=\"vip-link\" @tap=\"goToRecord\">查看续费与开通记录</view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 套餐选择 -->\r\n        <view class=\"package-list\">\r\n            <view class=\"package-title\">选择普通充值套餐</view>\r\n            <view class=\"package-grid\" v-if=\"packageList?.length > 0\">\r\n                <view v-for=\"item in packageList\" :key=\"item.id\" class=\"package-item\"\r\n                    :class=\"{ active: choosePackage.id === item.id }\" @click=\"handleChoosePackage(item)\">\r\n                    <view class=\"package-content\">\r\n                        <view class=\"package-name\">{{ item.packageName }}</view>\r\n                        <view class=\"package-price\">\r\n                            <text class=\"price-symbol\">￥</text>\r\n                            <text class=\"price-amount\">{{ item.packagePrice }}</text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 空状态显示 -->\r\n            <view class=\"empty-state\" v-else>\r\n                <up-empty mode=\"data\" text=\"暂无可用套餐\" />\r\n            </view>\r\n            <button class=\"buy-btn\" :disabled=\"!choosePackage.id || packageList.length === 0\" @click=\"handleBuyPackage\">\r\n                {{ userPackagePlate?.endVipTime ? '续费套餐' : '购买套餐' }}\r\n            </button>\r\n        </view>\r\n\r\n        <!-- 场库选择器组件 -->\r\n        <warehouse-selector :show=\"wareHouseSelector\" :warehouse-list=\"wareHouseList\"\r\n            :current-warehouse=\"currentWarehouse\" :window-height-half=\"400\" @close=\"closeWarehouseSelector\"\r\n            @select=\"selectWarehouse\" />\r\n\r\n        <!-- 车辆选择器 -->\r\n        <u-picker :show=\"showCarSelector\" :columns=\"[carList]\" @confirm=\"onCarConfirm\" @cancel=\"showCarSelector = false\"\r\n            keyName=\"plateNo\"></u-picker>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { getPackageList, getUserPackagePlate} from '@/api/package';\r\nimport { getParkWareHouseList } from '@/api/warehouse';\r\nimport WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue';\r\nimport { getCarList } from '@/api/car';\r\n// 普通套餐类型标识\r\nconst vipType = ref(0)\r\n\r\n// 场库选择器\r\nconst wareHouseList = ref([]);\r\nconst wareHouseSelector = ref(false);\r\nconst currentWarehouse = ref({ id: 0, name: \"选择场库\"});\r\n\r\n// 套餐列表\r\nconst packageList = ref([]);\r\nconst choosePackage = ref({});\r\n\r\n// 车辆列表\r\nconst carList = ref([]);\r\nconst selectedCar = ref({});\r\nconst showCarSelector = ref(false);\r\nconst userPackagePlate = ref({});\r\n\r\n// 统一初始化数据\r\nconst initData = async () => {\r\n    await initWarehouseData();\r\n    await initPackageData();\r\n    await initCarData();\r\n};\r\n\r\n// 初始化场库数据\r\nconst initWarehouseData = async () => {\r\n    const res = await getParkWareHouseList();\r\n    wareHouseList.value = res.data.map(item => ({\r\n        id: item.id,\r\n        name: item.warehouseName,\r\n        latitude: item.latitude,\r\n        longitude: item.longitude\r\n    }));\r\n    // 先从缓存中查找场库信息\r\n    const cachedWarehouse = uni.getStorageSync('currentWarehouse')\r\n    if (cachedWarehouse && wareHouseList.value.some(w => w.id === cachedWarehouse.id)) {\r\n        currentWarehouse.value = cachedWarehouse\r\n    } else if (wareHouseList.value.length > 0) {\r\n        // 如果缓存中没有，使用场库列表的第一项\r\n        currentWarehouse.value = wareHouseList.value[0]\r\n        uni.setStorageSync('currentWarehouse', currentWarehouse.value)\r\n    }\r\n};\r\n\r\n// 初始化套餐数据\r\nconst initPackageData = async () => {\r\n    try {\r\n        // 场库不存在，套餐列表为空\r\n        if (!currentWarehouse.value || !currentWarehouse.value.id) {\r\n            packageList.value = [];\r\n            choosePackage.value = {};\r\n            return;\r\n        }\r\n\r\n        // 按场库ID获取套餐列表\r\n        const warehouseId = currentWarehouse.value.id;\r\n        const res = await getPackageList({ warehouseId: warehouseId});\r\n        packageList.value = res.data || [];\r\n        \r\n        // 默认选中第一个套餐\r\n        if (packageList.value.length > 0) {\r\n            choosePackage.value = packageList.value[0];\r\n        } \r\n    } catch (error) {\r\n        uni.showToast({\r\n            title: '套餐数据加载失败',\r\n            icon: 'none'\r\n        });\r\n        packageList.value = [];\r\n        choosePackage.value = {};\r\n    }\r\n};\r\n\r\n// 初始化车辆数据\r\nconst initCarData = async () => {\r\n    try {\r\n        const res = await getCarList();\r\n        carList.value = res.data || [];\r\n        if(carList.value.length === 0) {\r\n            return;\r\n        }\r\n        \r\n        // 优先使用缓存的车辆\r\n        const cachedCar = uni.getStorageSync('selectedCar');\r\n        if (cachedCar && cachedCar.plateNo) {\r\n            // 检查缓存的车辆是否在当前车辆列表中\r\n            const cachedCarInList = carList.value.find(car => car.plateNo === cachedCar.plateNo);\r\n            if (cachedCarInList) {\r\n                selectedCar.value = cachedCarInList;\r\n                // 更新缓存中的车辆信息（可能有更新）\r\n                try {\r\n                    uni.setStorageSync('selectedCar', cachedCarInList);\r\n                } catch (error) {\r\n                    console.error('更新缓存车辆信息失败:', error);\r\n                }\r\n            } else {\r\n                // 缓存的车辆不在列表中，使用默认逻辑\r\n                selectDefaultCar();\r\n            }\r\n        } else {\r\n            // 没有缓存，使用默认逻辑\r\n            selectDefaultCar();\r\n        }\r\n        \r\n        // 获取用户车辆未过期套餐信息\r\n        if (currentWarehouse.value?.id && selectedCar.value?.plateNo) {\r\n            getUserPackagePlate({ \r\n                warehouseId: currentWarehouse.value.id, \r\n                plateNo: selectedCar.value.plateNo,\r\n                vipType: vipType.value\r\n            }).then(res => {\r\n                userPackagePlate.value = res.data || {};\r\n            }).catch(error => {\r\n                console.error('获取用户套餐信息失败:', error);\r\n                userPackagePlate.value = {};\r\n            });\r\n        }\r\n    } catch (error) {\r\n        console.error('初始化车辆数据失败:', error);\r\n        carList.value = [];\r\n        selectedCar.value = {};\r\n        userPackagePlate.value = {};\r\n    }\r\n};\r\n\r\n// 选择默认车辆的逻辑\r\nconst selectDefaultCar = () => {\r\n    // 设置默认车辆（isDefault为1的车辆）\r\n    const defaultCar = carList.value.find(car => car.isDefault === 1);\r\n    if (defaultCar) {\r\n        selectedCar.value = defaultCar;\r\n    } else if (carList.value.length > 0) {\r\n        // 如果没有默认车辆，选择第一辆\r\n        selectedCar.value = carList.value[0];\r\n    }\r\n    \r\n    // 将选中的车辆缓存起来\r\n    if (selectedCar.value && selectedCar.value.plateNo) {\r\n        try {\r\n            uni.setStorageSync('selectedCar', selectedCar.value);\r\n        } catch (error) {\r\n            console.error('缓存车辆信息失败:', error);\r\n        }\r\n    }\r\n};\r\n\r\n// 显示场库选择器\r\nconst showWarehouseSelector = () => {\r\n    wareHouseSelector.value = true;\r\n};\r\n\r\n// 关闭场库选择器\r\nconst closeWarehouseSelector = () => {\r\n    wareHouseSelector.value = false;\r\n};\r\n\r\n// 选择场库\r\nconst selectWarehouse = (warehouse) => {\r\n    currentWarehouse.value = warehouse;\r\n    uni.setStorageSync(\"currentWarehouse\", warehouse);\r\n    closeWarehouseSelector();\r\n    \r\n    // 切换场库后重新查询套餐列表\r\n    initPackageData();\r\n    \r\n    // 重新获取用户套餐信息\r\n    if (selectedCar.value?.plateNo) {\r\n        getUserPackagePlate({ \r\n            warehouseId: warehouse.id, \r\n            plateNo: selectedCar.value.plateNo,\r\n            vipType: vipType.value\r\n        }).then(res => {\r\n            userPackagePlate.value = res.data;\r\n        }).catch(error => {\r\n            console.error('获取用户套餐信息失败:', error);\r\n            userPackagePlate.value = {};\r\n        });\r\n    }\r\n};\r\n\r\n// 显示车辆选择器\r\nconst openCarSelector = () => {\r\n    if(!hasCar()) {\r\n        return;\r\n    }\r\n    showCarSelector.value = true;\r\n};\r\n// 用户是否添加车辆\r\nconst hasCar = () => {\r\n    if (carList.value.length === 0) {\r\n        uni.showToast({\r\n            title: '请先去个人中心添加车辆',\r\n            icon: \"none\",\r\n            duration: 1500,\r\n        });\r\n        return false;\r\n    }\r\n    return true;\r\n}\r\n\r\n// 车辆选择确认\r\nconst onCarConfirm = (e) => {\r\n    selectedCar.value = e.value[0];\r\n    showCarSelector.value = false;\r\n    \r\n    // 将选中的车辆缓存起来\r\n    try {\r\n        uni.setStorageSync('selectedCar', selectedCar.value);\r\n    } catch (error) {\r\n        console.error('缓存车辆信息失败:', error);\r\n    }\r\n    \r\n    // 切换车辆后重新获取用户套餐信息\r\n    if (currentWarehouse.value?.id && selectedCar.value?.plateNo) {\r\n        getUserPackagePlate({ \r\n            warehouseId: currentWarehouse.value.id, \r\n            plateNo: selectedCar.value.plateNo,\r\n            vipType: vipType.value\r\n        }).then(res => {\r\n            userPackagePlate.value = res.data || {};\r\n            console.log('userPackagePlate', userPackagePlate.value);\r\n        }).catch(error => {\r\n            console.error('获取用户套餐信息失败:', error);\r\n            userPackagePlate.value = {};\r\n        });\r\n    }\r\n};\r\n\r\n// 选择套餐\r\nconst handleChoosePackage = (item) => {\r\n    choosePackage.value = item;\r\n};\r\n\r\nconst handleBuyPackage = () => {\r\n    if(!hasCar()) {\r\n        return;\r\n    }\r\n    const packageOrder = {\r\n        warehouseId: currentWarehouse.value.id,\r\n        warehouseName: currentWarehouse.value.name,\r\n        packageId: choosePackage.value.id,\r\n        packageName: choosePackage.value.packageName,\r\n        packagePrice: choosePackage.value.packagePrice,\r\n        packageDays: choosePackage.value.packageType,\r\n        plateNo: selectedCar.value.plateNo,\r\n        vipType: vipType.value,\r\n        isRenewal: !!(userPackagePlate.value?.beginVipTime && userPackagePlate.value?.endVipTime),\r\n        beginVipTime: userPackagePlate.value?.beginVipTime,\r\n        expirationTime: userPackagePlate.value?.endVipTime\r\n    }\r\n    uni.navigateTo({ url: '/pages/package/packageBuy?packageOrder=' + JSON.stringify(packageOrder) });\r\n};\r\n\r\nconst goToRecord = () => {\r\n    uni.navigateTo({ url: '/pages/package/packageRecord?vipType=' + vipType.value });\r\n};\r\n\r\n// 暴露方法给父组件调用\r\ndefineExpose({\r\n    initData\r\n});\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-page {\r\n    min-height: 80vh;\r\n    position: relative;\r\n}\r\n\r\n.vip-bg {\r\n    position: absolute;\r\n    top: -15rpx;\r\n    left: 0;\r\n    width: 100%;\r\n    z-index: 1;\r\n}\r\n\r\n.vip-card {\r\n    position: relative;\r\n    z-index: 10;\r\n    margin: 15rpx 0 0 50rpx;\r\n    \r\n    .vip-info {\r\n        padding: 24rpx;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        color: #fff;\r\n\r\n        .vip-title-container {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 16rpx;\r\n            cursor: pointer;\r\n\r\n            .vip-title {\r\n                font-size: 45rpx;\r\n                font-weight: bold;\r\n            }\r\n        }\r\n\r\n        .vip-plate {\r\n            font-size: 30rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            margin:20rpx 0;\r\n            \r\n            .plate-number {\r\n                margin-right: 10rpx;\r\n                font-size: 30rpx;\r\n                font-weight: 500;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        .vip-expire {\r\n            font-size: 24rpx;\r\n            color: #ffffff;\r\n            margin-bottom: 10rpx;\r\n        }\r\n\r\n        .vip-link {\r\n            margin-top: 15rpx;\r\n            font-size: 30rpx;\r\n            color: #ececec;\r\n        }\r\n    }\r\n}\r\n\r\n.package-list {\r\n    position: relative;\r\n    z-index: 10;\r\n    margin-top: 95rpx;\r\n    padding: 0 24rpx;\r\n\r\n    .package-title {\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n        margin-bottom: 32rpx;\r\n    }\r\n\r\n    .package-grid {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 32rpx;\r\n    }\r\n\r\n    .package-item {\r\n        width: calc((100% - 80rpx) / 3);\r\n        background: #fff;\r\n        border-radius: 18rpx;\r\n        height: 160rpx;\r\n        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n        transition: all 0.3s ease;\r\n        border: 2rpx solid transparent;\r\n        flex-shrink: 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .package-content {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 16rpx 8rpx;\r\n\r\n            .package-name {\r\n                font-size: 24rpx;\r\n                color: #333;\r\n                font-weight: 500;\r\n                margin-bottom: 12rpx;\r\n                text-align: center;\r\n            }\r\n\r\n            .package-price {\r\n                display: flex;\r\n                align-items: baseline;\r\n                justify-content: center;\r\n                text-align: center;\r\n                \r\n                .price-symbol {\r\n                    font-size: 24rpx;\r\n                    color: #2c2c2c;\r\n                    font-weight: normal;\r\n                }\r\n                \r\n                .price-amount {\r\n                    font-size: 40rpx;\r\n                    color: #232323;\r\n                    font-weight: bold;\r\n                }\r\n            }\r\n        }\r\n\r\n        &.active {\r\n            border: 3rpx solid #ffb300;\r\n            background: #fffbe6;\r\n            box-shadow: 0 4rpx 16rpx rgba(255, 179, 0, 0.2);\r\n            transform: scale(1.02);\r\n\r\n            .package-content {\r\n                .package-name {\r\n                    color: #ff9900;\r\n                }\r\n                \r\n                .package-price {\r\n                    .price-symbol {\r\n                        color: #ff9900;\r\n                    }\r\n                    \r\n                    .price-amount {\r\n                        color: #ff9900;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .buy-btn {\r\n        width: 100%;\r\n        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);\r\n        color: #fff;\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        border-radius: 45rpx;\r\n        border: none;\r\n        margin: 40rpx 0 0 0;\r\n    }\r\n\r\n    .buy-btn:disabled {\r\n        background: #d0d0d0;\r\n        color: #fff;\r\n    }\r\n\r\n    .empty-state {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 50rpx 0;\r\n    }\r\n}\r\n\r\n.buy-section {\r\n    display: none;\r\n}\r\n</style>", "import Component from 'F:/parking/park-uniapp/pages/package/components/ParkingNormalPackage.vue'\nwx.createComponent(Component)"], "names": ["ref", "getParkWareHouseList", "uni", "getPackageList", "getCarList", "getUserPackagePlate", "res"], "mappings": ";;;;;;;;;;;;;;;;;AAkEA,MAAM,oBAAoB,MAAW;;;;AAGrC,UAAM,UAAUA,cAAG,IAAC,CAAC;AAGrB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AACnC,UAAM,mBAAmBA,cAAG,IAAC,EAAE,IAAI,GAAG,MAAM,OAAM,CAAC;AAGnD,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAG5B,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AACtB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,WAAW,YAAY;AACzB,YAAM,kBAAiB;AACvB,YAAM,gBAAe;AACrB,YAAM,YAAW;AAAA,IACrB;AAGA,UAAM,oBAAoB,YAAY;AAClC,YAAM,MAAM,MAAMC,cAAAA;AAClB,oBAAc,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,QACxC,IAAI,KAAK;AAAA,QACT,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,MACnB,EAAC;AAEF,YAAM,kBAAkBC,cAAAA,MAAI,eAAe,kBAAkB;AAC7D,UAAI,mBAAmB,cAAc,MAAM,KAAK,OAAK,EAAE,OAAO,gBAAgB,EAAE,GAAG;AAC/E,yBAAiB,QAAQ;AAAA,MAC5B,WAAU,cAAc,MAAM,SAAS,GAAG;AAEvC,yBAAiB,QAAQ,cAAc,MAAM,CAAC;AAC9CA,sBAAAA,MAAI,eAAe,oBAAoB,iBAAiB,KAAK;AAAA,MAChE;AAAA,IACL;AAGA,UAAM,kBAAkB,YAAY;AAChC,UAAI;AAEA,YAAI,CAAC,iBAAiB,SAAS,CAAC,iBAAiB,MAAM,IAAI;AACvD,sBAAY,QAAQ;AACpB,wBAAc,QAAQ;AACtB;AAAA,QACH;AAGD,cAAM,cAAc,iBAAiB,MAAM;AAC3C,cAAM,MAAM,MAAMC,YAAc,eAAC,EAAE,YAAwB,CAAC;AAC5D,oBAAY,QAAQ,IAAI,QAAQ,CAAA;AAGhC,YAAI,YAAY,MAAM,SAAS,GAAG;AAC9B,wBAAc,QAAQ,YAAY,MAAM,CAAC;AAAA,QAC5C;AAAA,MACJ,SAAQ,OAAO;AACZD,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD,oBAAY,QAAQ;AACpB,sBAAc,QAAQ;MACzB;AAAA,IACL;AAGA,UAAM,cAAc,YAAY;;AAC5B,UAAI;AACA,cAAM,MAAM,MAAME,QAAAA;AAClB,gBAAQ,QAAQ,IAAI,QAAQ,CAAA;AAC5B,YAAG,QAAQ,MAAM,WAAW,GAAG;AAC3B;AAAA,QACH;AAGD,cAAM,YAAYF,cAAAA,MAAI,eAAe,aAAa;AAClD,YAAI,aAAa,UAAU,SAAS;AAEhC,gBAAM,kBAAkB,QAAQ,MAAM,KAAK,SAAO,IAAI,YAAY,UAAU,OAAO;AACnF,cAAI,iBAAiB;AACjB,wBAAY,QAAQ;AAEpB,gBAAI;AACAA,4BAAAA,MAAI,eAAe,eAAe,eAAe;AAAA,YACpD,SAAQ,OAAO;AACZA,4BAAc,MAAA,MAAA,SAAA,4DAAA,eAAe,KAAK;AAAA,YACrC;AAAA,UACjB,OAAmB;AAEH;UACH;AAAA,QACb,OAAe;AAEH;QACH;AAGD,cAAI,sBAAiB,UAAjB,mBAAwB,SAAM,iBAAY,UAAZ,mBAAmB,UAAS;AAC1DG,0CAAoB;AAAA,YAChB,aAAa,iBAAiB,MAAM;AAAA,YACpC,SAAS,YAAY,MAAM;AAAA,YAC3B,SAAS,QAAQ;AAAA,UACjC,CAAa,EAAE,KAAK,CAAAC,SAAO;AACX,6BAAiB,QAAQA,KAAI,QAAQ,CAAA;AAAA,UACrD,CAAa,EAAE,MAAM,WAAS;AACdJ,0BAAA,MAAA,MAAA,SAAA,4DAAc,eAAe,KAAK;AAClC,6BAAiB,QAAQ;UACzC,CAAa;AAAA,QACJ;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,4DAAc,cAAc,KAAK;AACjC,gBAAQ,QAAQ;AAChB,oBAAY,QAAQ;AACpB,yBAAiB,QAAQ;MAC5B;AAAA,IACL;AAGA,UAAM,mBAAmB,MAAM;AAE3B,YAAM,aAAa,QAAQ,MAAM,KAAK,SAAO,IAAI,cAAc,CAAC;AAChE,UAAI,YAAY;AACZ,oBAAY,QAAQ;AAAA,MACvB,WAAU,QAAQ,MAAM,SAAS,GAAG;AAEjC,oBAAY,QAAQ,QAAQ,MAAM,CAAC;AAAA,MACtC;AAGD,UAAI,YAAY,SAAS,YAAY,MAAM,SAAS;AAChD,YAAI;AACAA,wBAAAA,MAAI,eAAe,eAAe,YAAY,KAAK;AAAA,QACtD,SAAQ,OAAO;AACZA,wBAAc,MAAA,MAAA,SAAA,4DAAA,aAAa,KAAK;AAAA,QACnC;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,wBAAwB,MAAM;AAChC,wBAAkB,QAAQ;AAAA,IAC9B;AAGA,UAAM,yBAAyB,MAAM;AACjC,wBAAkB,QAAQ;AAAA,IAC9B;AAGA,UAAM,kBAAkB,CAAC,cAAc;;AACnC,uBAAiB,QAAQ;AACzBA,oBAAAA,MAAI,eAAe,oBAAoB,SAAS;AAChD;AAGA;AAGA,WAAI,iBAAY,UAAZ,mBAAmB,SAAS;AAC5BG,wCAAoB;AAAA,UAChB,aAAa,UAAU;AAAA,UACvB,SAAS,YAAY,MAAM;AAAA,UAC3B,SAAS,QAAQ;AAAA,QAC7B,CAAS,EAAE,KAAK,SAAO;AACX,2BAAiB,QAAQ,IAAI;AAAA,QACzC,CAAS,EAAE,MAAM,WAAS;AACdH,wBAAA,MAAA,MAAA,SAAA,4DAAc,eAAe,KAAK;AAClC,2BAAiB,QAAQ;QACrC,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,kBAAkB,MAAM;AAC1B,UAAG,CAAC,OAAM,GAAI;AACV;AAAA,MACH;AACD,sBAAgB,QAAQ;AAAA,IAC5B;AAEA,UAAM,SAAS,MAAM;AACjB,UAAI,QAAQ,MAAM,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD,eAAO;AAAA,MACV;AACD,aAAO;AAAA,IACX;AAGA,UAAM,eAAe,CAAC,MAAM;;AACxB,kBAAY,QAAQ,EAAE,MAAM,CAAC;AAC7B,sBAAgB,QAAQ;AAGxB,UAAI;AACAA,sBAAAA,MAAI,eAAe,eAAe,YAAY,KAAK;AAAA,MACtD,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,4DAAc,aAAa,KAAK;AAAA,MACnC;AAGD,YAAI,sBAAiB,UAAjB,mBAAwB,SAAM,iBAAY,UAAZ,mBAAmB,UAAS;AAC1DG,wCAAoB;AAAA,UAChB,aAAa,iBAAiB,MAAM;AAAA,UACpC,SAAS,YAAY,MAAM;AAAA,UAC3B,SAAS,QAAQ;AAAA,QAC7B,CAAS,EAAE,KAAK,SAAO;AACX,2BAAiB,QAAQ,IAAI,QAAQ,CAAA;AACrCH,wBAAY,MAAA,MAAA,OAAA,4DAAA,oBAAoB,iBAAiB,KAAK;AAAA,QAClE,CAAS,EAAE,MAAM,WAAS;AACdA,wBAAA,MAAA,MAAA,SAAA,4DAAc,eAAe,KAAK;AAClC,2BAAiB,QAAQ;QACrC,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,sBAAsB,CAAC,SAAS;AAClC,oBAAc,QAAQ;AAAA,IAC1B;AAEA,UAAM,mBAAmB,MAAM;;AAC3B,UAAG,CAAC,OAAM,GAAI;AACV;AAAA,MACH;AACD,YAAM,eAAe;AAAA,QACjB,aAAa,iBAAiB,MAAM;AAAA,QACpC,eAAe,iBAAiB,MAAM;AAAA,QACtC,WAAW,cAAc,MAAM;AAAA,QAC/B,aAAa,cAAc,MAAM;AAAA,QACjC,cAAc,cAAc,MAAM;AAAA,QAClC,aAAa,cAAc,MAAM;AAAA,QACjC,SAAS,YAAY,MAAM;AAAA,QAC3B,SAAS,QAAQ;AAAA,QACjB,WAAW,CAAC,IAAE,sBAAiB,UAAjB,mBAAwB,mBAAgB,sBAAiB,UAAjB,mBAAwB;AAAA,QAC9E,eAAc,sBAAiB,UAAjB,mBAAwB;AAAA,QACtC,iBAAgB,sBAAiB,UAAjB,mBAAwB;AAAA,MAC3C;AACDA,0BAAI,WAAW,EAAE,KAAK,4CAA4C,KAAK,UAAU,YAAY,EAAC,CAAE;AAAA,IACpG;AAEA,UAAM,aAAa,MAAM;AACrBA,oBAAG,MAAC,WAAW,EAAE,KAAK,0CAA0C,QAAQ,MAAK,CAAE;AAAA,IACnF;AAGA,aAAa;AAAA,MACT;AAAA,IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxUD,GAAG,gBAAgB,SAAS;"}