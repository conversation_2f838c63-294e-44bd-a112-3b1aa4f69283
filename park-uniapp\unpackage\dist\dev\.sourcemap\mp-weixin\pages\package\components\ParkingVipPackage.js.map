{"version": 3, "file": "ParkingVipPackage.js", "sources": ["pages/package/components/ParkingVipPackage.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniComponent:/RjovcGFya2luZy9wYXJrLXVuaWFwcC9wYWdlcy9wYWNrYWdlL2NvbXBvbmVudHMvUGFya2luZ1ZpcFBhY2thZ2UudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"parking-vip-package\">\r\n        <!-- VIP套餐卡片列表 -->\r\n        <view class=\"vip-package-list\">\r\n            <!-- 渲染3个VIP卡片 -->\r\n            <view class=\"vip-card\" v-for=\"(cardData, index) in vipCards\" :key=\"index\">\r\n                <!-- 上半部分：内容和图片 -->\r\n                <view class=\"content-section\">\r\n                    <view class=\"vip-info\">\r\n                        <view class=\"vip-plate\">\r\n                            车牌号：{{ cardData.plateNo || '--' }}\r\n                        </view>\r\n                        <view class=\"vip-title-container\">\r\n                            场库：{{ cardData.warehouseName || '--' }}\r\n                        </view>\r\n                        <view class=\"vip-expire\">\r\n                            开始：{{ cardData.beginVipTime || '--' }}\r\n                        </view>\r\n                        <view class=\"vip-expire\">\r\n                            结束：{{ cardData.endVipTime || '--' }}\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"right-section\">\r\n                        <view class=\"car-image\" v-if=\"cardData.plateNo\">\r\n                            <image src=\"/static/image/carLeft.png\" mode=\"aspectFit\"></image>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <!-- 按钮区域：独占最后一行 -->\r\n                <view class=\"button-section\">\r\n                    <view class=\"vip-link\" \r\n                          :class=\"{ disabled: !isButtonClickable(cardData, index) }\"\r\n                          @tap=\"handleAction(cardData, index)\" >\r\n                        {{ getButtonText(cardData, index) }}\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        \r\n        <!-- 注意事项 -->\r\n        <!-- <view class=\"notice-section\">\r\n            <view class=\"notice-title\">注意事项</view>\r\n            <view class=\"notice-content\">\r\n                <text class=\"notice-text\" v-if=\"props.userType === 'VIP套餐'\">\r\n                    最多可购买三辆车，每辆车价格1800元/年，手机号和车牌号一一对应，如需修改手机号或者车牌号，请联系客服。\r\n                </text>\r\n                <text class=\"notice-text\" v-else>\r\n                    最多可购买三辆车，第一辆车免费，第二辆车600元/年，第三辆车1800元/年，手机号和车牌号一一对应，如需修改手机号或者车牌号，请联系客服。\r\n                </text>\r\n            </view>\r\n        </view> -->\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from \"vue\";\r\nimport { getVipUserPackageList } from \"@/api/package\";\r\n\r\n// 定义props\r\nconst props = defineProps({\r\n    userType: {\r\n        type: String,\r\n        default: 'VIP套餐' // 默认VIP套餐\r\n    }\r\n});\r\n\r\n// 响应式数据\r\nconst vipUserCarList = ref(null);\r\n\r\n// 计算属性\r\nconst vipCards = computed(() => {\r\n    const carPackages = vipUserCarList.value || [];\r\n    const cards = [];\r\n\r\n    // 添加已有数据的卡片\r\n    for (let i = 0; i < carPackages.length && i < 3; i++) {\r\n        const car = carPackages[i];\r\n        cards.push({\r\n            ...car,\r\n            warehouseName: car.warehouseName,\r\n            // 判断是否未过期：beginVipTime和endVipTime都为null表示未过期\r\n            isVip: car.beginVipTime !== null && car.endVipTime !== null\r\n        });\r\n    }\r\n\r\n    // 填充空位到3个\r\n    while (cards.length < 3) {\r\n        cards.push({\r\n            plateNo: null,\r\n            warehouseName: null,\r\n            beginVipTime: null,\r\n            endVipTime: null,\r\n            isVip: false\r\n        });\r\n    }\r\n    console.log('cards', cards)\r\n    return cards;\r\n});\r\n\r\n// 计算当前有车辆数量\r\nconst currentCarCount = computed(() => {\r\n    const carPackages = vipUserCarList.value || [];\r\n    return carPackages.length;\r\n});\r\n\r\n// 计算vipType\r\nconst vipType = computed(() => {\r\n    return props.userType === '集团套餐' ? 1 : 2;\r\n});\r\n\r\n// 初始化数据\r\nconst initData = async () => {\r\n    try {\r\n        uni.showLoading({ title: \"加载中...\" });\r\n        await loadVipUserCarList();\r\n        uni.hideLoading();\r\n    } catch (error) {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n            title: error.message || \"数据加载失败\",\r\n            icon: \"none\",\r\n        });\r\n    }\r\n};\r\n\r\n// 加载VIP用户数据\r\nconst loadVipUserCarList = async () => {\r\n    try {\r\n        const res = await getVipUserPackageList(vipType.value);\r\n        vipUserCarList.value = res.data;\r\n    } catch (error) {\r\n        throw new Error(error.message || \"获取VIP用户数据失败\");\r\n    }\r\n};\r\n\r\n// 判断按钮是否可点击\r\nconst isButtonClickable = (cardData, index) => {\r\n    if (cardData.plateNo !== null) {\r\n        return true;\r\n    }\r\n    return index <= currentCarCount.value;\r\n};\r\n\r\n// 获取按钮文字\r\nconst getButtonText = (cardData, index) => {\r\n    if (cardData.plateNo === null) {\r\n        return '购买';\r\n    }\r\n    if (cardData.isVip) {\r\n        return '续费';\r\n    } else {\r\n        return '购买';\r\n    }\r\n};\r\n\r\n// 处理按钮点击\r\nconst handleAction = (cardData, index) => {\r\n    if (!isButtonClickable(cardData, index)) {\r\n        return;\r\n    }\r\n\r\n    // 有数据的卡片，续费或重新购买\r\n    const isRenewal = cardData.isVip;\r\n    const packageOrder = {\r\n        warehouseId: cardData.warehouseId,\r\n        warehouseName: cardData.warehouseName,\r\n        plateNo: cardData.plateNo,\r\n        index: index + 1,   //作为索引，判断是第几辆车\r\n        vipType: vipType.value,\r\n        isRenewal: isRenewal,\r\n        beginVipTime: cardData.beginVipTime,\r\n        expirationTime: cardData.endVipTime,\r\n    };\r\n\r\n    // 根据用户类型设置不同的套餐信息\r\n    if (vipType.value === 2) {\r\n        // VIP客户：固定套餐信息\r\n        packageOrder.packageName = \"VIP年度套餐\";\r\n        packageOrder.packagePrice = 0.01;\r\n        packageOrder.packageDays = 1;\r\n        packageOrder.packageId = 6;\r\n    } else {\r\n        // 集团客户：套餐信息为空，需要在购买页面选择\r\n        packageOrder.packageName = null;\r\n        packageOrder.packagePrice = null;\r\n        packageOrder.packageDays = null;\r\n        packageOrder.packageId = null;\r\n    }\r\n\r\n    console.log('packageOrder', packageOrder)\r\n\r\n    // 跳转到购买页面\r\n    uni.navigateTo({\r\n        url: `/pages/package/packageVipBuy?packageOrder=${encodeURIComponent(\r\n            JSON.stringify(packageOrder)\r\n        )}`,\r\n    });\r\n};\r\n\r\n// 暴露方法给父组件调用\r\ndefineExpose({\r\n    initData\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.parking-vip-package {\r\n    padding: 0 20rpx;\r\n    min-height: 100vh;\r\n}\r\n\r\n.vip-package-list {\r\n    .vip-card {\r\n        display: flex;\r\n        flex-direction: column;\r\n        border-radius: 16rpx;\r\n        overflow: hidden;\r\n        background-image: url('https://test-parknew.lgfw24hours.com:3443/statics/wx/specialVipHeader.png');\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        padding-bottom: 40rpx;\r\n\r\n        .content-section {\r\n            color: #fff;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 20rpx 20rpx 0 20rpx;\r\n\r\n            .vip-info {\r\n                padding: 10rpx 30rpx 0;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n\r\n                .vip-title-container {\r\n                    margin: 5rpx 0;\r\n                    font-size: 32rpx;\r\n                }\r\n\r\n                .vip-plate {\r\n                    font-size: 36rpx;\r\n                    font-weight: bold;\r\n                    margin: 10rpx 0;\r\n                }\r\n\r\n                .vip-expire {\r\n                    font-size: 24rpx;\r\n                }\r\n            }\r\n\r\n            .right-section {\r\n                margin-left: 40rpx;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                .car-image {\r\n                    width: 180rpx;\r\n                    height: 180rpx;\r\n\r\n                    image {\r\n                        width: 100%;\r\n                        height: 100%;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .button-section {\r\n            display: flex;\r\n            justify-content: flex-end;\r\n            margin-right: 40rpx;\r\n\r\n            .vip-link {\r\n                background: #007aff;\r\n                padding: 5rpx 20rpx;\r\n                border-radius: 40rpx;\r\n                text-align: center;\r\n                font-size: 28rpx;\r\n                font-weight: bold;\r\n                color: #fff;\r\n\r\n                &.disabled {\r\n                    background: #ccc;\r\n                    color: #999;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.notice-section {\r\n    margin-top: 30rpx;\r\n    padding: 20rpx;\r\n    background: #fff;\r\n    border-radius: 12rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);\r\n    \r\n    .notice-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin-bottom: 16rpx;\r\n        padding-bottom: 12rpx;\r\n        border-bottom: 1rpx solid #f0f0f0;\r\n    }\r\n    \r\n    .notice-content {\r\n        .notice-text {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            line-height: 1.6;\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'F:/parking/park-uniapp/pages/package/components/ParkingVipPackage.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni", "getVipUserPackageList"], "mappings": ";;;;;;;;;;;;;;AA2DA,UAAM,QAAQ;AAQd,UAAM,iBAAiBA,cAAAA,IAAI,IAAI;AAG/B,UAAM,WAAWC,cAAQ,SAAC,MAAM;AAC5B,YAAM,cAAc,eAAe,SAAS;AAC5C,YAAM,QAAQ,CAAA;AAGd,eAAS,IAAI,GAAG,IAAI,YAAY,UAAU,IAAI,GAAG,KAAK;AAClD,cAAM,MAAM,YAAY,CAAC;AACzB,cAAM,KAAK;AAAA,UACP,GAAG;AAAA,UACH,eAAe,IAAI;AAAA;AAAA,UAEnB,OAAO,IAAI,iBAAiB,QAAQ,IAAI,eAAe;AAAA,QACnE,CAAS;AAAA,MACJ;AAGD,aAAO,MAAM,SAAS,GAAG;AACrB,cAAM,KAAK;AAAA,UACP,SAAS;AAAA,UACT,eAAe;AAAA,UACf,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,OAAO;AAAA,QACnB,CAAS;AAAA,MACJ;AACDC,oBAAAA,2EAAY,SAAS,KAAK;AAC1B,aAAO;AAAA,IACX,CAAC;AAGD,UAAM,kBAAkBD,cAAQ,SAAC,MAAM;AACnC,YAAM,cAAc,eAAe,SAAS;AAC5C,aAAO,YAAY;AAAA,IACvB,CAAC;AAGD,UAAM,UAAUA,cAAQ,SAAC,MAAM;AAC3B,aAAO,MAAM,aAAa,SAAS,IAAI;AAAA,IAC3C,CAAC;AAGD,UAAM,WAAW,YAAY;AACzB,UAAI;AACAC,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,mBAAkB;AACxBA,sBAAG,MAAC,YAAW;AAAA,MAClB,SAAQ,OAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,qBAAqB,YAAY;AACnC,UAAI;AACA,cAAM,MAAM,MAAMC,YAAAA,sBAAsB,QAAQ,KAAK;AACrD,uBAAe,QAAQ,IAAI;AAAA,MAC9B,SAAQ,OAAO;AACZ,cAAM,IAAI,MAAM,MAAM,WAAW,aAAa;AAAA,MACjD;AAAA,IACL;AAGA,UAAM,oBAAoB,CAAC,UAAU,UAAU;AAC3C,UAAI,SAAS,YAAY,MAAM;AAC3B,eAAO;AAAA,MACV;AACD,aAAO,SAAS,gBAAgB;AAAA,IACpC;AAGA,UAAM,gBAAgB,CAAC,UAAU,UAAU;AACvC,UAAI,SAAS,YAAY,MAAM;AAC3B,eAAO;AAAA,MACV;AACD,UAAI,SAAS,OAAO;AAChB,eAAO;AAAA,MACf,OAAW;AACH,eAAO;AAAA,MACV;AAAA,IACL;AAGA,UAAM,eAAe,CAAC,UAAU,UAAU;AACtC,UAAI,CAAC,kBAAkB,UAAU,KAAK,GAAG;AACrC;AAAA,MACH;AAGD,YAAM,YAAY,SAAS;AAC3B,YAAM,eAAe;AAAA,QACjB,aAAa,SAAS;AAAA,QACtB,eAAe,SAAS;AAAA,QACxB,SAAS,SAAS;AAAA,QAClB,OAAO,QAAQ;AAAA;AAAA,QACf,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,cAAc,SAAS;AAAA,QACvB,gBAAgB,SAAS;AAAA,MACjC;AAGI,UAAI,QAAQ,UAAU,GAAG;AAErB,qBAAa,cAAc;AAC3B,qBAAa,eAAe;AAC5B,qBAAa,cAAc;AAC3B,qBAAa,YAAY;AAAA,MACjC,OAAW;AAEH,qBAAa,cAAc;AAC3B,qBAAa,eAAe;AAC5B,qBAAa,cAAc;AAC3B,qBAAa,YAAY;AAAA,MAC5B;AAEDD,oBAAAA,MAAA,MAAA,OAAA,yDAAY,gBAAgB,YAAY;AAGxCA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,6CAA6C;AAAA,UAC9C,KAAK,UAAU,YAAY;AAAA,QACvC,CAAS;AAAA,MACT,CAAK;AAAA,IACL;AAGA,aAAa;AAAA,MACT;AAAA,IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;ACzMD,GAAG,gBAAgB,SAAS;"}