{"version": 3, "file": "package.js", "sources": ["pages/package/package.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFja2FnZS9wYWNrYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"package-container\">\r\n        <!-- 套餐选择头部 -->\r\n        <view class=\"tab-header\">\r\n            <view class=\"tab-item\" :class=\"{ active: selectedPackageType.name === '停车套餐' }\" \r\n            @tap=\"selectPackageType('停车套餐')\">\r\n                <text class=\"tab-text\">停车套餐</text>\r\n            </view>\r\n            <view class=\"tab-divider\"></view>\r\n            <view class=\"tab-item\" :class=\"{ active: selectedPackageType.name === '充电套餐' }\"\r\n             @tap=\"selectPackageType('充电套餐')\">\r\n                <text class=\"tab-text\">充电套餐</text>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 子套餐分类 -->\r\n        <view class=\"sub-type-container\">\r\n            <view \r\n                v-for=\"(item, index) in currentSubTypes\" \r\n                :key=\"index\"\r\n                :class=\"['sub-type-item', selectedSubType.name === item.name ? 'active' : '']\"\r\n                @tap=\"selectSubType(item)\"\r\n            >\r\n                {{ item.name }}\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 套餐内容区域 -->\r\n        <view class=\"package-content\">\r\n            <!-- 停车套餐组件 -->\r\n            <ParkingNormalPackage \r\n                v-if=\"selectedPackageType.name === '停车套餐' && selectedSubType.name === '普通套餐'\" \r\n                :key=\"componentKey\"\r\n                ref=\"parkingNormalRef\"\r\n            />\r\n            <ParkingVipPackage \r\n                v-if=\"selectedPackageType.name === '停车套餐' && (selectedSubType.name === 'VIP套餐' || selectedSubType.name === '集团套餐')\" \r\n                :key=\"componentKey\"\r\n                :userType=\"selectedSubType.name === '集团套餐' ? '集团套餐' : 'VIP套餐'\"\r\n                ref=\"parkingVipRef\"\r\n            />\r\n\r\n            <!-- 充电套餐组件 -->\r\n            <ChargingFastPackage \r\n                v-if=\"selectedPackageType.name === '充电套餐'\" \r\n                :key=\"componentKey\"\r\n                ref=\"chargingFastRef\"\r\n            />\r\n        </view>\r\n            \r\n        <custom-tab-bar></custom-tab-bar>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport CustomTabBar from \"@/components/custom-tab-bar/index.vue\";\r\nimport ParkingNormalPackage from \"./components/ParkingNormalPackage.vue\";\r\nimport ChargingFastPackage from \"./components/ChargingFastPackage.vue\";\r\nimport ParkingVipPackage from \"./components/ParkingVipPackage.vue\";\r\nimport { ref, computed, nextTick } from 'vue';\r\nimport { onShow} from '@dcloudio/uni-app';\r\nimport { getSpecialUser } from '@/api/specialUser';\r\n\r\n// 停车会员(默认空对象/VIP客户/集团客户)\r\nconst specialUser = ref({});\r\n\r\n// 停车套餐子类型 - 根据用户类型动态设置\r\nconst parkingSubTypes = ref([\r\n    { name: '普通套餐' }\r\n]);\r\n\r\n// 充电套餐子类型\r\nconst chargingSubTypes = ref([]);\r\n\r\n// 当前选中的套餐类型（默认值）\r\nconst selectedPackageType = ref({ name: '停车套餐' });\r\nconst selectedSubType = ref({ name: '普通套餐' });\r\n\r\n// 组件key，渲染子页面\r\nconst componentKey = ref(0);\r\n\r\n// 组件引用\r\nconst parkingNormalRef = ref(null);\r\nconst parkingVipRef = ref(null);\r\nconst chargingFastRef = ref(null);\r\n\r\nonShow(() => {\r\n    // 首先从缓存中加载选中的套餐类型和子类型\r\n    loadSelectedTypesFromCache();\r\n\r\n    // 判断当前用户是否是特殊会员\r\n    getSpecialUser().then(res => {\r\n        specialUser.value = res.data || {};\r\n\r\n        // 根据用户类型动态设置停车套餐选项\r\n        if (res.data && res.data.userType) {\r\n            if (res.data.userType === 'VIP客户') {\r\n                parkingSubTypes.value = [\r\n                    { name: 'VIP套餐' },\r\n                    { name: '普通套餐' }\r\n                ];\r\n            } else if (res.data.userType === '集团客户') {\r\n                parkingSubTypes.value = [\r\n                    { name: '集团套餐' },\r\n                    { name: '普通套餐' }\r\n                ];\r\n            } else {\r\n                parkingSubTypes.value = [\r\n                    { name: '普通套餐' }\r\n                ];\r\n            }\r\n        } else {\r\n            parkingSubTypes.value = [\r\n                { name: '普通套餐' }\r\n            ];\r\n        }\r\n\r\n        // 验证并修正缓存中选中的套餐类型和子类型是否符合当前用户权限\r\n        validateAndCorrectSelectedTypes();\r\n\r\n        // 初始化时触发组件重新加载\r\n        forceComponentReload();\r\n    });\r\n});\r\n// 从缓存中读取选中的套餐类型和子类型\r\nconst loadSelectedTypesFromCache = () => {\r\n    try {\r\n        const cachedPackageType = uni.getStorageSync('selectedPackageType');\r\n        const cachedSubType = uni.getStorageSync('selectedSubType');\r\n        \r\n        if (cachedPackageType) {\r\n            selectedPackageType.value = cachedPackageType;\r\n        }\r\n        if (cachedSubType) {\r\n            selectedSubType.value = cachedSubType;\r\n        }\r\n    } catch (error) {\r\n        console.log('读取缓存失败:', error);\r\n    }\r\n};\r\n\r\n// 验证并修正选中的套餐类型是否符合当前用户权限\r\nconst validateAndCorrectSelectedTypes = () => {\r\n    // 如果当前选择的是停车套餐，需要验证子类型权限\r\n    if (selectedPackageType.value.name === '停车套餐') {\r\n        const currentSubTypeNames = parkingSubTypes.value.map(item => item.name);\r\n        \r\n        // 如果当前选中的子类型不在允许的选项中，重置为第一个选项\r\n        if (!currentSubTypeNames.includes(selectedSubType.value.name)) {\r\n            selectedSubType.value = parkingSubTypes.value[0];\r\n            saveSelectedTypesToCache(); \r\n        }\r\n    }\r\n};\r\n\r\n// 保存选中的套餐类型和子类型到缓存\r\nconst saveSelectedTypesToCache = () => {\r\n    try {\r\n        uni.setStorageSync('selectedPackageType', selectedPackageType.value);\r\n        uni.setStorageSync('selectedSubType', selectedSubType.value);\r\n    } catch (error) {\r\n        console.log('保存缓存失败:', error);\r\n    }\r\n};\r\n\r\n// 渲染组件触发页面加载\r\nconst forceComponentReload = () => {\r\n    componentKey.value++;\r\n    // 等待DOM更新后触发子组件的数据重新加载\r\n    nextTick(() => {\r\n        // 增加重试机制，确保组件完全加载\r\n        const tryInitData = (retryCount = 0) => {\r\n            const currentRef = getCurrentComponentRef();\r\n            \r\n            if (currentRef && currentRef.initData) {\r\n                currentRef.initData();\r\n            } else if (retryCount < 5) {\r\n                // 如果组件还没有准备好，等待更长时间后重试\r\n                setTimeout(() => {\r\n                    tryInitData(retryCount + 1);\r\n                }, 200 * (retryCount + 1)); // 递增延迟时间\r\n            }\r\n        };\r\n        \r\n        // 初始延迟\r\n        setTimeout(() => {\r\n            tryInitData();\r\n        }, 150);\r\n    });\r\n};\r\n\r\n// 获取当前显示的组件引用\r\nconst getCurrentComponentRef = () => {\r\n    if (selectedPackageType.value.name === '停车套餐') {\r\n        if (selectedSubType.value.name === '普通套餐') {\r\n            return parkingNormalRef.value;\r\n        } else if (selectedSubType.value.name === 'VIP套餐' || selectedSubType.value.name === '集团套餐') {\r\n            return parkingVipRef.value;\r\n        }\r\n    } else if (selectedPackageType.value.name === '充电套餐') {\r\n        return chargingFastRef.value;\r\n    }\r\n    \r\n    return null;\r\n};\r\n\r\n// 计算当前应该显示的子类型列表\r\nconst currentSubTypes = computed(() => {\r\n    return selectedPackageType.value.name === '停车套餐' ? parkingSubTypes.value : chargingSubTypes.value;\r\n});\r\n\r\n// 选择套餐类型\r\nconst selectPackageType = (typeName) => {\r\n    selectedPackageType.value = { name: typeName };\r\n    selectedSubType.value = currentSubTypes.value[0]; \r\n    saveSelectedTypesToCache();\r\n    forceComponentReload();\r\n};\r\n\r\n// 选择子套餐类型\r\nconst selectSubType = (subType) => {\r\n    if(selectedSubType.value.name === subType.name) return;\r\n    selectedSubType.value = subType;\r\n    saveSelectedTypesToCache();\r\n    forceComponentReload();\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.package-container {\r\n    background: linear-gradient(180deg, #eaf6ff 0%, #e3edff 100%);\r\n}\r\n\r\n.tab-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    background: linear-gradient(135deg, #4BA1FC, #7e6dff);\r\n    padding: 10rpx 30rpx;\r\n    z-index: 10;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.tab-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    font-size: 28rpx;\r\n    color: rgba(255, 255, 255, 0.9);\r\n    padding: 20rpx 0;\r\n    position: relative;\r\n\r\n    .tab-text {\r\n        margin-right: 0;\r\n    }\r\n\r\n    &.active {\r\n        color: #ffffff;\r\n        font-weight: bold;\r\n\r\n        &::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: 8rpx;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            width: 40rpx;\r\n            height: 4rpx;\r\n            background-color: #ffffff;\r\n            border-radius: 2rpx;\r\n        }\r\n    }\r\n}\r\n\r\n.tab-divider {\r\n    width: 2rpx;\r\n    height: 40rpx;\r\n    background-color: rgba(255, 255, 255, 0.7);\r\n    margin: 0 20rpx;\r\n}\r\n\r\n.sub-type-container {\r\n    display: flex;\r\n    padding: 20rpx 40rpx;\r\n    gap: 20rpx;\r\n}\r\n\r\n.sub-type-item {\r\n    padding: 14rpx 30rpx;\r\n    text-align: center;\r\n    background-color: #fff;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    border-radius: 40rpx;\r\n\r\n    &.active {\r\n        background: linear-gradient(135deg, #4BA1FC, #6B73FF);\r\n        color: #fff;\r\n        font-weight: bold;\r\n    }\r\n}\r\n\r\n</style>\r\n\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/package/package.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShow", "getSpecialUser", "uni", "nextTick", "computed"], "mappings": ";;;;;;AAuDA,MAAM,eAAe,MAAW;AAChC,MAAM,uBAAuB,MAAW;AACxC,MAAM,sBAAsB,MAAW;AACvC,MAAM,oBAAoB,MAAW;;;;AAMrC,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,OAAQ;AAAA,IACpB,CAAC;AAGD,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,sBAAsBA,cAAAA,IAAI,EAAE,MAAM,OAAQ,CAAA;AAChD,UAAM,kBAAkBA,cAAAA,IAAI,EAAE,MAAM,OAAQ,CAAA;AAG5C,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAG1B,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAEhCC,kBAAAA,OAAO,MAAM;AAET;AAGAC,qCAAgB,EAAC,KAAK,SAAO;AACzB,oBAAY,QAAQ,IAAI,QAAQ,CAAA;AAGhC,YAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AAC/B,cAAI,IAAI,KAAK,aAAa,SAAS;AAC/B,4BAAgB,QAAQ;AAAA,cACpB,EAAE,MAAM,QAAS;AAAA,cACjB,EAAE,MAAM,OAAQ;AAAA,YACpC;AAAA,UACa,WAAU,IAAI,KAAK,aAAa,QAAQ;AACrC,4BAAgB,QAAQ;AAAA,cACpB,EAAE,MAAM,OAAQ;AAAA,cAChB,EAAE,MAAM,OAAQ;AAAA,YACpC;AAAA,UACA,OAAmB;AACH,4BAAgB,QAAQ;AAAA,cACpB,EAAE,MAAM,OAAQ;AAAA,YACpC;AAAA,UACa;AAAA,QACb,OAAe;AACH,0BAAgB,QAAQ;AAAA,YACpB,EAAE,MAAM,OAAQ;AAAA,UAChC;AAAA,QACS;AAGD;AAGA;MACR,CAAK;AAAA,IACL,CAAC;AAED,UAAM,6BAA6B,MAAM;AACrC,UAAI;AACA,cAAM,oBAAoBC,cAAAA,MAAI,eAAe,qBAAqB;AAClE,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,iBAAiB;AAE1D,YAAI,mBAAmB;AACnB,8BAAoB,QAAQ;AAAA,QAC/B;AACD,YAAI,eAAe;AACf,0BAAgB,QAAQ;AAAA,QAC3B;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAY,MAAA,MAAA,OAAA,oCAAA,WAAW,KAAK;AAAA,MAC/B;AAAA,IACL;AAGA,UAAM,kCAAkC,MAAM;AAE1C,UAAI,oBAAoB,MAAM,SAAS,QAAQ;AAC3C,cAAM,sBAAsB,gBAAgB,MAAM,IAAI,UAAQ,KAAK,IAAI;AAGvE,YAAI,CAAC,oBAAoB,SAAS,gBAAgB,MAAM,IAAI,GAAG;AAC3D,0BAAgB,QAAQ,gBAAgB,MAAM,CAAC;AAC/C;QACH;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,2BAA2B,MAAM;AACnC,UAAI;AACAA,sBAAAA,MAAI,eAAe,uBAAuB,oBAAoB,KAAK;AACnEA,sBAAAA,MAAI,eAAe,mBAAmB,gBAAgB,KAAK;AAAA,MAC9D,SAAQ,OAAO;AACZA,sBAAY,MAAA,MAAA,OAAA,oCAAA,WAAW,KAAK;AAAA,MAC/B;AAAA,IACL;AAGA,UAAM,uBAAuB,MAAM;AAC/B,mBAAa;AAEbC,oBAAAA,WAAS,MAAM;AAEX,cAAM,cAAc,CAAC,aAAa,MAAM;AACpC,gBAAM,aAAa;AAEnB,cAAI,cAAc,WAAW,UAAU;AACnC,uBAAW,SAAQ;AAAA,UACnC,WAAuB,aAAa,GAAG;AAEvB,uBAAW,MAAM;AACb,0BAAY,aAAa,CAAC;AAAA,YAC7B,GAAE,OAAO,aAAa,EAAE;AAAA,UAC5B;AAAA,QACb;AAGQ,mBAAW,MAAM;AACb;QACH,GAAE,GAAG;AAAA,MACd,CAAK;AAAA,IACL;AAGA,UAAM,yBAAyB,MAAM;AACjC,UAAI,oBAAoB,MAAM,SAAS,QAAQ;AAC3C,YAAI,gBAAgB,MAAM,SAAS,QAAQ;AACvC,iBAAO,iBAAiB;AAAA,QACpC,WAAmB,gBAAgB,MAAM,SAAS,WAAW,gBAAgB,MAAM,SAAS,QAAQ;AACxF,iBAAO,cAAc;AAAA,QACxB;AAAA,MACJ,WAAU,oBAAoB,MAAM,SAAS,QAAQ;AAClD,eAAO,gBAAgB;AAAA,MAC1B;AAED,aAAO;AAAA,IACX;AAGA,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACnC,aAAO,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,QAAQ,iBAAiB;AAAA,IAChG,CAAC;AAGD,UAAM,oBAAoB,CAAC,aAAa;AACpC,0BAAoB,QAAQ,EAAE,MAAM,SAAQ;AAC5C,sBAAgB,QAAQ,gBAAgB,MAAM,CAAC;AAC/C;AACA;IACJ;AAGA,UAAM,gBAAgB,CAAC,YAAY;AAC/B,UAAG,gBAAgB,MAAM,SAAS,QAAQ;AAAM;AAChD,sBAAgB,QAAQ;AACxB;AACA;IACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChOA,GAAG,WAAW,eAAe;"}