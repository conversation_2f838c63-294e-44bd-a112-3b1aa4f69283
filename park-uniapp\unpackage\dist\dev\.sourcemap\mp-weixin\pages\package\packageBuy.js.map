{"version": 3, "file": "packageBuy.js", "sources": ["pages/package/packageBuy.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFja2FnZS9wYWNrYWdlQnV5LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"package-buy\">\r\n        <view class=\"notice-card\">\r\n            <view class=\"notice-content\">\r\n                <text class=\"notice-text\">\r\n                    {{ noticeText }}\r\n                </text>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 订单信息卡片 -->\r\n        <view class=\"cell\">\r\n            <view class=\"content\">\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/changku.png\" mode=\"aspectFit\"></image>场库名称\r\n                    </view>\r\n                    <view class=\"word red\"> {{ packageOrder.warehouseName || '-' }}</view>\r\n                </view>\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/chepai.png\" mode=\"aspectFit\"></image>车牌牌号\r\n                    </view>\r\n                    <view class=\"word\"> {{ packageOrder.plateNo || '-' }} </view>\r\n                </view>\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/taocantype.png\" mode=\"aspectFit\"></image>套餐类型\r\n                    </view>\r\n                    <view class=\"word\"> {{ packageOrder.packageName || '-' }} </view>\r\n                </view>\r\n                <!-- 如果是续费，显示当前到期时间 -->\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>开始时间\r\n                    </view>\r\n                    <view class=\"word\" :class=\"[canSelectTime ? 'clickable-text' : '']\">\r\n                        <!-- 日期选择器 -->\r\n                        <picker v-if=\"canSelectTime\" mode=\"date\" :value=\"selectedDate\" :start=\"minDate\" :end=\"maxDate\"\r\n                            @change=\"onDateChange\">\r\n                            <view class=\"picker-display\">\r\n                                {{ packageOrder.beginVipTime || '请选择开始时间' }}\r\n                            </view>\r\n                        </picker>\r\n                        <view v-else class=\"picker-display\">\r\n                            {{ packageOrder.beginVipTime || '请选择开始时间' }}\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <view v-if=\"packageOrder.isRenewal\" class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>当前到期时间\r\n                    </view>\r\n                    <view class=\"word\"> {{ packageOrder.expirationTime }} </view>\r\n                </view>\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>{{ packageOrder.isRenewal ?\r\n                        '续费后到期时间' : '到期时间' }}\r\n                    </view>\r\n                    <view class=\"word\"> {{ (packageOrder.newExpirationTime || packageOrder.expirationTime) || '--' }} </view>\r\n                </view>\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/taocanprice.png\" mode=\"aspectFit\"></image>套餐价格\r\n                    </view>\r\n                    <view class=\"word\">\r\n                        <text class=\"tips\"> ¥ </text> {{ packageOrder.packagePrice || '-' }}\r\n                    </view>\r\n                </view>\r\n                <view class=\"content_item\">\r\n                    <view class=\"title\">\r\n                        <image src=\"/static/package/shifu.png\" mode=\"aspectFit\"></image>实付金额\r\n                    </view>\r\n                    <view class=\"word money\">\r\n                        <text class=\"tips red\"> ¥ </text> {{ packageOrder.packagePrice || '0.00' }}\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            <button @tap=\"submitOrder\" class=\"btn\">{{ packageOrder.isRenewal ? '确认续费' : '提交订单' }}</button>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed} from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { createOrder, updateOrder} from '@/api/package';\r\nimport { checkCarInWarehouse } from '@/api/package';\r\nimport { getOpenid } from '@/api/login';\r\nimport { isSameDate, extractDatePart, formatDate, createDate } from '@/utils/utils';\r\n\r\nconst packageOrder = ref({});\r\n// 是否已是会员\r\nconst isExistingMember = ref(false);\r\n// 是否在场\r\nconst isCarInWarehouse = ref(false);\r\nconst packageJudge = ref({});\r\n// 是否可选择时间\r\nconst canSelectTime = ref(false);   \r\nconst selectedDate = ref('');\r\n\r\n// 日期范围设置\r\nconst minDate = ref('');\r\nconst maxDate = ref(''); \r\n\r\n// 计算购买前提示文本\r\nconst noticeText = computed(() => {\r\n    if (packageOrder.value.isRenewal) {\r\n        return `当前选择的续费套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。\r\n        续费将在原有套餐到期日期基础上延长，无法修改时间`;\r\n    }\r\n    \r\n    if (isCarInWarehouse.value) {\r\n        // 检查是否有会员过期或临停缴费记录\r\n        const hasExpiredMember = packageJudge.value.endVipTime !== null;\r\n        const hasParkingPayment = packageJudge.value.endParkingTime !== null;\r\n        \r\n        if (hasExpiredMember || hasParkingPayment) {\r\n            return `当前选择的套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。\r\n            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;\r\n        } else {\r\n            return `当前选择的套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。\r\n            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;\r\n        }\r\n    }\r\n    \r\n    return `当前选择的开通套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。\r\n    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;\r\n});\r\n\r\nonLoad((options) => {\r\n    packageOrder.value = JSON.parse(options.packageOrder);\r\n    // 是否已是会员，是的话续费订单，否则是首次开通订单\r\n    isExistingMember.value = packageOrder.value.isRenewal;\r\n    console.log('isExistingMember.value: ', isExistingMember.value)\r\n    console.log('packageOrder.value: ', packageOrder.value) \r\n\r\n    // 判断是否是续费订单\r\n    if (isExistingMember.value) {\r\n        handleRenewalOrder();\r\n    } else {\r\n        // 首次开通，需要检查车辆是否在场\r\n        checkCarInWarehouse({\r\n            plateNo: packageOrder.value.plateNo,\r\n            warehouseId: packageOrder.value.warehouseId\r\n        }).then(res => {\r\n            packageJudge.value = res.data;\r\n            isCarInWarehouse.value = res.data && res.data.isCarInWarehouse;\r\n            handleFirstTimeOrder();\r\n        }).catch(err => {\r\n            console.log('检查车辆是否在场失败：', err);\r\n            isCarInWarehouse.value = false;\r\n            handleFirstTimeOrder();\r\n        });\r\n    }\r\n});\r\n\r\n// 处理续费订单\r\nconst handleRenewalOrder = () => {\r\n    // 验证续费订单必须有原开始时间和结束时间\r\n    if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {\r\n        uni.showToast({\r\n            title: '续费订单数据异常，请重新操作',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n        return;\r\n    }\r\n    \r\n    // 续费订单：开始时间保持原开始时间，结束时间在原结束时间基础上延长\r\n    const originalEndDate = new Date(packageOrder.value.expirationTime);\r\n    const newEndDate = new Date(originalEndDate);\r\n    newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);\r\n    \r\n            const endDateStr = formatDate(newEndDate);\r\n        packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;\r\n    \r\n    // 续费订单不允许选择时间\r\n    packageOrder.value.canSelectTime = false;\r\n};\r\n\r\n// 处理会员开通\r\nconst handleFirstTimeOrder = () => {\r\n    if (isCarInWarehouse.value) {\r\n        handleCarInWarehouse();\r\n    } else {\r\n        handleCarNotInWarehouse();\r\n    }\r\n};\r\n\r\n// 处理车辆在场的开通会员\r\nconst handleCarInWarehouse = () => {\r\n    // 检查是否有会员过期记录\r\n    const hasExpiredMember = packageJudge.value.endVipTime !== null;\r\n    \r\n    if (hasExpiredMember && packageJudge.value.finalBeginTime) {\r\n        // 如果有会员过期记录，需要判断finalBeginTime是来自会员过期还是临停缴费\r\n        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);\r\n        let startDate = new Date(finalBeginDate);\r\n        \r\n        // 判断finalBeginTime是否来自临停缴费（临停缴费时间更晚）\r\n        const hasParkingPayment = packageJudge.value.endParkingTime !== null;\r\n        const isFinalBeginFromParking = hasParkingPayment && \r\n            packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;\r\n        \r\n        if (isFinalBeginFromParking) {\r\n            // 如果finalBeginTime来自临停缴费时间，使用finalBeginTime的0点（不加一天）\r\n            startDate.setHours(0, 0, 0, 0);\r\n        } else {\r\n            // 如果finalBeginTime来自会员过期时间，使用finalBeginTime+1天的0点\r\n            startDate.setDate(finalBeginDate.getDate() + 1);\r\n            startDate.setHours(0, 0, 0, 0);\r\n        }\r\n        \r\n        const startDateStr = formatDate(startDate);\r\n        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;\r\n        \r\n        // 计算结束时间：需要判断开始时间是否是今天\r\n        const today = new Date();\r\n        const endDate = new Date(startDate);\r\n        const isToday = isSameDate(startDate, today);\r\n        \r\n        if (isToday) {\r\n            // 如果是今天，送用户0-1天优惠\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);\r\n        } else {\r\n            // 如果不是今天，正常计算（不送优惠天数）\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);\r\n        }\r\n        \r\n        const endDateStr = formatDate(endDate);\r\n        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;\r\n    } else {\r\n        // 没有会员过期记录，使用接口返回的finalBeginTime作为开始时间\r\n        if (packageJudge.value.finalBeginTime) {\r\n            // 将finalBeginTime转换为日期部分并设置为00:00:00\r\n            const datePart = extractDatePart(packageJudge.value.finalBeginTime);\r\n            packageOrder.value.beginVipTime = `${datePart} 00:00:00`;\r\n            \r\n            // 计算结束时间：开始时间+套餐天数（车辆在场额外优惠）\r\n            const startDate = new Date(packageOrder.value.beginVipTime);\r\n            const endDate = new Date(startDate);\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);\r\n            \r\n            const endDateStr = formatDate(endDate);\r\n            packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;\r\n        } else {\r\n            uni.showToast({\r\n                title: '参数错误，请联系客服',\r\n                icon: 'none',\r\n                duration: 2000\r\n            });\r\n            return;\r\n        }\r\n    }\r\n    \r\n    packageOrder.value.canSelectTime = false;\r\n};\r\n\r\n// 处理车辆不在场开通会员\r\nconst handleCarNotInWarehouse = () => {\r\n    // 车辆不在场，允许选择开始时间\r\n    canSelectTime.value = true;\r\n    \r\n    // 设置默认开始时间为今天（可享受额外优惠）\r\n    const today = new Date();\r\n    const dateStr = formatDate(today);\r\n    // 会员开始时间\r\n    packageOrder.value.beginVipTime = `${dateStr} 00:00:00`;\r\n    \r\n    // 设置(开始时间）选择范围\r\n    updateDateRange();\r\n    \r\n    // 计算结束时间\r\n    calculateEndDate();\r\n    \r\n};\r\n\r\n// 设置日期选择范围\r\nconst updateDateRange = () => {\r\n    // 如果不允许选择时间，不需要设置日期范围\r\n    if (!canSelectTime.value) {\r\n        return;\r\n    }\r\n    \r\n    // 最小日期为今天\r\n    const today = new Date();\r\n    minDate.value = formatDate(today);\r\n    \r\n    // 最大日期为3个月后 - 使用明确的日期计算\r\n    const maxDateObj = new Date();\r\n    maxDateObj.setMonth(today.getMonth() + 3);\r\n    maxDate.value = formatDate(maxDateObj);\r\n    \r\n    // 设置默认选中日期\r\n    selectedDate.value = formatDate(today);\r\n    \r\n};\r\n\r\n// 日期选择改变\r\nconst onDateChange = (e) => {\r\n    if (!canSelectTime.value) {\r\n        return;\r\n    }\r\n    \r\n    // 原生picker的事件格式\r\n    const selectedDateStr = e.detail.value;\r\n    \r\n    // 判断选择的日期是否是今天\r\n    const selectedDateObj = createDate(selectedDateStr);\r\n    const today = new Date();\r\n    const isToday = isSameDate(selectedDateObj, today);\r\n    \r\n    // 为选择的日期添加00:00:00时间\r\n    packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`;\r\n    \r\n    // 更新selectedDate的值\r\n    selectedDate.value = selectedDateStr;\r\n    \r\n    // 计算到期时间\r\n    calculateEndDate();\r\n};\r\n\r\n// 计算到期时间（仅用于首次开通）\r\nconst calculateEndDate = () => {\r\n    if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {\r\n        const startDate = new Date(packageOrder.value.beginVipTime);\r\n        const today = new Date();\r\n        const endDate = new Date(startDate);\r\n        \r\n        // 判断开始时间是否是今天（使用更可靠的日期比较方法）\r\n        const isToday = isSameDate(startDate, today);\r\n        \r\n        if (isToday) {\r\n            // 如果是今天，送用户0-1天，从第二天开始算天数\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);\r\n        } else {\r\n            // 如果不是今天，正常计算（开始日期+套餐天数-1天）\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);\r\n        }\r\n\r\n        const endDateStr = formatDate(endDate);\r\n        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;\r\n    }\r\n};\r\n\r\n\r\n\r\n// 提交订单\r\nconst submitOrder = () => {\r\n    // 校验到期时间不能早于当前时间\r\n    const currentTime = new Date();\r\n    let endTime;\r\n    \r\n    // 根据是否为续费选择相应的到期时间\r\n    if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {\r\n        endTime = new Date(packageOrder.value.newExpirationTime);\r\n    } else if (packageOrder.value.expirationTime) {\r\n        endTime = new Date(packageOrder.value.expirationTime);\r\n    } else {\r\n        uni.showToast({\r\n            title: '套餐到期时间异常，请重新选择',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n        return;\r\n    }\r\n    \r\n    // 检查到期时间是否早于当前时间\r\n    if (endTime <= currentTime) {\r\n        uni.showToast({\r\n            title: '套餐到期时间不能早于当前时间，请重新选择',\r\n            icon: 'none',\r\n            duration: 3000\r\n        });\r\n        return;\r\n    }\r\n    uni.login({\r\n        success: async (loginRes) => {\r\n            try {\r\n                const openidRes = await getOpenid({\r\n                    wxCode: loginRes.code\r\n                })\r\n                \r\n                // 将openid存储到packageOrder中\r\n                packageOrder.value.openid = openidRes.data\r\n                \r\n                // 继续创建订单\r\n                createOrderWithOpenid()\r\n            } catch (error) {\r\n                console.error('获取openid失败:', error)\r\n                uni.showToast({\r\n                    title: '获取用户信息失败',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            }\r\n        },\r\n        fail: (error) => {\r\n            console.error('登录失败:', error)\r\n            uni.showToast({\r\n                title: '登录失败，请重试',\r\n                icon: 'none',\r\n                duration: 2000\r\n            })\r\n        }\r\n    })\r\n}\r\n\r\n// 创建订单（在获取openid后调用）\r\nconst createOrderWithOpenid = () => {\r\n    uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n    })\r\n    \r\n    // console.log('packageOrder.value: ', packageOrder.value)\r\n    createOrder(packageOrder.value).then(res => {\r\n        console.log('创建订单 res: ', res)\r\n        if (res.data.needPay) {\r\n            uni.requestPayment({\r\n                timeStamp: res.data.timeStamp,\r\n                nonceStr: res.data.nonceStr,\r\n                package: res.data.package,\r\n                signType: res.data.signType,\r\n                paySign: res.data.paySign,\r\n                success: function (result) {\r\n                    // 延迟一下显示toast，避免与complete中的hideLoading冲突\r\n                    uni.hideLoading()\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付成功~',\r\n                            icon: 'none',\r\n                            duration: 2000\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                },\r\n                fail: function (err) {\r\n                    uni.hideLoading()\r\n                    console.log('支付失败的回调：', err)\r\n\r\n                    // 调用更新订单接口，将状态改为已取消\r\n                    if (res.data.orderId) {\r\n                        updateOrder({\r\n                            id: res.data.orderId,\r\n                            payStatus: 3  // 已取消\r\n                        }).then(updateRes => {\r\n                            console.log('订单状态更新为已取消：', updateRes)\r\n                        }).catch(updateErr => {\r\n                            console.log('订单状态更新失败：', updateErr)\r\n                            // 即使更新失败也不影响用户体验\r\n                        })\r\n                    }\r\n\r\n                    // 延迟一下显示toast，避免与hideLoading冲突\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付失败',\r\n                            icon: 'none',\r\n                            duration: 1500\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                },\r\n                complete: function (res) {\r\n                    uni.hideLoading()\r\n                }\r\n            })\r\n        } else {\r\n            uni.hideLoading()\r\n            setTimeout(() => {\r\n                uni.showToast({\r\n                    title: '开通成功~',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            }, 100)\r\n            setTimeout(() => {\r\n                uni.navigateBack()\r\n            }, 2000)\r\n        }\r\n    }).catch(err => {\r\n        console.log(err);\r\n        uni.hideLoading()\r\n        setTimeout(() => {\r\n            uni.showToast({\r\n                title: '订单创建失败',\r\n                icon: 'none',\r\n                duration: 1500\r\n            })\r\n        }, 100)\r\n        setTimeout(() => {\r\n            uni.navigateBack()\r\n        }, 2000)\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.package-buy {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n    padding: 20rpx;\r\n}\r\n.notice-card {\r\n    background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .notice-content {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 12rpx;\r\n        \r\n        .notice-text {\r\n            font-size: 26rpx;\r\n            color: #e65100;\r\n            line-height: 1.5;\r\n            flex: 1;\r\n        }\r\n    }\r\n}\r\n\r\n.cell {\r\n    background-color: #fff;\r\n    border-radius: 20rpx;\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content {\r\n    margin-bottom: 40rpx;\r\n}\r\n\r\n.content_item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    \r\n    image {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        margin-right: 15rpx;\r\n    }\r\n}\r\n\r\n.word {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    \r\n    &.red {\r\n        color: #ff4757;\r\n        font-weight: bold;\r\n    }\r\n    \r\n    &.money {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #ff4757;\r\n    }\r\n    \r\n    &.clickable-text {\r\n        color: #4BA1FC;\r\n        font-weight: 500;\r\n        text-decoration: underline;\r\n        text-decoration-color: rgba(75, 161, 252, 0.3);\r\n        text-underline-offset: 4rpx;\r\n    }\r\n    \r\n    .picker-display {\r\n        width: 100%;\r\n        text-align: right;\r\n        color: inherit;\r\n    }\r\n    \r\n    &.dikou {\r\n        display: flex;\r\n        align-items: center;\r\n        color: #ff6b35;\r\n        \r\n        .desc {\r\n            font-size: 24rpx;\r\n            margin-right: 10rpx;\r\n        }\r\n        \r\n        image {\r\n            width: 24rpx;\r\n            height: 24rpx;\r\n            margin-left: 10rpx;\r\n        }\r\n    }\r\n    \r\n    .disabled-tip {\r\n        font-size: 22rpx;\r\n        color: #999;\r\n        margin-left: 10rpx;\r\n    }\r\n}\r\n\r\n.tips {\r\n    font-size: 24rpx;\r\n    \r\n    &.red {\r\n        color: #ff4757;\r\n    }\r\n    \r\n    &.orange {\r\n        color: #ff6b35;\r\n    }\r\n}\r\n\r\n.btn {\r\n    width: 100%;\r\n    background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);;\r\n    color: #fff;\r\n    border-radius: 44rpx;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    border: none;\r\n    box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\r\n    \r\n    &:active {\r\n        transform: translateY(1rpx);\r\n        box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);\r\n    }\r\n}\r\n\r\n// 弹窗样式\r\n.popup-content {\r\n    background-color: #fff;\r\n    border-radius: 20rpx;\r\n    padding: 60rpx 40rpx 40rpx;\r\n    width: 600rpx;\r\n    text-align: center;\r\n}\r\n\r\n.popup-title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 30rpx;\r\n}\r\n\r\n.popup-desc {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    margin-bottom: 50rpx;\r\n}\r\n\r\n.popup-buttons {\r\n    display: flex;\r\n    gap: 30rpx;\r\n}\r\n\r\n.popup-btn {\r\n    flex: 1;\r\n    height: 80rpx;\r\n    border-radius: 40rpx;\r\n    font-size: 28rpx;\r\n    border: none;\r\n    \r\n    &.cancel {\r\n        background-color: #f5f5f5;\r\n        color: #666;\r\n    }\r\n    \r\n    &.confirm {\r\n        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);\r\n        color: #fff;\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/package/packageBuy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "uni", "checkCarInWarehouse", "formatDate", "isSameDate", "extractDatePart", "createDate", "getOpenid", "createOrder", "updateOrder", "res"], "mappings": ";;;;;;;;;AA4FA,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAE3B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAElC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAE3B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAG3B,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,UAAM,UAAUA,cAAAA,IAAI,EAAE;AAGtB,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAC9B,UAAI,aAAa,MAAM,WAAW;AAC9B,eAAO,cAAc,aAAa,MAAM,eAAe,EAAE,IAAI,aAAa,MAAM,eAAe,EAAE;AAAA;AAAA,MAEpG;AAED,UAAI,iBAAiB,OAAO;AAExB,cAAM,mBAAmB,aAAa,MAAM,eAAe;AAC3D,cAAM,oBAAoB,aAAa,MAAM,mBAAmB;AAEhE,YAAI,oBAAoB,mBAAmB;AACvC,iBAAO,YAAY,aAAa,MAAM,eAAe,EAAE,IAAI,aAAa,MAAM,eAAe,EAAE;AAAA;AAAA,QAE3G,OAAe;AACH,iBAAO,YAAY,aAAa,MAAM,eAAe,EAAE,IAAI,aAAa,MAAM,eAAe,EAAE;AAAA,oCACvE,aAAa,MAAM,cAAc;AAAA,QAC5D;AAAA,MACJ;AAED,aAAO,cAAc,aAAa,MAAM,eAAe,EAAE,IAAI,aAAa,MAAM,eAAe,EAAE;AAAA;AAAA,IAErG,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAChB,mBAAa,QAAQ,KAAK,MAAM,QAAQ,YAAY;AAEpD,uBAAiB,QAAQ,aAAa,MAAM;AAC5CC,oBAAY,MAAA,MAAA,OAAA,uCAAA,4BAA4B,iBAAiB,KAAK;AAC9DA,oBAAA,MAAA,MAAA,OAAA,uCAAY,wBAAwB,aAAa,KAAK;AAGtD,UAAI,iBAAiB,OAAO;AACxB;MACR,OAAW;AAEHC,wCAAoB;AAAA,UAChB,SAAS,aAAa,MAAM;AAAA,UAC5B,aAAa,aAAa,MAAM;AAAA,QAC5C,CAAS,EAAE,KAAK,SAAO;AACX,uBAAa,QAAQ,IAAI;AACzB,2BAAiB,QAAQ,IAAI,QAAQ,IAAI,KAAK;AAC9C;QACZ,CAAS,EAAE,MAAM,SAAO;AACZD,wBAAY,MAAA,MAAA,OAAA,uCAAA,eAAe,GAAG;AAC9B,2BAAiB,QAAQ;AACzB;QACZ,CAAS;AAAA,MACJ;AAAA,IACL,CAAC;AAGD,UAAM,qBAAqB,MAAM;AAE7B,UAAI,CAAC,aAAa,MAAM,gBAAgB,CAAC,aAAa,MAAM,gBAAgB;AACxEA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAGD,YAAM,kBAAkB,IAAI,KAAK,aAAa,MAAM,cAAc;AAClE,YAAM,aAAa,IAAI,KAAK,eAAe;AAC3C,iBAAW,QAAQ,gBAAgB,QAAS,IAAG,aAAa,MAAM,WAAW;AAErE,YAAM,aAAaE,uBAAW,UAAU;AAC5C,mBAAa,MAAM,oBAAoB,GAAG,UAAU;AAGxD,mBAAa,MAAM,gBAAgB;AAAA,IACvC;AAGA,UAAM,uBAAuB,MAAM;AAC/B,UAAI,iBAAiB,OAAO;AACxB;MACR,OAAW;AACH;MACH;AAAA,IACL;AAGA,UAAM,uBAAuB,MAAM;AAE/B,YAAM,mBAAmB,aAAa,MAAM,eAAe;AAE3D,UAAI,oBAAoB,aAAa,MAAM,gBAAgB;AAEvD,cAAM,iBAAiB,IAAI,KAAK,aAAa,MAAM,cAAc;AACjE,YAAI,YAAY,IAAI,KAAK,cAAc;AAGvC,cAAM,oBAAoB,aAAa,MAAM,mBAAmB;AAChE,cAAM,0BAA0B,qBAC5B,aAAa,MAAM,mBAAmB,aAAa,MAAM;AAE7D,YAAI,yBAAyB;AAEzB,oBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QACzC,OAAe;AAEH,oBAAU,QAAQ,eAAe,QAAS,IAAG,CAAC;AAC9C,oBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QAChC;AAED,cAAM,eAAeA,uBAAW,SAAS;AACzC,qBAAa,MAAM,eAAe,GAAG,YAAY;AAGjD,cAAM,QAAQ,oBAAI;AAClB,cAAM,UAAU,IAAI,KAAK,SAAS;AAClC,cAAM,UAAUC,YAAAA,WAAW,WAAW,KAAK;AAE3C,YAAI,SAAS;AAET,kBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AAAA,QAChF,OAAe;AAEH,kBAAQ,QAAQ,UAAU,QAAO,IAAK,aAAa,MAAM,cAAc,CAAC;AAAA,QAC3E;AAED,cAAM,aAAaD,uBAAW,OAAO;AACrC,qBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,MACzD,OAAW;AAEH,YAAI,aAAa,MAAM,gBAAgB;AAEnC,gBAAM,WAAWE,YAAe,gBAAC,aAAa,MAAM,cAAc;AAClE,uBAAa,MAAM,eAAe,GAAG,QAAQ;AAG7C,gBAAM,YAAY,IAAI,KAAK,aAAa,MAAM,YAAY;AAC1D,gBAAM,UAAU,IAAI,KAAK,SAAS;AAClC,kBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AAEpE,gBAAM,aAAaF,uBAAW,OAAO;AACrC,uBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,QAC7D,OAAe;AACHF,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AACD;AAAA,QACH;AAAA,MACJ;AAED,mBAAa,MAAM,gBAAgB;AAAA,IACvC;AAGA,UAAM,0BAA0B,MAAM;AAElC,oBAAc,QAAQ;AAGtB,YAAM,QAAQ,oBAAI;AAClB,YAAM,UAAUE,uBAAW,KAAK;AAEhC,mBAAa,MAAM,eAAe,GAAG,OAAO;AAG5C;AAGA;IAEJ;AAGA,UAAM,kBAAkB,MAAM;AAE1B,UAAI,CAAC,cAAc,OAAO;AACtB;AAAA,MACH;AAGD,YAAM,QAAQ,oBAAI;AAClB,cAAQ,QAAQA,uBAAW,KAAK;AAGhC,YAAM,aAAa,oBAAI;AACvB,iBAAW,SAAS,MAAM,SAAU,IAAG,CAAC;AACxC,cAAQ,QAAQA,uBAAW,UAAU;AAGrC,mBAAa,QAAQA,uBAAW,KAAK;AAAA,IAEzC;AAGA,UAAM,eAAe,CAAC,MAAM;AACxB,UAAI,CAAC,cAAc,OAAO;AACtB;AAAA,MACH;AAGD,YAAM,kBAAkB,EAAE,OAAO;AAGjC,YAAM,kBAAkBG,uBAAW,eAAe;AAClD,YAAM,QAAQ,oBAAI;AACFF,kBAAU,WAAC,iBAAiB,KAAK;AAGjD,mBAAa,MAAM,eAAe,GAAG,eAAe;AAGpD,mBAAa,QAAQ;AAGrB;IACJ;AAGA,UAAM,mBAAmB,MAAM;AAC3B,UAAI,aAAa,MAAM,gBAAgB,aAAa,MAAM,aAAa;AACnE,cAAM,YAAY,IAAI,KAAK,aAAa,MAAM,YAAY;AAC1D,cAAM,QAAQ,oBAAI;AAClB,cAAM,UAAU,IAAI,KAAK,SAAS;AAGlC,cAAM,UAAUA,YAAAA,WAAW,WAAW,KAAK;AAE3C,YAAI,SAAS;AAET,kBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AAAA,QAChF,OAAe;AAEH,kBAAQ,QAAQ,UAAU,QAAO,IAAK,aAAa,MAAM,cAAc,CAAC;AAAA,QAC3E;AAED,cAAM,aAAaD,uBAAW,OAAO;AACrC,qBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,MACpD;AAAA,IACL;AAKA,UAAM,cAAc,MAAM;AAEtB,YAAM,cAAc,oBAAI;AACxB,UAAI;AAGJ,UAAI,aAAa,MAAM,aAAa,aAAa,MAAM,mBAAmB;AACtE,kBAAU,IAAI,KAAK,aAAa,MAAM,iBAAiB;AAAA,MAC/D,WAAe,aAAa,MAAM,gBAAgB;AAC1C,kBAAU,IAAI,KAAK,aAAa,MAAM,cAAc;AAAA,MAC5D,OAAW;AACHF,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAGD,UAAI,WAAW,aAAa;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AACDA,oBAAAA,MAAI,MAAM;AAAA,QACN,SAAS,OAAO,aAAa;AACzB,cAAI;AACA,kBAAM,YAAY,MAAMM,oBAAU;AAAA,cAC9B,QAAQ,SAAS;AAAA,YACrC,CAAiB;AAGD,yBAAa,MAAM,SAAS,UAAU;AAGtC,kCAAuB;AAAA,UAC1B,SAAQ,OAAO;AACZN,0BAAAA,4DAAc,eAAe,KAAK;AAClCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACJ;AAAA,QACJ;AAAA,QACD,MAAM,CAAC,UAAU;AACbA,wBAAAA,MAAc,MAAA,SAAA,uCAAA,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,wBAAwB,MAAM;AAChCA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AAGDO,kBAAAA,YAAY,aAAa,KAAK,EAAE,KAAK,SAAO;AACxCP,sBAAAA,MAAY,MAAA,OAAA,uCAAA,cAAc,GAAG;AAC7B,YAAI,IAAI,KAAK,SAAS;AAClBA,wBAAAA,MAAI,eAAe;AAAA,YACf,WAAW,IAAI,KAAK;AAAA,YACpB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,SAAS,SAAU,QAAQ;AAEvBA,4BAAAA,MAAI,YAAa;AACjB,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YACV;AAAA,YACD,MAAM,SAAU,KAAK;AACjBA,4BAAAA,MAAI,YAAa;AACjBA,4BAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY,GAAG;AAG3B,kBAAI,IAAI,KAAK,SAAS;AAClBQ,wCAAY;AAAA,kBACR,IAAI,IAAI,KAAK;AAAA,kBACb,WAAW;AAAA;AAAA,gBACvC,CAAyB,EAAE,KAAK,eAAa;AACjBR,gCAAAA,MAAY,MAAA,OAAA,uCAAA,eAAe,SAAS;AAAA,gBAChE,CAAyB,EAAE,MAAM,eAAa;AAClBA,gCAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,SAAS;AAAA,gBAE9D,CAAyB;AAAA,cACJ;AAGD,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YACV;AAAA,YACD,UAAU,SAAUS,MAAK;AACrBT,4BAAAA,MAAI,YAAa;AAAA,YACpB;AAAA,UACjB,CAAa;AAAA,QACb,OAAe;AACHA,wBAAAA,MAAI,YAAa;AACjB,qBAAW,MAAM;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACJ,GAAE,GAAG;AACN,qBAAW,MAAM;AACbA,0BAAAA,MAAI,aAAc;AAAA,UACrB,GAAE,GAAI;AAAA,QACV;AAAA,MACT,CAAK,EAAE,MAAM,SAAO;AACZA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,GAAG;AACfA,sBAAAA,MAAI,YAAa;AACjB,mBAAW,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ,GAAE,GAAG;AACN,mBAAW,MAAM;AACbA,wBAAAA,MAAI,aAAc;AAAA,QACrB,GAAE,GAAI;AAAA,MACf,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpfA,GAAG,WAAW,eAAe;"}