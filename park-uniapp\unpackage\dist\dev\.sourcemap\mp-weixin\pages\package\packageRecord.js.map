{"version": 3, "file": "packageRecord.js", "sources": ["pages/package/packageRecord.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFja2FnZS9wYWNrYWdlUmVjb3JkLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"package-record\">\r\n        <!-- 用户类型筛选 - 仅VIP和集团用户显示 -->\r\n        <view class=\"user-type-filter\" v-if=\"showUserTypeFilter\">\r\n            <view \r\n                v-for=\"userType in userTypeFilters\" \r\n                :key=\"userType.value\"\r\n                class=\"filter-tab\" \r\n                :class=\"{ active: activeUserTypeFilter === userType.value }\" \r\n                @tap=\"setUserTypeFilter(userType.value)\"\r\n            >\r\n                {{ userType.label }}\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 订单状态筛选 -->\r\n        <view class=\"status-filter\"\r\n            :style=\"{ top: (specialUser && specialUser.userType && (specialUser.userType === 'VIP客户' || specialUser.userType === '集团客户')) ? '88rpx' : '0' }\">\r\n            <view \r\n                v-for=\"status in statusFilters\" \r\n                :key=\"status.value\"\r\n                class=\"filter-tab\" \r\n                :class=\"{ active: activeStatusFilter === status.value }\" \r\n                @tap=\"setStatusFilter(status.value)\"\r\n            >\r\n                {{ status.label }}\r\n            </view>\r\n        </view>\r\n\r\n        <view class=\"content\"\r\n            :style=\"{ paddingTop: (specialUser && specialUser.userType && (specialUser.userType === 'VIP客户' || specialUser.userType === '集团客户')) ? '196rpx' : '108rpx' }\">\r\n            <template v-if=\"recordList.length > 0\">\r\n                <view class=\"record-list\">\r\n                    <view class=\"record-item\" v-for=\"item in recordList\" :key=\"item.id\">\r\n                        <view class=\"record-card\">\r\n                            <view class=\"card-header\">\r\n                                <view class=\"order-info\">\r\n                                    <text class=\"order-no\">{{ item.tradeId || '订单号待生成' }}</text>\r\n                                </view>\r\n                                <view class=\"header-right\">\r\n                                    <view class=\"status-row\">\r\n                                        <view class=\"status-badge\" :class=\"getStatusClass(item.payStatus)\">\r\n                                            {{ getStatusText(item.payStatus) }}\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <view class=\"card-body\">\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">停车场:</text>\r\n                                    <text class=\"info-value\">{{ item.warehouseName }}</text>\r\n                                    <text class=\"info-label plate-label\">车牌:</text>\r\n                                    <text class=\"info-value\">{{ item.plateNo }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">会员类型:</text>\r\n                                    <text class=\"info-value\">{{ item.packageName }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">购买时间:</text>\r\n                                    <text class=\"info-value time\">{{ item.createTime}}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">开始时间:</text>\r\n                                    <text class=\"info-value time\">{{item.beginVipTime}}</text>\r\n                                </view>\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">结束时间:</text>\r\n                                    <text class=\"info-value time\">{{item.expirationTime }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"card-bottom\">\r\n                                    <view class=\"price-info\">\r\n                                        <view class=\"price-item\" v-if=\"item.discountAmount > 0\">\r\n                                            <text class=\"price-label\">优惠:</text>\r\n                                            <text class=\"price-value discount\">-¥{{ item.discountAmount }}</text>\r\n                                        </view>\r\n                                        <view class=\"price-item\">\r\n                                            <text class=\"price-label\">实付:</text>\r\n                                            <text class=\"price-value actual\">¥{{ item.actualPayment }}</text>\r\n                                        </view>\r\n                                    </view>\r\n\r\n                                    <!-- 已支付订单 - 开发票按钮区域 -->\r\n                                    <template v-if=\"item.payStatus === 5\">\r\n                                        <view class=\"invoice-actions\">\r\n                                            <!-- 已开具发票，可发送邮箱 -->\r\n                                            <template\r\n                                                v-if=\"item.miniInvoiceRecord && item.miniInvoiceRecord.status === 'ISSUED'\">\r\n                                                <view class=\"invoice-btn send-btn\"\r\n                                                    @tap=\"chooseEmail(item.miniInvoiceRecord.id)\">\r\n                                                    <up-icon name=\"email\" color=\"#3b82f6\" size=\"14\"></up-icon>\r\n                                                    <text>发送邮箱</text>\r\n                                                </view>\r\n                                            </template>\r\n\r\n                                            <!-- 未开具发票，可开发票 -->\r\n                                            <template\r\n                                                v-if=\"!item.miniInvoiceRecord || (item.miniInvoiceRecord && (item.miniInvoiceRecord.status === 'UNISSU'\r\n                                                 || item.miniInvoiceRecord.status === 'CLOSED') && !item.miniInvoiceRecord.reopenSign)\">\r\n                                                <view class=\"invoice-btn open-btn\" @tap=\"routeToInvoice(item)\">\r\n                                                    <up-icon name=\"file-text\" color=\"#ffffff\" size=\"14\"></up-icon>\r\n                                                    <text>开发票</text>\r\n                                                </view>\r\n                                            </template>\r\n\r\n                                            <!-- 申请换开 -->\r\n                                            <template\r\n                                                v-if=\"item.miniInvoiceRecord && ((item.miniInvoiceRecord.status === 'ISSUED' && !item.miniInvoiceRecord.reopenSign) \r\n                                                || (item.miniInvoiceRecord.status === 'CLOSED' && item.miniInvoiceRecord.reopenSign) || item.miniInvoiceRecord.status === 'REVERSED')\">\r\n                                                <view class=\"invoice-btn reopen-btn\" @tap=\"routeToInvoice(item)\">\r\n                                                    <up-icon name=\"reload\" color=\"#ffffff\" size=\"14\"></up-icon>\r\n                                                    <text>申请换开</text>\r\n                                                </view>\r\n                                            </template>\r\n                                        </view>\r\n                                    </template>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </template>\r\n\r\n            <template v-else>\r\n                <view class=\"empty-state\">\r\n                    <view class=\"empty-icon\">📋</view>\r\n                    <text class=\"empty-text\">暂无购买记录</text>\r\n                </view>\r\n            </template>\r\n        </view>\r\n\r\n        <!-- 邮箱发送弹窗 -->\r\n        <up-popup :show=\"showPopup\" mode=\"center\" :round=\"10\" :safeAreaInsetBottom=\"false\" closeOnClickOverlay\r\n            @close=\"showPopup = false\">\r\n            <view class=\"popup-cell\">\r\n                <view class=\"email\">\r\n                    <view class=\"content_item\">\r\n                        <view class=\"email-title\">电子邮箱</view>\r\n                        <view class=\"email-input\">\r\n                            <up-input v-model=\"notifyEmail\" border=\"none\" placeholder=\"请输入您的电子邮箱\" clearable\r\n                                fontSize=\"28rpx\" color=\"#616161\" :placeholderStyle=\"placeholderStyle\"\r\n                                :customStyle=\"emailInputStyle\"></up-input>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"desc\">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>\r\n                </view>\r\n                <view class=\"choose_btn\">\r\n                    <view class=\"cancel_btn\" @tap=\"showPopup = false\">取消</view>\r\n                    <view class=\"sure_btn\" @tap=\"handlePostInvoiceSend\">确认</view>\r\n                </view>\r\n            </view>\r\n        </up-popup>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\nimport { onLoad, onShow } from '@dcloudio/uni-app';\r\nimport { getUserPackageRecordList } from '@/api/package';\r\nimport { getSpecialUser } from '@/api/specialUser';\r\nimport { postInvoiceSend } from '@/api/invoice';\r\n// TODO: 添加取消订单API\r\n// import { cancelPackageOrder } from '@/api/package';\r\n\r\nconst recordList = ref([]);\r\nconst vipType = ref(0);\r\nconst specialUser = ref({});\r\nconst activeStatusFilter = ref(0); // 订单状态筛选，0代表全部\r\nconst activeUserTypeFilter = ref(''); // 用户类型筛选\r\nconst showPopup = ref(false);\r\nconst invoiceId = ref(null);\r\nconst notifyEmail = ref('');\r\n\r\n// 订单状态筛选配置\r\nconst statusFilters = ref([\r\n    { value: 0, label: '全部' },\r\n    { value: 5, label: '已支付' },\r\n    { value: 3, label: '已失败' },\r\n    { value: 4, label: '已退款' }\r\n]);\r\n\r\n// 用户类型筛选配置\r\nconst userTypeFilters = ref([\r\n    { value: '普通用户', label: '普通套餐' }\r\n]);\r\n\r\n// 样式对象\r\nconst placeholderStyle = {\r\n    color: '#616161'\r\n}\r\n\r\nconst emailInputStyle = {\r\n    paddingLeft: '20rpx',\r\n    paddingTop: '26rpx',\r\n    paddingBottom: '26rpx',\r\n    borderBottom: '1rpx solid rgba(189,189,189,0.2)'\r\n}\r\n\r\n// 是否显示用户类型筛选\r\nconst showUserTypeFilter = computed(() => {\r\n    return specialUser.value && specialUser.value.userType && \r\n           (specialUser.value.userType === 'VIP客户' || specialUser.value.userType === '集团客户');\r\n});\r\n\r\nonLoad((options) => {\r\n    vipType.value = options.vipType || 0;\r\n});\r\n\r\nonShow(() => {\r\n    initUserData();\r\n});\r\n\r\n// 初始化用户数据\r\nconst initUserData = async () => {\r\n    try {\r\n        const res = await getSpecialUser();\r\n        specialUser.value = res.data || {};\r\n        console.log('specialUser:', specialUser.value);\r\n        \r\n        // 动态设置用户类型筛选选项\r\n        if (specialUser.value && specialUser.value.userType) {\r\n            const userTypeLabel = specialUser.value.userType === 'VIP客户' ? 'VIP套餐' : '集团套餐';\r\n            userTypeFilters.value = [\r\n                { value: '普通用户', label: '普通套餐' },\r\n                { value: specialUser.value.userType, label: userTypeLabel }\r\n            ];\r\n            \r\n            // 设置默认筛选为当前用户类型\r\n            activeUserTypeFilter.value = specialUser.value.userType;\r\n        } else {\r\n            // 普通用户只显示普通套餐\r\n            activeUserTypeFilter.value = '普通用户';\r\n        }\r\n        \r\n        // 获取记录列表\r\n        getRecordList();\r\n    } catch (error) {\r\n        console.error('获取用户信息失败:', error);\r\n        // 即使获取用户信息失败，也要获取记录列表\r\n        activeUserTypeFilter.value = '普通用户';\r\n        getRecordList();\r\n    }\r\n};\r\n\r\n// 获取套餐购买记录\r\nconst getRecordList = async () => {\r\n    try {\r\n        uni.showLoading({\r\n            title: '加载中...',\r\n            mask: true\r\n        });\r\n        \r\n        // 构建查询参数\r\n        const params = {};\r\n        \r\n        // 优先使用用户筛选的类型，否则使用页面参数传入的类型\r\n        if (activeUserTypeFilter.value) {\r\n            params.vipType = getUserTypeNumber(activeUserTypeFilter.value);\r\n        } else {\r\n            params.vipType = vipType.value;\r\n        }\r\n        \r\n        // 添加订单状态筛选\r\n        if (activeStatusFilter.value !== 0) {\r\n            params.payStatus = activeStatusFilter.value;\r\n        }\r\n        \r\n        console.log('查询参数:', params);\r\n        const res = await getUserPackageRecordList(params);\r\n        recordList.value = res.data || [];\r\n        \r\n    } catch (error) {\r\n        console.error('获取套餐购买记录失败:', error);\r\n        uni.showToast({\r\n            title: '获取记录失败',\r\n            icon: 'none'\r\n        });\r\n    } finally {\r\n        uni.hideLoading();\r\n    }\r\n};\r\n\r\n// 设置订单状态筛选\r\nconst setStatusFilter = async (payStatus) => {\r\n    if (activeStatusFilter.value === payStatus) return;\r\n    \r\n    activeStatusFilter.value = payStatus;\r\n    await getRecordList();\r\n};\r\n\r\n// 设置用户类型筛选\r\nconst setUserTypeFilter = async (userType) => {\r\n    if (activeUserTypeFilter.value === userType) return;\r\n    \r\n    activeUserTypeFilter.value = userType;\r\n    await getRecordList();\r\n};\r\n\r\n\r\n// 格式化日期范围\r\nconst formatDateRange = (beginTime, endTime) => {\r\n    return `${beginTime} 至 ${endTime}`;\r\n};\r\n\r\n// 用户类型映射配置\r\nconst userTypeMap = {\r\n    'VIP客户': 2,\r\n    '集团客户': 1,\r\n    '普通用户': 0\r\n};\r\n\r\n// 将用户类型字符串转换为数字\r\nconst getUserTypeNumber = (userType) => {\r\n    return userTypeMap[userType] ?? 0;\r\n};\r\n\r\n// 获取会员类型文本\r\nconst getVipTypeText = (vipType) => {\r\n    // 如果是字符串类型，直接返回\r\n    if (typeof vipType === 'string') {\r\n        return vipType;\r\n    }\r\n    \r\n    // 兼容数字类型\r\n    switch (vipType) {\r\n        case 0:\r\n            return '普通用户';\r\n        case 1:\r\n            return '集团客户';\r\n        case 2:\r\n            return 'VIP客户';\r\n        default:\r\n            return '普通用户';\r\n    }\r\n};\r\n\r\n// 状态映射配置\r\nconst statusMap = {\r\n    3: { text: '已失败', class: 'status-failed' },\r\n    4: { text: '已退款', class: 'status-failed' },\r\n    5: { text: '已支付', class: 'status-paid' }\r\n};\r\n\r\n// 获取支付状态对应的文本\r\nconst getStatusText = (status) => {\r\n    return statusMap[status]?.text || '未知状态';\r\n};\r\n\r\n// 获取支付状态对应的样式类\r\nconst getStatusClass = (status) => {\r\n    return statusMap[status]?.class || 'status-unknown';\r\n};\r\n\r\n// 跳转到开发票页面\r\nconst routeToInvoice = (item) => {\r\n    // functionType 功能类型：1停车，2会员\r\n    if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {\r\n        // 发票重开\r\n        if (item.miniInvoiceRecord.isResume) {\r\n            uni.navigateTo({\r\n                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`\r\n            });\r\n        } else {\r\n            uni.navigateTo({\r\n                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}`\r\n            });\r\n        }\r\n    } else {\r\n        uni.navigateTo({\r\n            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2`\r\n        });\r\n    }\r\n};\r\n\r\n// 选择邮箱发送\r\nconst chooseEmail = (id) => {\r\n    invoiceId.value = id;\r\n    showPopup.value = true;\r\n};\r\n\r\n// 发送发票到邮箱\r\nconst handlePostInvoiceSend = async () => {\r\n    if (!notifyEmail.value) {\r\n        uni.showToast({\r\n            title: '请输入邮箱',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n    \r\n    try {\r\n        const params = {\r\n            id: invoiceId.value,\r\n            notifyEmail: notifyEmail.value\r\n        };\r\n        \r\n        await postInvoiceSend(params);\r\n        \r\n        uni.showToast({\r\n            title: '邮箱发送成功～',\r\n            icon: 'none',\r\n            duration: 2000\r\n        });\r\n        \r\n        setTimeout(() => {\r\n            showPopup.value = false;\r\n            notifyEmail.value = '';\r\n        }, 1000);\r\n    } catch (error) {\r\n        console.error('发送邮箱失败:', error);\r\n        uni.showToast({\r\n            title: '发送失败，请重试',\r\n            icon: 'none'\r\n        });\r\n    }\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.package-record {\r\n    background: #f5f5f5;\r\n    min-height: 100vh;\r\n}\r\n\r\n// 用户类型筛选器\r\n.user-type-filter {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    background: #ffffff;\r\n    z-index: 10;\r\n    display: flex;\r\n    padding: 16rpx 24rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);\r\n    \r\n    .filter-tab {\r\n        flex: 1;\r\n        text-align: center;\r\n        padding: 14rpx 12rpx;\r\n        font-size: 26rpx;\r\n        color: #666666;\r\n        border-radius: 12rpx;\r\n        margin: 0 6rpx;\r\n        transition: all 0.3s ease;\r\n        \r\n        &.active {\r\n            background: #3b82f6;\r\n            color: #ffffff;\r\n            font-weight: 500;\r\n        }\r\n        \r\n        &:not(.active):active {\r\n            background: #f8fafc;\r\n        }\r\n        \r\n        &:first-child {\r\n            margin-left: 0;\r\n        }\r\n        \r\n        &:last-child {\r\n            margin-right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n// 订单状态筛选器\r\n.status-filter {\r\n    position: fixed;\r\n    left: 0;\r\n    width: 100%;\r\n    background: #ffffff;\r\n    z-index: 9;\r\n    display: flex;\r\n    padding: 16rpx 24rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);\r\n    \r\n    .filter-tab {\r\n        flex: 1;\r\n        text-align: center;\r\n        padding: 14rpx 12rpx;\r\n        font-size: 26rpx;\r\n        color: #666666;\r\n        border-radius: 12rpx;\r\n        margin: 0 6rpx;\r\n        transition: all 0.3s ease;\r\n        \r\n        &.active {\r\n            background: #3b82f6;\r\n            color: #ffffff;\r\n            font-weight: 500;\r\n        }\r\n        \r\n        &:not(.active):active {\r\n            background: #f8fafc;\r\n        }\r\n        \r\n        &:first-child {\r\n            margin-left: 0;\r\n        }\r\n        \r\n        &:last-child {\r\n            margin-right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.content {\r\n    padding: 20rpx 32rpx 40rpx;\r\n}\r\n\r\n.record-list {\r\n    .record-item {\r\n        margin-bottom: 20rpx;\r\n        \r\n        .record-card {\r\n            background: white;\r\n            border-radius: 20rpx;\r\n            padding: 32rpx;\r\n            box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);\r\n            \r\n            .card-header {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: flex-start;\r\n                margin-bottom: 16rpx;\r\n                \r\n                .order-info {\r\n                    .order-no {\r\n                        font-size: 20rpx;\r\n                        font-weight: bold;\r\n                        color: #797979;\r\n                    }\r\n                }\r\n                \r\n                .header-right {\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    align-items: flex-end;\r\n                    \r\n                    .status-row {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        gap: 12rpx;\r\n                    }\r\n                    \r\n                    .status-badge {\r\n                        padding: 6rpx 12rpx;\r\n                        border-radius: 16rpx;\r\n                        font-size: 22rpx;\r\n                        font-weight: 500;\r\n                        \r\n                        &.status-failed {\r\n                            background: #fef2f2;\r\n                            color: #dc2626;\r\n                            border: 1rpx solid #fecaca;\r\n                        }\r\n                        \r\n                        &.status-paid {\r\n                            background: #f0f9ff;\r\n                            color: #0ea5e9;\r\n                            border: 1rpx solid #bae6fd;\r\n                        }\r\n                        \r\n                        &.status-unknown {\r\n                            background: #f9fafb;\r\n                            color: #6b7280;\r\n                            border: 1rpx solid #e5e7eb;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            .card-body {\r\n                .info-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    margin-bottom: 16rpx;\r\n                    \r\n                    .info-label {\r\n                        font-size: 26rpx;\r\n                        color: #666666;\r\n                        margin-right: 16rpx;\r\n                        min-width: 120rpx;\r\n                        \r\n                        &.plate-label {\r\n                            margin-left: 40rpx;\r\n                            min-width: 80rpx;\r\n                        }\r\n                    }\r\n                    \r\n                    .info-value {\r\n                        font-size: 26rpx;\r\n                        color: #333333;\r\n                        flex: 1;\r\n                        \r\n                        &.time {\r\n                            color: #333333;\r\n                        }\r\n                    }\r\n                }\r\n                \r\n                .card-bottom {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    margin-top: 20rpx;\r\n                    padding-top: 20rpx;\r\n                    border-top: 1rpx solid #f5f5f5;\r\n                    \r\n                    .price-info {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        \r\n                        .price-item {\r\n                            display: flex;\r\n                            align-items: center;\r\n                            margin-right: 20rpx;\r\n                            \r\n                            .price-label {\r\n                                font-size: 22rpx;\r\n                                color: #666666;\r\n                                margin-right: 8rpx;\r\n                            }\r\n                            \r\n                            .price-value {\r\n                                font-size: 26rpx;\r\n                                font-weight: bold;\r\n                                \r\n                                &.discount {\r\n                                    color: #16a34a;\r\n                                }\r\n                                \r\n                                &.actual {\r\n                                    color: #ff0000;\r\n                                    font-size: 30rpx;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    .invoice-actions {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        \r\n                        .invoice-btn {\r\n                            display: flex;\r\n                            align-items: center;\r\n                            padding: 12rpx 16rpx;\r\n                            border-radius: 8rpx;\r\n                            font-size: 24rpx;\r\n                            font-weight: 500;\r\n                            margin-left: 10rpx;\r\n                            \r\n                            text {\r\n                                margin-left: 6rpx;\r\n                            }\r\n                            \r\n                            &.send-btn {\r\n                                background: #f0f9ff;\r\n                                color: #3b82f6;\r\n                                border: 1rpx solid #bae6fd;\r\n                            }\r\n                            \r\n                            &.open-btn {\r\n                                background: #3b82f6;\r\n                                color: #ffffff;\r\n                            }\r\n                            \r\n                            &.reopen-btn {\r\n                                background: #f59e0b;\r\n                                color: #ffffff;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.bottom-tip {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40rpx;\r\n    padding: 20rpx 0;\r\n    \r\n    .divider-line {\r\n        width: 100rpx;\r\n        height: 1rpx;\r\n        background: #e0e0e0;\r\n    }\r\n    \r\n    .tip-text {\r\n        font-size: 22rpx;\r\n        color: #666666;\r\n        margin: 0 20rpx;\r\n    }\r\n}\r\n\r\n.empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 120rpx 0;\r\n    \r\n    .empty-icon {\r\n        font-size: 120rpx;\r\n        margin-bottom: 20rpx;\r\n        opacity: 0.3;\r\n    }\r\n    \r\n    .empty-text {\r\n        font-size: 26rpx;\r\n        color: #666666;\r\n    }\r\n}\r\n\r\n.popup-cell {\r\n    width: 600rpx;\r\n    text-align: center;\r\n    padding: 44rpx 0;\r\n    \r\n    .email {\r\n        padding: 32rpx;\r\n        background: #ffffff;\r\n        border-radius: 20rpx;\r\n        \r\n        .content_item {\r\n            margin-bottom: 20rpx;\r\n            \r\n            .email-title {\r\n                font-size: 28rpx;\r\n                color: #212121;\r\n                font-weight: 500;\r\n                margin-bottom: 16rpx;\r\n                text-align: left;\r\n            }\r\n        }\r\n        \r\n        .desc {\r\n            font-size: 24rpx;\r\n            color: #f5820e;\r\n            line-height: 1.5;\r\n            margin-top: 16rpx;\r\n        }\r\n    }\r\n    \r\n    .choose_btn {\r\n        display: flex;\r\n        justify-content: space-around;\r\n        padding: 0 20rpx;\r\n        margin-top: 32rpx;\r\n        \r\n        .cancel_btn, .sure_btn {\r\n            width: 240rpx;\r\n            height: 80rpx;\r\n            line-height: 80rpx;\r\n            border-radius: 80rpx;\r\n            text-align: center;\r\n            font-size: 28rpx;\r\n            font-weight: 600;\r\n        }\r\n        \r\n        .cancel_btn {\r\n            background: #ffffff;\r\n            border: 2rpx solid #246bfd;\r\n            color: #246bfd;\r\n        }\r\n        \r\n        .sure_btn {\r\n            background: #246bfd;\r\n            color: #ffffff;\r\n        }\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/package/packageRecord.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "onShow", "getSpecialUser", "uni", "getUserPackageRecordList", "postInvoiceSend"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyKA,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,UAAUA,cAAAA,IAAI,CAAC;AACrB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAChC,UAAM,uBAAuBA,cAAAA,IAAI,EAAE;AACnC,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAG1B,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACtB,EAAE,OAAO,GAAG,OAAO,KAAM;AAAA,MACzB,EAAE,OAAO,GAAG,OAAO,MAAO;AAAA,MAC1B,EAAE,OAAO,GAAG,OAAO,MAAO;AAAA,MAC1B,EAAE,OAAO,GAAG,OAAO,MAAO;AAAA,IAC9B,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MACxB,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,IACpC,CAAC;AAGD,UAAM,mBAAmB;AAAA,MACrB,OAAO;AAAA,IACX;AAEA,UAAM,kBAAkB;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,IAClB;AAGA,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACtC,aAAO,YAAY,SAAS,YAAY,MAAM,aACtC,YAAY,MAAM,aAAa,WAAW,YAAY,MAAM,aAAa;AAAA,IACrF,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAChB,cAAQ,QAAQ,QAAQ,WAAW;AAAA,IACvC,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AACT;IACJ,CAAC;AAGD,UAAM,eAAe,YAAY;AAC7B,UAAI;AACA,cAAM,MAAM,MAAMC,gBAAAA;AAClB,oBAAY,QAAQ,IAAI,QAAQ,CAAA;AAChCC,sBAAY,MAAA,MAAA,OAAA,0CAAA,gBAAgB,YAAY,KAAK;AAG7C,YAAI,YAAY,SAAS,YAAY,MAAM,UAAU;AACjD,gBAAM,gBAAgB,YAAY,MAAM,aAAa,UAAU,UAAU;AACzE,0BAAgB,QAAQ;AAAA,YACpB,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,YAChC,EAAE,OAAO,YAAY,MAAM,UAAU,OAAO,cAAe;AAAA,UAC3E;AAGY,+BAAqB,QAAQ,YAAY,MAAM;AAAA,QAC3D,OAAe;AAEH,+BAAqB,QAAQ;AAAA,QAChC;AAGD;MACH,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,KAAK;AAEhC,6BAAqB,QAAQ;AAC7B;MACH;AAAA,IACL;AAGA,UAAM,gBAAgB,YAAY;AAC9B,UAAI;AACAA,sBAAAA,MAAI,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAGD,cAAM,SAAS,CAAA;AAGf,YAAI,qBAAqB,OAAO;AAC5B,iBAAO,UAAU,kBAAkB,qBAAqB,KAAK;AAAA,QACzE,OAAe;AACH,iBAAO,UAAU,QAAQ;AAAA,QAC5B;AAGD,YAAI,mBAAmB,UAAU,GAAG;AAChC,iBAAO,YAAY,mBAAmB;AAAA,QACzC;AAEDA,sBAAA,MAAA,MAAA,OAAA,0CAAY,SAAS,MAAM;AAC3B,cAAM,MAAM,MAAMC,qCAAyB,MAAM;AACjD,mBAAW,QAAQ,IAAI,QAAQ,CAAA;AAAA,MAElC,SAAQ,OAAO;AACZD,sBAAA,MAAA,MAAA,SAAA,0CAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACT,UAAc;AACNA,sBAAG,MAAC,YAAW;AAAA,MAClB;AAAA,IACL;AAGA,UAAM,kBAAkB,OAAO,cAAc;AACzC,UAAI,mBAAmB,UAAU;AAAW;AAE5C,yBAAmB,QAAQ;AAC3B,YAAM,cAAa;AAAA,IACvB;AAGA,UAAM,oBAAoB,OAAO,aAAa;AAC1C,UAAI,qBAAqB,UAAU;AAAU;AAE7C,2BAAqB,QAAQ;AAC7B,YAAM,cAAa;AAAA,IACvB;AASA,UAAM,cAAc;AAAA,MAChB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ;AAGA,UAAM,oBAAoB,CAAC,aAAa;AACpC,aAAO,YAAY,QAAQ,KAAK;AAAA,IACpC;AAuBA,UAAM,YAAY;AAAA,MACd,GAAG,EAAE,MAAM,OAAO,OAAO,gBAAiB;AAAA,MAC1C,GAAG,EAAE,MAAM,OAAO,OAAO,gBAAiB;AAAA,MAC1C,GAAG,EAAE,MAAM,OAAO,OAAO,cAAe;AAAA,IAC5C;AAGA,UAAM,gBAAgB,CAAC,WAAW;;AAC9B,eAAO,eAAU,MAAM,MAAhB,mBAAmB,SAAQ;AAAA,IACtC;AAGA,UAAM,iBAAiB,CAAC,WAAW;;AAC/B,eAAO,eAAU,MAAM,MAAhB,mBAAmB,UAAS;AAAA,IACvC;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAE7B,UAAI,KAAK,qBAAqB,KAAK,kBAAkB,IAAI;AAErD,YAAI,KAAK,kBAAkB,UAAU;AACjCA,wBAAAA,MAAI,WAAW;AAAA,YACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa,6BAA6B,KAAK,kBAAkB,EAAE,aAAa,KAAK,kBAAkB,QAAQ;AAAA,UACnM,CAAa;AAAA,QACb,OAAe;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa,6BAA6B,KAAK,kBAAkB,EAAE;AAAA,UACvJ,CAAa;AAAA,QACJ;AAAA,MACT,OAAW;AACHA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa;AAAA,QAC7F,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,cAAc,CAAC,OAAO;AACxB,gBAAU,QAAQ;AAClB,gBAAU,QAAQ;AAAA,IACtB;AAGA,UAAM,wBAAwB,YAAY;AACtC,UAAI,CAAC,YAAY,OAAO;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI;AACA,cAAM,SAAS;AAAA,UACX,IAAI,UAAU;AAAA,UACd,aAAa,YAAY;AAAA,QACrC;AAEQ,cAAME,YAAAA,gBAAgB,MAAM;AAE5BF,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAED,mBAAW,MAAM;AACb,oBAAU,QAAQ;AAClB,sBAAY,QAAQ;AAAA,QACvB,GAAE,GAAI;AAAA,MACV,SAAQ,OAAO;AACZA,sBAAc,MAAA,MAAA,SAAA,0CAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnaA,GAAG,WAAW,eAAe;"}