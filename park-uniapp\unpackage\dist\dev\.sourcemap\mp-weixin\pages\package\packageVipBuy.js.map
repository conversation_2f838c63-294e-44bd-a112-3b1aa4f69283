{"version": 3, "file": "packageVipBuy.js", "sources": ["pages/package/packageVipBuy.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFja2FnZS9wYWNrYWdlVmlwQnV5LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"vip-buy-container\">\r\n        <!-- 提示信息 -->\r\n        <view class=\"notice-card\">\r\n            <view class=\"notice-content\">\r\n                <text class=\"notice-text\">\r\n                    {{ noticeText }}\r\n                </text>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 表单区域 -->\r\n        <view class=\"form-container\">\r\n            <!-- 用户手机号 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/shifu.png\" mode=\"aspectFit\"></image>用户手机号\r\n                </view>\r\n                <view class=\"word\">{{ userPhone || '未获取到手机号' }}</view>\r\n            </view>\r\n\r\n            <!-- 场库选择 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/changku.png\" mode=\"aspectFit\"></image>场库名称\r\n                </view>\r\n                <view class=\"word\"\r\n                    :class=\"{ 'clickable-text': canSelectWarehouse, 'red': packageOrder.warehouseName && packageOrder.warehouseName !== '--' }\"\r\n                    @tap=\"handleWarehouseClick\">\r\n                    {{ packageOrder.warehouseName || '请选择场库' }}\r\n                    <u-icon v-if=\"canSelectWarehouse\" name=\"arrow-down\" size=\"12\" color=\"#4BA1FC\"\r\n                        style=\"margin-left: 8rpx;\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 车牌号 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/chepai.png\" mode=\"aspectFit\"></image>车牌牌号\r\n                </view>\r\n                <view class=\"word\" :class=\"[canSelectPlate ? 'clickable-text' : '']\" @tap=\"handlePlateClick\">\r\n                    {{ packageOrder.plateNo || '请输入车牌号' }}\r\n                    <u-icon v-if=\"canSelectPlate\" name=\"edit-pen\" size=\"12\" color=\"#4BA1FC\"\r\n                        style=\"margin-left: 8rpx;\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 套餐类型 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/taocantype.png\" mode=\"aspectFit\"></image>套餐类型\r\n                </view>\r\n                <view class=\"word\" v-if=\"packageOrder.vipType === 2 && packageOrder.isRenewal\">{{ packageOrder.packageName || '-' }}</view>\r\n                <view class=\"word\" v-else :class=\"[canSelectPackage ? 'clickable-text' : '']\" @tap=\"handlePackageClick\">\r\n                    {{ packageOrder.packageName || '请选择套餐类型' }}\r\n                    <u-icon v-if=\"canSelectPackage\" name=\"arrow-down\" size=\"12\" color=\"#4BA1FC\" style=\"margin-left: 8rpx;\"></u-icon>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 开始时间 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>开始时间\r\n                </view>\r\n                <view class=\"word\" :class=\"[canSelectTime ? 'clickable-text' : '']\">\r\n                    <picker v-if=\"canSelectTime\" mode=\"date\" :value=\"selectedDate\" :start=\"minDate\" :end=\"maxDate\"\r\n                        @change=\"onDateChange\">\r\n                        <view class=\"picker-display\">\r\n                            {{ packageOrder.beginVipTime || '请选择开始时间' }}\r\n                        </view>\r\n                    </picker>\r\n                    <view v-else class=\"picker-display\">\r\n                        {{ packageOrder.beginVipTime || '请选择开始时间' }}\r\n                    </view>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 当前到期时间（仅续费时显示） -->\r\n            <view v-if=\"packageOrder.isRenewal\" class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>当前到期时间\r\n                </view>\r\n                <view class=\"word\">{{ packageOrder.expirationTime }}</view>\r\n            </view>\r\n\r\n            <!-- 到期时间 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>{{ packageOrder.isRenewal ?\r\n                    '续费后到期时间' : '到期时间' }}\r\n                </view>\r\n                <view class=\"word\">\r\n                    {{ packageOrder.isRenewal ? (packageOrder.newExpirationTime || '--') : (packageOrder.expirationTime || '--') }}\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 套餐价格 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/taocanprice.png\" mode=\"aspectFit\"></image>套餐价格\r\n                </view>\r\n                <view class=\"word\">\r\n                    <text class=\"tips\">¥</text> \r\n                    {{ packageOrder.packagePrice !== null && packageOrder.packagePrice !== undefined ? packageOrder.packagePrice : '0.00' }}\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 实付金额 -->\r\n            <view class=\"content_item\">\r\n                <view class=\"title\">\r\n                    <image src=\"/static/package/shifu.png\" mode=\"aspectFit\"></image>实付金额\r\n                </view>\r\n                <view class=\"word money\">\r\n                    <text class=\"tips red\">¥</text> {{ packageOrder.packagePrice !== null && packageOrder.packagePrice !== undefined ? packageOrder.packagePrice : '0.00' }}\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 支付按钮 -->\r\n        <view class=\"button-section\">\r\n            <button class=\"pay-button\" @tap=\"submitOrder\" :disabled=\"!canSubmit\">\r\n                {{ packageOrder.isRenewal ? '确认续费' : '立即购买' }}\r\n            </button>\r\n        </view>\r\n\r\n        <!-- 车牌输入键盘 -->\r\n        <plate-input @typeChange=\"typeChange\" v-if=\"plateShow\" :plate=\"packageOrder.plateNo || ''\" @export=\"setPlate\"\r\n            @close=\"plateShow = false\" />\r\n\r\n        <!-- 场库选择器 -->\r\n        <WarehouseSelector :show=\"showSelector\" :warehouseList=\"warehouseList\" :currentWarehouse=\"currentWarehouse\"\r\n            :windowHeightHalf=\"400\" @close=\"closeWarehouseSelector\" @select=\"selectWarehouse\" />\r\n        \r\n        <!-- 套餐选择器 -->\r\n        <up-popup :show=\"showPackageSelector\" mode=\"bottom\" round=\"20\" :safeAreaInsetBottom=\"true\" @close=\"showPackageSelector = false\">\r\n            <view class=\"package-popup\">\r\n                <view class=\"popup-header\">\r\n                    <text class=\"popup-title\">选择套餐类型</text>\r\n                    <up-icon name=\"close\" size=\"18\" color=\"#999\" @click=\"showPackageSelector = false\"></up-icon>\r\n                </view>\r\n                <scroll-view class=\"package-list\" scroll-y style=\"max-height: 400px;\">\r\n                    <view v-for=\"(option, index) in packageOptions\" :key=\"index\" \r\n                          class=\"package-item\"\r\n                          :class=\"{ active: selectedPackage && selectedPackage.name === option.name }\" \r\n                          @click=\"selectPackageOption(option)\">\r\n                        <view class=\"package-item-left\">\r\n                            <text class=\"package-item-name\">{{ option.name }}</text>\r\n                            <text class=\"package-item-days\">{{ option.days }}天</text>\r\n                        </view>\r\n                        <view class=\"package-item-right\">\r\n                            <text class=\"package-item-price\">¥{{ option.price }}</text>\r\n                            <!-- <up-icon v-if=\"selectedPackage && selectedPackage.name === option.name\" name=\"checkmark\" size=\"16\" color=\"#40a9ff\"></up-icon> -->\r\n                        </view>\r\n                    </view>\r\n                </scroll-view>\r\n            </view>\r\n        </up-popup>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport plateInput from '@/components/uni-plate-input/uni-plate-input.vue'\r\nimport WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue'\r\nimport { getParkWareHouseList } from '@/api/warehouse'\r\nimport { checkCarInWarehouse, createOrder, updateOrder } from '@/api/package'\r\nimport { getOpenid } from '@/api/login'\r\nimport { formatDate, extractDatePart, isSameDate } from '@/utils/utils'\r\n\r\n// 响应式数据\r\nconst userPhone = ref('')\r\nconst plateShow = ref(false)\r\nconst warehouseList = ref([])\r\nconst showSelector = ref(false)\r\nconst currentWarehouse = ref({ id: 0, name: '' })\r\n\r\n// 核心数据\r\nconst packageOrder = ref({\r\n    plateNo: '',\r\n    warehouseId: null,\r\n    warehouseName: '',\r\n    packageId: null,\r\n    packageName: '',\r\n    packagePrice: 0,\r\n    packageDays: null,\r\n    beginVipTime: '',\r\n    expirationTime: '',\r\n    vipType: 0,\r\n    isRenewal: false,\r\n    index: 1\r\n})\r\nconst packageJudge = ref({})\r\n\r\n// 状态控制\r\nconst isCarInWarehouse = ref(false)\r\nconst canSelectTime = ref(false)\r\nconst canSelectWarehouse = ref(true)\r\nconst canSelectPlate = ref(true)\r\nconst canSelectPackage = ref(false)\r\n\r\n// 时间选择相关\r\nconst selectedDate = ref('')\r\nconst minDate = ref('')\r\nconst maxDate = ref('')\r\n\r\n// 套餐选择相关\r\nconst showPackageSelector = ref(false)\r\nconst packageOptions = ref([])\r\nconst selectedPackage = ref(null)\r\n\r\n// 计算属性\r\nconst canSubmit = computed(() => {\r\n    const basicRequirements = packageOrder.value.plateNo && \r\n                             packageOrder.value.warehouseId && \r\n                             packageOrder.value.beginVipTime\r\n    \r\n    // VIP用户需要有套餐价格\r\n    if (packageOrder.value.vipType === 2) {\r\n        return basicRequirements && packageOrder.value.packagePrice !== null\r\n    }\r\n    \r\n    // 集团客户需要选择套餐类型\r\n    if (packageOrder.value.vipType === 1) {\r\n        const packageRequirements = packageOrder.value.packageName && \r\n                                   packageOrder.value.packageDays !== null &&\r\n                                   packageOrder.value.packagePrice !== null\r\n        \r\n        // 续费情况下还需要有新的结束时间\r\n        if (packageOrder.value.isRenewal) {\r\n            return basicRequirements && packageRequirements && packageOrder.value.newExpirationTime !== null\r\n        }\r\n        \r\n        return basicRequirements && packageRequirements\r\n    }\r\n    \r\n    return basicRequirements\r\n})\r\n\r\n// 提示文本\r\nconst noticeText = computed(() => {\r\n    const userType = packageOrder.value.vipType === 1 ? '集团客户' : 'VIP用户'\r\n    const packageInfo = packageOrder.value.packageName ? \r\n        `${packageOrder.value.packageName}(${packageOrder.value.packageDays || ''}个自然日)` : \r\n        '套餐类型未选择'\r\n    \r\n    if (packageOrder.value.isRenewal) {\r\n        if (packageOrder.value.vipType === 1) {\r\n            // 集团客户续费，可以选择套餐类型\r\n            return `当前的续费套餐是：${packageInfo}。\r\n            续费将在原有套餐到期日期基础上延长，无法修改时间。`;\r\n        } else {\r\n            // VIP用户续费，不可选择套餐类型\r\n            return `当前的续费套餐是：${packageInfo}。\r\n            续费将在原有套餐到期日期基础上延长，无法修改时间。`;\r\n        }\r\n    }\r\n    \r\n    if (isCarInWarehouse.value) {\r\n        const hasExpiredMember = packageJudge.value.endVipTime !== null;\r\n        const hasParkingPayment = packageJudge.value.endParkingTime !== null;\r\n        \r\n        if (hasExpiredMember || hasParkingPayment) {\r\n            return `当前${userType}套餐是：${packageInfo}。\r\n            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;\r\n        } else {\r\n            return `当前${userType}套餐是：${packageInfo}。\r\n            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;\r\n        }\r\n    }\r\n    \r\n    return `当前${userType}开通套餐是：${packageInfo}。\r\n    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;\r\n})\r\n\r\n// 页面加载\r\nonLoad((options) => {\r\n    loadUserInfo()\r\n    loadWarehouseList()\r\n    console.log('接收到的参数:', options)\r\n    \r\n    if (options.packageOrder) {\r\n        try {\r\n            const parsedOrder = JSON.parse(decodeURIComponent(options.packageOrder))\r\n            // 合并传入的数据和默认值，确保所有属性都有值\r\n            packageOrder.value = Object.assign({}, packageOrder.value, parsedOrder)\r\n            packageOrder.value.phoneNumber = userPhone.value\r\n            \r\n            // 如果是集团客户续费且没有选择套餐，初始化newExpirationTime为null\r\n            if (packageOrder.value.isRenewal && packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {\r\n                packageOrder.value.newExpirationTime = null\r\n            }\r\n            \r\n            console.log('解析的套餐订单:', packageOrder.value)\r\n            \r\n            // 初始化控制状态\r\n            initControlStates()\r\n            \r\n            // 如果是续费，直接处理续费逻辑\r\n            if (packageOrder.value.isRenewal) {\r\n                handleRenewalOrder()\r\n            } else {\r\n                // 非续费情况，检查是否需要调用packageJudge\r\n                checkPackageJudge()\r\n            }\r\n        } catch (error) {\r\n            console.error('解析套餐订单失败:', error)\r\n        }\r\n    }\r\n})\r\n\r\n\r\n// 初始化控制状态\r\nconst initControlStates = () => {\r\n    // 续费时：车辆，场库，时间都不可选择\r\n    if (packageOrder.value.isRenewal) {\r\n        canSelectWarehouse.value = false\r\n        canSelectPlate.value = false\r\n        canSelectTime.value = false\r\n        // 集团客户续费时，套餐类型可以选择\r\n        canSelectPackage.value = packageOrder.value.vipType === 1\r\n        if (canSelectPackage.value) {\r\n            generatePackageOptions()\r\n        }\r\n        return\r\n    }\r\n    \r\n    // 非续费时：根据传入参数决定\r\n    // 有车牌号：不允许选择车牌，只允许选择场库，时间\r\n    if (packageOrder.value.plateNo && packageOrder.value.plateNo !== '--') {\r\n        canSelectPlate.value = false\r\n        canSelectWarehouse.value = true\r\n    } else {\r\n        // 没有车牌号：允许选择车牌，场库，时间\r\n        canSelectPlate.value = true\r\n        canSelectWarehouse.value = true\r\n    }\r\n    \r\n    // 集团客户需要选择套餐类型\r\n    if (packageOrder.value.vipType === 1) {\r\n        canSelectPackage.value = true\r\n        generatePackageOptions()\r\n    }\r\n}\r\n\r\n// 生成套餐选项\r\nconst generatePackageOptions = () => {\r\n    const index = packageOrder.value.index || 1\r\n    const options = []\r\n    \r\n    // if (index === 1) {\r\n    //     // 第一辆车：只能选择年度套餐，365天，0元\r\n    //     options.push({\r\n    //         name: '年度套餐',\r\n    //         days: 365,\r\n    //         price: 0\r\n    //     })\r\n    // } else if (index === 2) {\r\n    //     // 第二辆车：年度套餐（365天，600元）或半年度套餐（183天，300元）\r\n    //     options.push({\r\n    //         name: '年度套餐',\r\n    //         days: 365,\r\n    //         price: 600\r\n    //     })\r\n    //     options.push({\r\n    //         name: '半年度套餐',\r\n    //         days: 183,\r\n    //         price: 300\r\n    //     })\r\n    // } else if (index === 3) {\r\n    //     // 第三辆车：年度套餐（365天，1800元）或半年度套餐（183天，900元）\r\n    //     options.push({\r\n    //         name: '年度套餐',\r\n    //         days: 365,\r\n    //         price: 1800\r\n    //     })\r\n    //     options.push({\r\n    //         name: '半年度套餐',\r\n    //         days: 183,\r\n    //         price: 900\r\n    //     })\r\n    // }\r\n    if (index === 1) {\r\n        // 第一辆车：只能选择年度套餐，365天，0元\r\n        options.push({\r\n            id: 1,\r\n            name: '年度套餐',\r\n            days: 1,\r\n            price: 0\r\n        })\r\n    } else if (index === 2) {\r\n        // 第二辆车：年度套餐（365天，600元）或半年度套餐（183天，300元）\r\n        options.push({\r\n            id: 4,\r\n            name: '年度套餐',\r\n            days: 2,\r\n            price: 0.01\r\n        })\r\n        options.push({\r\n            id: 2,\r\n            name: '半年度套餐',\r\n            days: 1,\r\n            price: 0.01\r\n        })\r\n    } else if (index === 3) {\r\n        // 第三辆车：年度套餐（365天，1800元）或半年度套餐（183天，900元）\r\n        options.push({\r\n            id: 4,  \r\n            name: '年度套餐',\r\n            days: 2,\r\n            price: 0.01\r\n        })\r\n        options.push({\r\n            id: 2,\r\n            name: '半年度套餐',\r\n            days: 1,\r\n            price: 0.01\r\n        })\r\n    }\r\n    \r\n    packageOptions.value = options\r\n}\r\n\r\n// 检查是否需要调用packageJudge\r\nconst checkPackageJudge = () => {\r\n    // 当车牌和场库都存在时，调用packageJudge\r\n    if (packageOrder.value.plateNo && packageOrder.value.warehouseId) {\r\n        performPackageJudge()\r\n    } else {\r\n        // 车辆不在场，允许选择开始时间\r\n        handleCarNotInWarehouse()\r\n    }\r\n}\r\n\r\n// 执行packageJudge检查\r\nconst performPackageJudge = async () => {\r\n    try {\r\n        const res = await checkCarInWarehouse({\r\n            plateNo: packageOrder.value.plateNo,\r\n            warehouseId: packageOrder.value.warehouseId\r\n        })\r\n        \r\n        packageJudge.value = res.data\r\n        isCarInWarehouse.value = res.data && res.data.isCarInWarehouse\r\n        \r\n        console.log('packageJudge结果:', res.data)\r\n        console.log('车辆是否在场:', isCarInWarehouse.value)\r\n        \r\n        if (isCarInWarehouse.value) {\r\n            handleCarInWarehouse()\r\n        } else {\r\n            handleCarNotInWarehouse()\r\n        }\r\n    } catch (error) {\r\n        console.error('检查车辆失败:', error)\r\n        isCarInWarehouse.value = false\r\n        handleCarNotInWarehouse()\r\n    }\r\n}\r\n\r\n// 处理续费订单\r\nconst handleRenewalOrder = () => {\r\n    if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {\r\n        uni.showToast({\r\n            title: '续费订单数据异常，请重新操作',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        return\r\n    }\r\n    \r\n    // 如果是集团客户续费，需要选择套餐类型后才能计算结束时间\r\n    if (packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {\r\n        // 集团客户续费，暂时不计算结束时间，等选择套餐后再计算\r\n        packageOrder.value.newExpirationTime = null\r\n        console.log('集团客户续费，等待选择套餐类型')\r\n        return\r\n    }\r\n    \r\n    // 续费：在原结束时间基础上延长\r\n    const originalEndDate = new Date(packageOrder.value.expirationTime)\r\n    const newEndDate = new Date(originalEndDate)\r\n    newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays)\r\n    \r\n    const endDateStr = formatDate(newEndDate)\r\n    packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`\r\n    \r\n    console.log('续费订单处理完成，新结束时间:', packageOrder.value.newExpirationTime)\r\n}\r\n\r\n// 处理车辆在场的开通会员\r\nconst handleCarInWarehouse = () => {\r\n    // 检查是否有会员过期记录\r\n    const hasExpiredMember = packageJudge.value.endVipTime !== null;\r\n    \r\n    if (hasExpiredMember && packageJudge.value.finalBeginTime) {\r\n        // 如果有会员过期记录，需要判断finalBeginTime是来自会员过期还是临停缴费\r\n        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);\r\n        let startDate = new Date(finalBeginDate);\r\n        \r\n        // 判断finalBeginTime是否来自临停缴费（临停缴费时间更晚）\r\n        const hasParkingPayment = packageJudge.value.endParkingTime !== null;\r\n        const isFinalBeginFromParking = hasParkingPayment && \r\n            packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;\r\n        \r\n        if (isFinalBeginFromParking) {\r\n            // 如果finalBeginTime来自临停缴费时间，使用finalBeginTime的0点（不加一天）\r\n            startDate.setHours(0, 0, 0, 0);\r\n        } else {\r\n            // 如果finalBeginTime来自会员过期时间，使用finalBeginTime+1天的0点\r\n            startDate.setDate(finalBeginDate.getDate() + 1);\r\n            startDate.setHours(0, 0, 0, 0);\r\n        }\r\n        \r\n        const startDateStr = formatDate(startDate);\r\n        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;\r\n        \r\n        // 计算结束时间：需要判断开始时间是否是今天\r\n        if (packageOrder.value.packageDays) {\r\n            const today = new Date();\r\n            const endDate = new Date(startDate);\r\n            const isToday = isSameDate(startDate, today);\r\n            \r\n            if (isToday) {\r\n                // 如果是今天，送用户0-1天优惠\r\n                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);\r\n            } else {\r\n                // 如果不是今天，正常计算（不送优惠天数）\r\n                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);\r\n            }\r\n            \r\n            const endDateStr = formatDate(endDate);\r\n            packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;\r\n        }\r\n    } else {\r\n        // 没有会员过期记录，使用接口返回的finalBeginTime作为开始时间\r\n        if (packageJudge.value.finalBeginTime) {\r\n            // 将finalBeginTime转换为日期部分并设置为00:00:00\r\n            const datePart = extractDatePart(packageJudge.value.finalBeginTime);\r\n            packageOrder.value.beginVipTime = `${datePart} 00:00:00`;\r\n            \r\n            // 计算结束时间：开始时间+套餐天数（车辆在场额外优惠）\r\n            if (packageOrder.value.packageDays) {\r\n                const startDate = new Date(packageOrder.value.beginVipTime);\r\n                const endDate = new Date(startDate);\r\n                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);\r\n                \r\n                const endDateStr = formatDate(endDate);\r\n                packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;\r\n            }\r\n        } else {\r\n            uni.showToast({\r\n                title: '参数错误，请联系客服',\r\n                icon: 'none',\r\n                duration: 2000\r\n            });\r\n            return;\r\n        }\r\n    }\r\n    \r\n    canSelectTime.value = false;\r\n}\r\n\r\n// 处理车辆不在场开通会员\r\nconst handleCarNotInWarehouse = () => {\r\n    canSelectTime.value = true\r\n    \r\n    // 设置默认开始时间为今天\r\n    const today = new Date()\r\n    const dateStr = formatDate(today)\r\n    packageOrder.value.beginVipTime = `${dateStr} 00:00:00`\r\n    selectedDate.value = dateStr\r\n    \r\n    // 设置日期选择范围\r\n    updateDateRange()\r\n    calculateEndDate()\r\n    \r\n    console.log('车辆不在场，允许选择开始时间')\r\n}\r\n\r\n// 加载用户信息（从缓存获取手机号）\r\nconst loadUserInfo = () => {\r\n    try {\r\n        const wxUser = uni.getStorageSync('wxUser')\r\n        if (wxUser && wxUser.phoneNumber) {\r\n            userPhone.value = wxUser.phoneNumber\r\n        } else {\r\n            userPhone.value = '未获取到手机号'\r\n        }\r\n        console.log('获取用户手机号:', userPhone.value)\r\n    } catch (error) {\r\n        console.error('获取用户信息失败:', error)\r\n        userPhone.value = '获取失败'\r\n    }\r\n}\r\n\r\n// 加载场库列表\r\nconst loadWarehouseList = async () => {\r\n    try {\r\n        const res = await getParkWareHouseList()\r\n        warehouseList.value = res.data.map(item => ({\r\n            id: item.id,\r\n            name: item.warehouseName,\r\n            latitude: item.latitude,\r\n            longitude: item.longitude\r\n        }))\r\n        \r\n        // 设置当前场库\r\n        if (packageOrder.value.warehouseId && packageOrder.value.warehouseName) {\r\n            currentWarehouse.value = {\r\n                id: packageOrder.value.warehouseId,\r\n                name: packageOrder.value.warehouseName\r\n            }\r\n        }\r\n    } catch (error) {\r\n        console.error('获取场库列表失败:', error)\r\n        uni.showToast({\r\n            title: '场库数据加载失败',\r\n            icon: 'none'\r\n        })\r\n    }\r\n}\r\n\r\n// 场库选择相关方法\r\nconst handleWarehouseClick = () => {\r\n    if (!canSelectWarehouse.value) return\r\n    \r\n    if (!uni.getStorageSync(\"token\")) {\r\n        uni.showModal({\r\n            title: \"提示\",\r\n            content: \"请先登录\",\r\n            success: (res) => {\r\n                if (res.confirm) {\r\n                    uni.navigateTo({\r\n                        url: \"/pages/login/login\",\r\n                    })\r\n                }\r\n            },\r\n        })\r\n        return\r\n    }\r\n    showSelector.value = true\r\n}\r\n\r\nconst closeWarehouseSelector = () => {\r\n    showSelector.value = false\r\n}\r\n\r\nconst selectWarehouse = (warehouse) => {\r\n    packageOrder.value.warehouseId = warehouse.id\r\n    packageOrder.value.warehouseName = warehouse.name\r\n    currentWarehouse.value = warehouse\r\n    \r\n    uni.setStorageSync('currentWarehouse', currentWarehouse.value)\r\n    closeWarehouseSelector()\r\n    \r\n    uni.showToast({\r\n        title: `已选择${warehouse.name}`,\r\n        icon: \"success\",\r\n        duration: 1500,\r\n    })\r\n    \r\n    // 选择场库后，检查是否需要调用packageJudge\r\n    checkPackageJudge()\r\n}\r\n\r\n// 车牌选择相关方法\r\nconst handlePlateClick = () => {\r\n    if (!canSelectPlate.value) return\r\n    plateShow.value = true\r\n}\r\n\r\nconst setPlate = (plate) => {\r\n    if (plate.length >= 7) {\r\n        packageOrder.value.plateNo = plate\r\n        plateShow.value = false\r\n        \r\n        // 选择车牌后，检查是否需要调用packageJudge\r\n        checkPackageJudge()\r\n    }\r\n}\r\n\r\nconst typeChange = (e) => {\r\n    // 重置车牌号\r\n    packageOrder.value.plateNo = ''\r\n}\r\n\r\n// 套餐选择相关方法\r\nconst handlePackageClick = () => {\r\n    if (!canSelectPackage.value) return\r\n    showPackageSelector.value = true\r\n}\r\n\r\nconst selectPackageOption = (option) => {\r\n    selectedPackage.value = option\r\n    packageOrder.value.packageId = option.id\r\n    packageOrder.value.packageName = option.name\r\n    packageOrder.value.packagePrice = option.price\r\n    packageOrder.value.packageDays = option.days\r\n    showPackageSelector.value = false\r\n    \r\n    // 选择套餐后重新计算结束时间\r\n    if (packageOrder.value.isRenewal) {\r\n        // 续费：在原结束时间基础上延长\r\n        const originalEndDate = new Date(packageOrder.value.expirationTime)\r\n        const newEndDate = new Date(originalEndDate)\r\n        newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays)\r\n        \r\n        const endDateStr = formatDate(newEndDate)\r\n        packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`\r\n        \r\n        console.log('续费套餐选择完成，新结束时间:', packageOrder.value.newExpirationTime)\r\n    } else if (packageOrder.value.beginVipTime) {\r\n        // 非续费：正常计算结束时间\r\n        calculateEndDate()\r\n    }\r\n    \r\n    uni.showToast({\r\n        title: `已选择${option.name}`,\r\n        icon: 'success',\r\n        duration: 1500\r\n    })\r\n}\r\n\r\n// 时间相关方法\r\nconst updateDateRange = () => {\r\n    if (!canSelectTime.value) return\r\n    \r\n    const today = new Date()\r\n    minDate.value = formatDate(today)\r\n    \r\n    const maxDateObj = new Date()\r\n    maxDateObj.setMonth(today.getMonth() + 3)\r\n    maxDate.value = formatDate(maxDateObj)\r\n    \r\n    selectedDate.value = formatDate(today)\r\n}\r\n\r\nconst onDateChange = (e) => {\r\n    if (!canSelectTime.value) return\r\n    \r\n    const selectedDateStr = e.detail.value\r\n    packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`\r\n    selectedDate.value = selectedDateStr\r\n    \r\n    calculateEndDate()\r\n}\r\n\r\nconst calculateEndDate = () => {\r\n    if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {\r\n        const startDate = new Date(packageOrder.value.beginVipTime)\r\n        const today = new Date()\r\n        const endDate = new Date(startDate)\r\n        \r\n        const isToday = isSameDate(startDate, today)\r\n        \r\n        if (isToday) {\r\n            // 如果是今天，送用户一天（套餐天数+1天-1）\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays)\r\n            console.log('车辆不在场，开始时间是今天，送用户一天，截止日期', endDate)\r\n        } else {\r\n            // 如果不是今天，正常计算（套餐天数-1天）\r\n            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1)\r\n        }\r\n\r\n        const endDateStr = formatDate(endDate)\r\n        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`\r\n    }\r\n}\r\n\r\n// 提交订单\r\nconst submitOrder = () => {\r\n    // 验证必填项\r\n    if (!packageOrder.value.plateNo) {\r\n        uni.showToast({\r\n            title: '请选择车牌号',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    if (!packageOrder.value.warehouseId) {\r\n        uni.showToast({\r\n            title: '请选择场库',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    if (packageOrder.value.vipType === 1 && !packageOrder.value.packageName) {\r\n        uni.showToast({\r\n            title: '请选择套餐类型',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    if (!packageOrder.value.beginVipTime) {\r\n        uni.showToast({\r\n            title: '请选择开始时间',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    // 校验到期时间\r\n    const currentTime = new Date()\r\n    let endTime\r\n    \r\n    if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {\r\n        endTime = new Date(packageOrder.value.newExpirationTime)\r\n    } else if (packageOrder.value.expirationTime) {\r\n        endTime = new Date(packageOrder.value.expirationTime)\r\n    } else {\r\n        uni.showToast({\r\n            title: '套餐到期时间异常，请重新选择',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        return\r\n    }\r\n    \r\n    if (endTime <= currentTime) {\r\n        uni.showToast({\r\n            title: '套餐到期时间不能早于当前时间，请重新选择',\r\n            icon: 'none',\r\n            duration: 3000\r\n        })\r\n        return\r\n    }\r\n    \r\n    // 微信登录获取openid\r\n    uni.login({\r\n        success: async (loginRes) => {\r\n            console.log('登录成功 res:', loginRes)\r\n            try {\r\n                const openidRes = await getOpenid({\r\n                    wxCode: loginRes.code\r\n                })\r\n                console.log('获取openid res:', openidRes)\r\n                \r\n                packageOrder.value.openid = openidRes.data\r\n                packageOrder.value.userPhone = userPhone.value\r\n                \r\n                createOrderWithOpenid()\r\n            } catch (error) {\r\n                console.error('获取openid失败:', error)\r\n                uni.showToast({\r\n                    title: '获取用户信息失败',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            }\r\n        },\r\n        fail: (error) => {\r\n            console.error('登录失败:', error)\r\n            uni.showToast({\r\n                title: '登录失败，请重试',\r\n                icon: 'none',\r\n                duration: 2000\r\n            })\r\n        }\r\n    })\r\n}\r\n\r\n// 创建订单\r\nconst createOrderWithOpenid = () => {\r\n    uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n    })\r\n    \r\n    createOrder(packageOrder.value).then(res => {\r\n        console.log('创建订单 res:', res)\r\n        if (res.data.needPay) {\r\n            uni.requestPayment({\r\n                timeStamp: res.data.timeStamp,\r\n                nonceStr: res.data.nonceStr,\r\n                package: res.data.package,\r\n                signType: res.data.signType,\r\n                paySign: res.data.paySign,\r\n                success: function (result) {\r\n                    // 延迟一下显示toast，避免与complete中的hideLoading冲突\r\n                    uni.hideLoading()\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付成功~',\r\n                            icon: 'none',\r\n                            duration: 2000\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                },\r\n                fail: function (err) {\r\n                    uni.hideLoading()\r\n                    console.log('支付失败的回调：', err)\r\n\r\n                    // 调用更新订单接口，将状态改为已取消\r\n                    if (res.data.orderId) {\r\n                        updateOrder({\r\n                            id: res.data.orderId,\r\n                            payStatus: 3  // 已取消\r\n                        }).then(updateRes => {\r\n                            console.log('订单状态更新为已取消：', updateRes)\r\n                        }).catch(updateErr => {\r\n                            console.log('订单状态更新失败：', updateErr)\r\n                            // 即使更新失败也不影响用户体验\r\n                        })\r\n                    }\r\n\r\n                    // 延迟一下显示toast，避免与hideLoading冲突\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付失败',\r\n                            icon: 'none',\r\n                            duration: 1500\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                },\r\n                complete: function (res) {\r\n                    uni.hideLoading()\r\n                }\r\n            })\r\n        } else if (!res.data.needPay){\r\n            uni.hideLoading()\r\n            setTimeout(() => {\r\n                uni.showToast({\r\n                    title: '开通成功~',\r\n                    icon: 'none',\r\n                    duration: 2000\r\n                })\r\n            }, 100)\r\n            setTimeout(() => {\r\n                uni.navigateBack()\r\n            }, 2000)\r\n        }\r\n    }).catch(err => {\r\n        console.log(err)\r\n        uni.hideLoading()\r\n        setTimeout(() => {\r\n            uni.showToast({\r\n                title: '订单创建失败',\r\n                icon: 'none',\r\n                duration: 1500\r\n            })\r\n        }, 100)\r\n        setTimeout(() => {\r\n            uni.navigateBack()\r\n        }, 2000)\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-buy-container {\r\n    background-color: #f5f5f5;\r\n    min-height: 100vh;\r\n    padding: 20rpx;\r\n    padding-bottom: 40rpx;\r\n}\r\n\r\n.notice-card {\r\n    background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .notice-content {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 12rpx;\r\n        \r\n        .notice-text {\r\n            font-size: 26rpx;\r\n            color: #e65100;\r\n            line-height: 1.5;\r\n            flex: 1;\r\n        }\r\n    }\r\n}\r\n\r\n.form-container {\r\n    background: #ffffff;\r\n    border-radius: 20rpx;\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content_item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    transition: all 0.2s ease;\r\n\r\n    &:last-child {\r\n        border-bottom: none;\r\n    }\r\n}\r\n\r\n.title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    min-width: 160rpx;\r\n    \r\n    image {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        margin-right: 15rpx;\r\n    }\r\n}\r\n\r\n.word {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    text-align: right;\r\n    flex: 1;\r\n    \r\n    &.red {\r\n        color: #ff4757;\r\n        font-weight: bold;\r\n    }\r\n    \r\n    &.money {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #ff4757;\r\n    }\r\n    \r\n    &.clickable-text {\r\n        color: #4BA1FC;\r\n        font-weight: 500;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        text-decoration: underline;\r\n        text-decoration-color: rgba(75, 161, 252, 0.3);\r\n        text-underline-offset: 4rpx;\r\n    }\r\n    \r\n    .picker-display {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        width: 100%;\r\n        color: inherit;\r\n    }\r\n}\r\n\r\n.tips {\r\n    font-size: 24rpx;\r\n    \r\n    &.red {\r\n        color: #ff4757;\r\n    }\r\n}\r\n\r\n.button-section {\r\n    margin-top: 40rpx;\r\n\r\n    .pay-button {\r\n        width: 100%;\r\n        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);\r\n        border-radius: 44rpx;\r\n        color: #fff;\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        height: 88rpx;\r\n        line-height: 88rpx;\r\n        border: none;\r\n        box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\r\n\r\n        &:active {\r\n            transform: translateY(1rpx);\r\n            box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);\r\n        }\r\n\r\n        &[disabled] {\r\n            background: #ccc;\r\n            color: #999;\r\n            box-shadow: none;\r\n        }\r\n    }\r\n}\r\n\r\n// 套餐选择器样式\r\n.package-popup {\r\n    background: #fff;\r\n    \r\n    .popup-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 30rpx 40rpx;\r\n        border-bottom: 1rpx solid #f0f0f0;\r\n        \r\n        .popup-title {\r\n            font-size: 32rpx;\r\n            font-weight: 500;\r\n            color: #333;\r\n        }\r\n    }\r\n    \r\n    .package-list {\r\n        .package-item {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            padding: 30rpx 40rpx;\r\n            border-bottom: 1rpx solid #f0f0f0;\r\n            transition: background-color 0.2s;\r\n            \r\n            &:last-child {\r\n                border-bottom: none;\r\n            }\r\n            \r\n            &:active {\r\n                background-color: #f8f9fa;\r\n            }\r\n            \r\n            &.active {\r\n                background-color: #f0f8ff;\r\n            }\r\n            \r\n            .package-item-left {\r\n                display: flex;\r\n                flex-direction: column;\r\n                \r\n                .package-item-name {\r\n                    font-size: 28rpx;\r\n                    color: #333;\r\n                    font-weight: 500;\r\n                    margin-bottom: 8rpx;\r\n                }\r\n                \r\n                .package-item-days {\r\n                    font-size: 24rpx;\r\n                    color: #666;\r\n                }\r\n            }\r\n            \r\n            .package-item-right {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 16rpx;\r\n                \r\n                .package-item-price {\r\n                    font-size: 28rpx;\r\n                    color: #ff4757;\r\n                    font-weight: bold;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/package/packageVipBuy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "uni", "checkCarInWarehouse", "formatDate", "isSameDate", "extractDatePart", "getParkWareHouseList", "getOpenid", "createOrder", "updateOrder", "res"], "mappings": ";;;;;;;;;;;;;;;;;;;AAmKA,MAAM,aAAa,MAAW;AAC9B,MAAM,oBAAoB,MAAW;;;;AAOrC,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,eAAeA,cAAG,IAAC,KAAK;AAC9B,UAAM,mBAAmBA,cAAG,IAAC,EAAE,IAAI,GAAG,MAAM,IAAI;AAGhD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACrB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,OAAO;AAAA,IACX,CAAC;AACD,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3B,UAAM,mBAAmBA,cAAG,IAAC,KAAK;AAClC,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,qBAAqBA,cAAG,IAAC,IAAI;AACnC,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAC/B,UAAM,mBAAmBA,cAAG,IAAC,KAAK;AAGlC,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,UAAUA,cAAG,IAAC,EAAE;AAGtB,UAAM,sBAAsBA,cAAG,IAAC,KAAK;AACrC,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,kBAAkBA,cAAG,IAAC,IAAI;AAGhC,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC7B,YAAM,oBAAoB,aAAa,MAAM,WACpB,aAAa,MAAM,eACnB,aAAa,MAAM;AAG5C,UAAI,aAAa,MAAM,YAAY,GAAG;AAClC,eAAO,qBAAqB,aAAa,MAAM,iBAAiB;AAAA,MACnE;AAGD,UAAI,aAAa,MAAM,YAAY,GAAG;AAClC,cAAM,sBAAsB,aAAa,MAAM,eACpB,aAAa,MAAM,gBAAgB,QACnC,aAAa,MAAM,iBAAiB;AAG/D,YAAI,aAAa,MAAM,WAAW;AAC9B,iBAAO,qBAAqB,uBAAuB,aAAa,MAAM,sBAAsB;AAAA,QAC/F;AAED,eAAO,qBAAqB;AAAA,MAC/B;AAED,aAAO;AAAA,IACX,CAAC;AAGD,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAC9B,YAAM,WAAW,aAAa,MAAM,YAAY,IAAI,SAAS;AAC7D,YAAM,cAAc,aAAa,MAAM,cACnC,GAAG,aAAa,MAAM,WAAW,IAAI,aAAa,MAAM,eAAe,EAAE,UACzE;AAEJ,UAAI,aAAa,MAAM,WAAW;AAC9B,YAAI,aAAa,MAAM,YAAY,GAAG;AAElC,iBAAO,YAAY,WAAW;AAAA;AAAA,QAE1C,OAAe;AAEH,iBAAO,YAAY,WAAW;AAAA;AAAA,QAEjC;AAAA,MACJ;AAED,UAAI,iBAAiB,OAAO;AACxB,cAAM,mBAAmB,aAAa,MAAM,eAAe;AAC3D,cAAM,oBAAoB,aAAa,MAAM,mBAAmB;AAEhE,YAAI,oBAAoB,mBAAmB;AACvC,iBAAO,KAAK,QAAQ,OAAO,WAAW;AAAA;AAAA,QAElD,OAAe;AACH,iBAAO,KAAK,QAAQ,OAAO,WAAW;AAAA,oCACd,aAAa,MAAM,cAAc;AAAA,QAC5D;AAAA,MACJ;AAED,aAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,IAE5C,CAAC;AAGDC,kBAAM,OAAC,CAAC,YAAY;AAChB,mBAAc;AACd,wBAAmB;AACnBC,oBAAAA,MAAA,MAAA,OAAA,0CAAY,WAAW,OAAO;AAE9B,UAAI,QAAQ,cAAc;AACtB,YAAI;AACA,gBAAM,cAAc,KAAK,MAAM,mBAAmB,QAAQ,YAAY,CAAC;AAEvE,uBAAa,QAAQ,OAAO,OAAO,CAAA,GAAI,aAAa,OAAO,WAAW;AACtE,uBAAa,MAAM,cAAc,UAAU;AAG3C,cAAI,aAAa,MAAM,aAAa,aAAa,MAAM,YAAY,KAAK,CAAC,aAAa,MAAM,aAAa;AACrG,yBAAa,MAAM,oBAAoB;AAAA,UAC1C;AAEDA,wBAAA,MAAA,MAAA,OAAA,0CAAY,YAAY,aAAa,KAAK;AAG1C,4BAAmB;AAGnB,cAAI,aAAa,MAAM,WAAW;AAC9B,+BAAoB;AAAA,UACpC,OAAmB;AAEH,8BAAmB;AAAA,UACtB;AAAA,QACJ,SAAQ,OAAO;AACZA,wBAAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,KAAK;AAAA,QACnC;AAAA,MACJ;AAAA,IACL,CAAC;AAID,UAAM,oBAAoB,MAAM;AAE5B,UAAI,aAAa,MAAM,WAAW;AAC9B,2BAAmB,QAAQ;AAC3B,uBAAe,QAAQ;AACvB,sBAAc,QAAQ;AAEtB,yBAAiB,QAAQ,aAAa,MAAM,YAAY;AACxD,YAAI,iBAAiB,OAAO;AACxB,iCAAwB;AAAA,QAC3B;AACD;AAAA,MACH;AAID,UAAI,aAAa,MAAM,WAAW,aAAa,MAAM,YAAY,MAAM;AACnE,uBAAe,QAAQ;AACvB,2BAAmB,QAAQ;AAAA,MACnC,OAAW;AAEH,uBAAe,QAAQ;AACvB,2BAAmB,QAAQ;AAAA,MAC9B;AAGD,UAAI,aAAa,MAAM,YAAY,GAAG;AAClC,yBAAiB,QAAQ;AACzB,+BAAwB;AAAA,MAC3B;AAAA,IACL;AAGA,UAAM,yBAAyB,MAAM;AACjC,YAAM,QAAQ,aAAa,MAAM,SAAS;AAC1C,YAAM,UAAU,CAAE;AAkClB,UAAI,UAAU,GAAG;AAEb,gBAAQ,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACnB,CAAS;AAAA,MACT,WAAe,UAAU,GAAG;AAEpB,gBAAQ,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACnB,CAAS;AACD,gBAAQ,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACnB,CAAS;AAAA,MACT,WAAe,UAAU,GAAG;AAEpB,gBAAQ,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACnB,CAAS;AACD,gBAAQ,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACnB,CAAS;AAAA,MACJ;AAED,qBAAe,QAAQ;AAAA,IAC3B;AAGA,UAAM,oBAAoB,MAAM;AAE5B,UAAI,aAAa,MAAM,WAAW,aAAa,MAAM,aAAa;AAC9D,4BAAqB;AAAA,MAC7B,OAAW;AAEH,gCAAyB;AAAA,MAC5B;AAAA,IACL;AAGA,UAAM,sBAAsB,YAAY;AACpC,UAAI;AACA,cAAM,MAAM,MAAMC,gCAAoB;AAAA,UAClC,SAAS,aAAa,MAAM;AAAA,UAC5B,aAAa,aAAa,MAAM;AAAA,QAC5C,CAAS;AAED,qBAAa,QAAQ,IAAI;AACzB,yBAAiB,QAAQ,IAAI,QAAQ,IAAI,KAAK;AAE9CD,sBAAA,MAAA,MAAA,OAAA,0CAAY,mBAAmB,IAAI,IAAI;AACvCA,sBAAY,MAAA,MAAA,OAAA,0CAAA,WAAW,iBAAiB,KAAK;AAE7C,YAAI,iBAAiB,OAAO;AACxB,+BAAsB;AAAA,QAClC,OAAe;AACH,kCAAyB;AAAA,QAC5B;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,WAAW,KAAK;AAC9B,yBAAiB,QAAQ;AACzB,gCAAyB;AAAA,MAC5B;AAAA,IACL;AAGA,UAAM,qBAAqB,MAAM;AAC7B,UAAI,CAAC,aAAa,MAAM,gBAAgB,CAAC,aAAa,MAAM,gBAAgB;AACxEA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAGD,UAAI,aAAa,MAAM,YAAY,KAAK,CAAC,aAAa,MAAM,aAAa;AAErE,qBAAa,MAAM,oBAAoB;AACvCA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,iBAAiB;AAC7B;AAAA,MACH;AAGD,YAAM,kBAAkB,IAAI,KAAK,aAAa,MAAM,cAAc;AAClE,YAAM,aAAa,IAAI,KAAK,eAAe;AAC3C,iBAAW,QAAQ,gBAAgB,QAAS,IAAG,aAAa,MAAM,WAAW;AAE7E,YAAM,aAAaE,YAAU,WAAC,UAAU;AACxC,mBAAa,MAAM,oBAAoB,GAAG,UAAU;AAEpDF,oBAAA,MAAA,MAAA,OAAA,0CAAY,mBAAmB,aAAa,MAAM,iBAAiB;AAAA,IACvE;AAGA,UAAM,uBAAuB,MAAM;AAE/B,YAAM,mBAAmB,aAAa,MAAM,eAAe;AAE3D,UAAI,oBAAoB,aAAa,MAAM,gBAAgB;AAEvD,cAAM,iBAAiB,IAAI,KAAK,aAAa,MAAM,cAAc;AACjE,YAAI,YAAY,IAAI,KAAK,cAAc;AAGvC,cAAM,oBAAoB,aAAa,MAAM,mBAAmB;AAChE,cAAM,0BAA0B,qBAC5B,aAAa,MAAM,mBAAmB,aAAa,MAAM;AAE7D,YAAI,yBAAyB;AAEzB,oBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QACzC,OAAe;AAEH,oBAAU,QAAQ,eAAe,QAAS,IAAG,CAAC;AAC9C,oBAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QAChC;AAED,cAAM,eAAeE,uBAAW,SAAS;AACzC,qBAAa,MAAM,eAAe,GAAG,YAAY;AAGjD,YAAI,aAAa,MAAM,aAAa;AAChC,gBAAM,QAAQ,oBAAI;AAClB,gBAAM,UAAU,IAAI,KAAK,SAAS;AAClC,gBAAM,UAAUC,YAAAA,WAAW,WAAW,KAAK;AAE3C,cAAI,SAAS;AAET,oBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AAAA,UACpF,OAAmB;AAEH,oBAAQ,QAAQ,UAAU,QAAO,IAAK,aAAa,MAAM,cAAc,CAAC;AAAA,UAC3E;AAED,gBAAM,aAAaD,uBAAW,OAAO;AACrC,uBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,QACpD;AAAA,MACT,OAAW;AAEH,YAAI,aAAa,MAAM,gBAAgB;AAEnC,gBAAM,WAAWE,YAAe,gBAAC,aAAa,MAAM,cAAc;AAClE,uBAAa,MAAM,eAAe,GAAG,QAAQ;AAG7C,cAAI,aAAa,MAAM,aAAa;AAChC,kBAAM,YAAY,IAAI,KAAK,aAAa,MAAM,YAAY;AAC1D,kBAAM,UAAU,IAAI,KAAK,SAAS;AAClC,oBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AAEpE,kBAAM,aAAaF,uBAAW,OAAO;AACrC,yBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,UACpD;AAAA,QACb,OAAe;AACHF,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AACD;AAAA,QACH;AAAA,MACJ;AAED,oBAAc,QAAQ;AAAA,IAC1B;AAGA,UAAM,0BAA0B,MAAM;AAClC,oBAAc,QAAQ;AAGtB,YAAM,QAAQ,oBAAI,KAAM;AACxB,YAAM,UAAUE,YAAU,WAAC,KAAK;AAChC,mBAAa,MAAM,eAAe,GAAG,OAAO;AAC5C,mBAAa,QAAQ;AAGrB,sBAAiB;AACjB,uBAAkB;AAElBF,oBAAAA,MAAA,MAAA,OAAA,0CAAY,gBAAgB;AAAA,IAChC;AAGA,UAAM,eAAe,MAAM;AACvB,UAAI;AACA,cAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,YAAI,UAAU,OAAO,aAAa;AAC9B,oBAAU,QAAQ,OAAO;AAAA,QACrC,OAAe;AACH,oBAAU,QAAQ;AAAA,QACrB;AACDA,sBAAA,MAAA,MAAA,OAAA,0CAAY,YAAY,UAAU,KAAK;AAAA,MAC1C,SAAQ,OAAO;AACZA,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChC,kBAAU,QAAQ;AAAA,MACrB;AAAA,IACL;AAGA,UAAM,oBAAoB,YAAY;AAClC,UAAI;AACA,cAAM,MAAM,MAAMK,mCAAsB;AACxC,sBAAc,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,UACxC,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,QAC5B,EAAU;AAGF,YAAI,aAAa,MAAM,eAAe,aAAa,MAAM,eAAe;AACpE,2BAAiB,QAAQ;AAAA,YACrB,IAAI,aAAa,MAAM;AAAA,YACvB,MAAM,aAAa,MAAM;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ,SAAQ,OAAO;AACZL,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,uBAAuB,MAAM;AAC/B,UAAI,CAAC,mBAAmB;AAAO;AAE/B,UAAI,CAACA,cAAG,MAAC,eAAe,OAAO,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACd,gBAAI,IAAI,SAAS;AACbA,4BAAAA,MAAI,WAAW;AAAA,gBACX,KAAK;AAAA,cAC7B,CAAqB;AAAA,YACJ;AAAA,UACJ;AAAA,QACb,CAAS;AACD;AAAA,MACH;AACD,mBAAa,QAAQ;AAAA,IACzB;AAEA,UAAM,yBAAyB,MAAM;AACjC,mBAAa,QAAQ;AAAA,IACzB;AAEA,UAAM,kBAAkB,CAAC,cAAc;AACnC,mBAAa,MAAM,cAAc,UAAU;AAC3C,mBAAa,MAAM,gBAAgB,UAAU;AAC7C,uBAAiB,QAAQ;AAEzBA,oBAAAA,MAAI,eAAe,oBAAoB,iBAAiB,KAAK;AAC7D,6BAAwB;AAExBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO,MAAM,UAAU,IAAI;AAAA,QAC3B,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAK;AAGD,wBAAmB;AAAA,IACvB;AAGA,UAAM,mBAAmB,MAAM;AAC3B,UAAI,CAAC,eAAe;AAAO;AAC3B,gBAAU,QAAQ;AAAA,IACtB;AAEA,UAAM,WAAW,CAAC,UAAU;AACxB,UAAI,MAAM,UAAU,GAAG;AACnB,qBAAa,MAAM,UAAU;AAC7B,kBAAU,QAAQ;AAGlB,0BAAmB;AAAA,MACtB;AAAA,IACL;AAEA,UAAM,aAAa,CAAC,MAAM;AAEtB,mBAAa,MAAM,UAAU;AAAA,IACjC;AAGA,UAAM,qBAAqB,MAAM;AAC7B,UAAI,CAAC,iBAAiB;AAAO;AAC7B,0BAAoB,QAAQ;AAAA,IAChC;AAEA,UAAM,sBAAsB,CAAC,WAAW;AACpC,sBAAgB,QAAQ;AACxB,mBAAa,MAAM,YAAY,OAAO;AACtC,mBAAa,MAAM,cAAc,OAAO;AACxC,mBAAa,MAAM,eAAe,OAAO;AACzC,mBAAa,MAAM,cAAc,OAAO;AACxC,0BAAoB,QAAQ;AAG5B,UAAI,aAAa,MAAM,WAAW;AAE9B,cAAM,kBAAkB,IAAI,KAAK,aAAa,MAAM,cAAc;AAClE,cAAM,aAAa,IAAI,KAAK,eAAe;AAC3C,mBAAW,QAAQ,gBAAgB,QAAS,IAAG,aAAa,MAAM,WAAW;AAE7E,cAAM,aAAaE,YAAU,WAAC,UAAU;AACxC,qBAAa,MAAM,oBAAoB,GAAG,UAAU;AAEpDF,sBAAA,MAAA,MAAA,OAAA,0CAAY,mBAAmB,aAAa,MAAM,iBAAiB;AAAA,MAC3E,WAAe,aAAa,MAAM,cAAc;AAExC,yBAAkB;AAAA,MACrB;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO,MAAM,OAAO,IAAI;AAAA,QACxB,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAK;AAAA,IACL;AAGA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,CAAC,cAAc;AAAO;AAE1B,YAAM,QAAQ,oBAAI,KAAM;AACxB,cAAQ,QAAQE,YAAU,WAAC,KAAK;AAEhC,YAAM,aAAa,oBAAI,KAAM;AAC7B,iBAAW,SAAS,MAAM,SAAQ,IAAK,CAAC;AACxC,cAAQ,QAAQA,YAAU,WAAC,UAAU;AAErC,mBAAa,QAAQA,YAAU,WAAC,KAAK;AAAA,IACzC;AAEA,UAAM,eAAe,CAAC,MAAM;AACxB,UAAI,CAAC,cAAc;AAAO;AAE1B,YAAM,kBAAkB,EAAE,OAAO;AACjC,mBAAa,MAAM,eAAe,GAAG,eAAe;AACpD,mBAAa,QAAQ;AAErB,uBAAkB;AAAA,IACtB;AAEA,UAAM,mBAAmB,MAAM;AAC3B,UAAI,aAAa,MAAM,gBAAgB,aAAa,MAAM,aAAa;AACnE,cAAM,YAAY,IAAI,KAAK,aAAa,MAAM,YAAY;AAC1D,cAAM,QAAQ,oBAAI,KAAM;AACxB,cAAM,UAAU,IAAI,KAAK,SAAS;AAElC,cAAM,UAAUC,YAAAA,WAAW,WAAW,KAAK;AAE3C,YAAI,SAAS;AAET,kBAAQ,QAAQ,UAAU,QAAS,IAAG,aAAa,MAAM,WAAW;AACpEH,wBAAAA,6DAAY,4BAA4B,OAAO;AAAA,QAC3D,OAAe;AAEH,kBAAQ,QAAQ,UAAU,QAAO,IAAK,aAAa,MAAM,cAAc,CAAC;AAAA,QAC3E;AAED,cAAM,aAAaE,YAAU,WAAC,OAAO;AACrC,qBAAa,MAAM,iBAAiB,GAAG,UAAU;AAAA,MACpD;AAAA,IACL;AAGA,UAAM,cAAc,MAAM;AAEtB,UAAI,CAAC,aAAa,MAAM,SAAS;AAC7BF,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI,CAAC,aAAa,MAAM,aAAa;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI,aAAa,MAAM,YAAY,KAAK,CAAC,aAAa,MAAM,aAAa;AACrEA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI,CAAC,aAAa,MAAM,cAAc;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAGD,YAAM,cAAc,oBAAI,KAAM;AAC9B,UAAI;AAEJ,UAAI,aAAa,MAAM,aAAa,aAAa,MAAM,mBAAmB;AACtE,kBAAU,IAAI,KAAK,aAAa,MAAM,iBAAiB;AAAA,MAC/D,WAAe,aAAa,MAAM,gBAAgB;AAC1C,kBAAU,IAAI,KAAK,aAAa,MAAM,cAAc;AAAA,MAC5D,OAAW;AACHA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAED,UAAI,WAAW,aAAa;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AACD;AAAA,MACH;AAGDA,oBAAAA,MAAI,MAAM;AAAA,QACN,SAAS,OAAO,aAAa;AACzBA,wBAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa,QAAQ;AACjC,cAAI;AACA,kBAAM,YAAY,MAAMM,oBAAU;AAAA,cAC9B,QAAQ,SAAS;AAAA,YACrC,CAAiB;AACDN,0BAAAA,6DAAY,iBAAiB,SAAS;AAEtC,yBAAa,MAAM,SAAS,UAAU;AACtC,yBAAa,MAAM,YAAY,UAAU;AAEzC,kCAAuB;AAAA,UAC1B,SAAQ,OAAO;AACZA,0BAAAA,+DAAc,eAAe,KAAK;AAClCA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACJ;AAAA,QACJ;AAAA,QACD,MAAM,CAAC,UAAU;AACbA,wBAAAA,MAAc,MAAA,SAAA,0CAAA,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,wBAAwB,MAAM;AAChCA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AAEDO,kBAAAA,YAAY,aAAa,KAAK,EAAE,KAAK,SAAO;AACxCP,sBAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa,GAAG;AAC5B,YAAI,IAAI,KAAK,SAAS;AAClBA,wBAAAA,MAAI,eAAe;AAAA,YACf,WAAW,IAAI,KAAK;AAAA,YACpB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,SAAS,SAAU,QAAQ;AAEvBA,4BAAAA,MAAI,YAAa;AACjB,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YACV;AAAA,YACD,MAAM,SAAU,KAAK;AACjBA,4BAAAA,MAAI,YAAa;AACjBA,4BAAAA,MAAA,MAAA,OAAA,0CAAY,YAAY,GAAG;AAG3B,kBAAI,IAAI,KAAK,SAAS;AAClBQ,wCAAY;AAAA,kBACR,IAAI,IAAI,KAAK;AAAA,kBACb,WAAW;AAAA;AAAA,gBACvC,CAAyB,EAAE,KAAK,eAAa;AACjBR,gCAAAA,MAAY,MAAA,OAAA,0CAAA,eAAe,SAAS;AAAA,gBAChE,CAAyB,EAAE,MAAM,eAAa;AAClBA,gCAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa,SAAS;AAAA,gBAE9D,CAAyB;AAAA,cACJ;AAGD,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YACV;AAAA,YACD,UAAU,SAAUS,MAAK;AACrBT,4BAAAA,MAAI,YAAa;AAAA,YACpB;AAAA,UACjB,CAAa;AAAA,QACJ,WAAU,CAAC,IAAI,KAAK,SAAQ;AACzBA,wBAAAA,MAAI,YAAa;AACjB,qBAAW,MAAM;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC9B,CAAiB;AAAA,UACJ,GAAE,GAAG;AACN,qBAAW,MAAM;AACbA,0BAAAA,MAAI,aAAc;AAAA,UACrB,GAAE,GAAI;AAAA,QACV;AAAA,MACT,CAAK,EAAE,MAAM,SAAO;AACZA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,GAAG;AACfA,sBAAAA,MAAI,YAAa;AACjB,mBAAW,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAC1B,CAAa;AAAA,QACJ,GAAE,GAAG;AACN,mBAAW,MAAM;AACbA,wBAAAA,MAAI,aAAc;AAAA,QACrB,GAAE,GAAI;AAAA,MACf,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC17BA,GAAG,WAAW,eAAe;"}