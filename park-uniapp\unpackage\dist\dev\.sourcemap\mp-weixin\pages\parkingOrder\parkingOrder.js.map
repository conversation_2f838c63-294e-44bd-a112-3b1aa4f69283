{"version": 3, "file": "parkingOrder.js", "sources": ["pages/parkingOrder/parkingOrder.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFya2luZ09yZGVyL3BhcmtpbmdPcmRlci52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"parking-order\">\r\n        <!-- 筛选器 - 固定在顶部 -->\r\n        <view class=\"filter-tabs-fixed\">\r\n            <view class=\"filter-tab\" :class=\"{ active: activeFilter === 0 }\" @tap=\"setFilter(0)\">\r\n                全部\r\n            </view>\r\n            <view class=\"filter-tab\" :class=\"{ active: activeFilter === 1 }\" @tap=\"setFilter(1)\">\r\n                进行中\r\n            </view>\r\n            <view class=\"filter-tab\" :class=\"{ active: activeFilter === 5 }\" @tap=\"setFilter(5)\">\r\n                已支付\r\n            </view>\r\n            <view class=\"filter-tab\" :class=\"{ active: activeFilter === 4 }\" @tap=\"setFilter(4)\">\r\n                已退款\r\n            </view>\r\n        </view>\r\n\r\n        <view class=\"content\">\r\n            <template v-if=\"orderList.length > 0\">\r\n                <view class=\"order-list\">\r\n                    <view class=\"order-item\" v-for=\"item in orderList\" :key=\"item.id\">\r\n                        <view class=\"order-card\">\r\n                            <view class=\"card-header\">\r\n                                <view class=\"order-info\">\r\n                                    <text class=\"order-no\">{{ item.tradeId || '订单号待生成' }}</text>\r\n                                </view>\r\n                                <view class=\"header-right\">\r\n                                    <view class=\"status-badge\" :class=\"getStatusClass(item.payStatus)\">\r\n                                        {{ getStatusText(item.payStatus) }}\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n\r\n                            <view class=\"card-body\">\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">停车场:</text>\r\n                                    <text class=\"info-value\">{{ item.warehouseName || '未知停车场' }}</text>\r\n                                    <text class=\"info-label plate-label\">车牌:</text>\r\n                                    <text class=\"info-value\">{{ item.plateNo }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">入场时间:</text>\r\n                                    <text class=\"info-value time\">{{ item.beginParkingTime }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">出场时间:</text>\r\n                                    <text class=\"info-value time\">{{ item.endParkingTime }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">停车时长:</text>\r\n                                    <text class=\"info-value\">{{ formatDuration(item.parkingDuration) }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"info-item\">\r\n                                    <text class=\"info-label\">支付时间:</text>\r\n                                    <text class=\"info-value time\">{{ item.paymentTime }}</text>\r\n                                </view>\r\n\r\n                                <view class=\"card-bottom\">\r\n                                    <view class=\"price-info\">\r\n                                        <view class=\"price-item\" v-if=\"item.discountAmount > 0\">\r\n                                            <text class=\"price-label\">优惠:</text>\r\n                                            <text class=\"price-value discount\">-¥{{ item.discountAmount }}</text>\r\n                                        </view>\r\n                                        <view class=\"price-item\">\r\n                                            <text class=\"price-label\">实付:</text>\r\n                                            <text class=\"price-value actual\">¥{{ item.actualPayment }}</text>\r\n                                        </view>\r\n                                    </view>\r\n\r\n                                    <!-- 按钮区域 -->\r\n                                    <!-- 进行中订单 - 去支付按钮 -->\r\n                                    <template v-if=\"item.payStatus === 1\">\r\n                                        <view class=\"invoice-actions\">\r\n                                            <view class=\"invoice-btn pay-btn\" @tap=\"goToPay(item)\">\r\n                                                <up-icon name=\"rmb-circle-fill\" color=\"#ffffff\" size=\"14\"></up-icon>\r\n                                                <text>去支付</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </template>\r\n\r\n                                    <!-- 已支付订单 - 开发票按钮区域 -->\r\n                                    <template v-if=\"item.payStatus === 5\">\r\n                                        <view class=\"invoice-actions\">\r\n                                            <!-- 已开具发票，可发送邮箱 -->\r\n                                            <template\r\n                                                v-if=\"item.miniInvoiceRecord && item.miniInvoiceRecord.status === 'ISSUED'\">\r\n                                                <view class=\"invoice-btn send-btn\"\r\n                                                    @tap=\"chooseEmail(item.miniInvoiceRecord.id)\">\r\n                                                    <up-icon name=\"email\" color=\"#3b82f6\" size=\"14\"></up-icon>\r\n                                                    <text>发送邮箱</text>\r\n                                                </view>\r\n                                            </template>\r\n\r\n                                            <!-- 未开具发票，可开发票 -->\r\n                                            <template\r\n                                                v-if=\"!item.miniInvoiceRecord || \r\n                                                (item.miniInvoiceRecord && (item.miniInvoiceRecord.status === 'UNISSU'\r\n                                                 || item.miniInvoiceRecord.status === 'CLOSED') && !item.miniInvoiceRecord.reopenSign)\">\r\n                                                <view class=\"invoice-btn open-btn\" @tap=\"routeToInvoice(item)\">\r\n                                                    <up-icon name=\"file-text\" color=\"#ffffff\" size=\"14\"></up-icon>\r\n                                                    <text>开发票</text>\r\n                                                </view>\r\n                                            </template>\r\n\r\n                                            <!-- 申请换开 -->\r\n                                            <template\r\n                                                v-if=\"item.miniInvoiceRecord && ((item.miniInvoiceRecord.status === 'ISSUED' && !item.miniInvoiceRecord.reopenSign) \r\n                                                || (item.miniInvoiceRecord.status === 'CLOSED' && item.miniInvoiceRecord.reopenSign) || item.miniInvoiceRecord.status === 'REVERSED')\">\r\n                                                <view class=\"invoice-btn reopen-btn\" @tap=\"routeToInvoice(item)\">\r\n                                                    <up-icon name=\"reload\" color=\"#ffffff\" size=\"14\"></up-icon>\r\n                                                    <text>申请换开</text>\r\n                                                </view>\r\n                                            </template>\r\n                                        </view>\r\n                                    </template>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </template>\r\n\r\n            <template v-else>\r\n                <view class=\"empty-state\">\r\n                    <up-empty text=\"暂无相关记录\" color=\"#64748b\"></up-empty>\r\n                </view>\r\n            </template>\r\n        </view>\r\n\r\n        <!-- 邮箱发送弹窗 -->\r\n        <up-popup :show=\"showPopup\" mode=\"center\" :round=\"10\" :safeAreaInsetBottom=\"false\" closeOnClickOverlay\r\n            @close=\"showPopup = false\">\r\n            <view class=\"popup-cell\">\r\n                <view class=\"email\">\r\n                    <view class=\"content_item\">\r\n                        <view class=\"email-title\">电子邮箱</view>\r\n                        <view class=\"email-input\">\r\n                            <up-input v-model=\"notifyEmail\" border=\"none\" placeholder=\"请输入您的电子邮箱\" clearable\r\n                                fontSize=\"28rpx\" color=\"#616161\" :placeholderStyle=\"placeholderStyle\"\r\n                                :customStyle=\"emailInputStyle\"></up-input>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"desc\">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>\r\n                </view>\r\n                <view class=\"choose_btn\">\r\n                    <view class=\"cancel_btn\" @tap=\"showPopup = false\">取消</view>\r\n                    <view class=\"sure_btn\" @tap=\"handlePostInvoiceSend\">确认</view>\r\n                </view>\r\n            </view>\r\n        </up-popup>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { onShow } from '@dcloudio/uni-app'\r\nimport { getParkingOrderList } from '@/api/parkingOrder'\r\nimport { postInvoiceSend } from '@/api/invoice'\r\n\r\nconst orderList = ref([])\r\nconst activeFilter = ref(0) // 当前选中的筛选条件，0代表全部\r\nconst showPopup = ref(false)\r\nconst invoiceId = ref(null)\r\nconst notifyEmail = ref('')\r\n\r\n// 样式对象\r\nconst placeholderStyle = {\r\n    color: '#616161'\r\n}\r\n\r\nconst emailInputStyle = {\r\n    paddingLeft: '20rpx',\r\n    paddingTop: '26rpx',\r\n    paddingBottom: '26rpx',\r\n    borderBottom: '1rpx solid rgba(189,189,189,0.2)'\r\n}\r\n\r\nonShow(() => {\r\n    getOrderList(0)\r\n})\r\n\r\n// 设置筛选条件 - 后端查询\r\nconst setFilter = async (payStatus) => {\r\n    if (activeFilter.value === payStatus) return // 避免重复请求\r\n    \r\n    activeFilter.value = payStatus\r\n    await getOrderList(payStatus)\r\n}\r\n\r\n// 获取停车订单列表 - 后端查询模式\r\nconst getOrderList = async (payStatus) => {\r\n    try {\r\n        uni.showLoading({\r\n            title: '加载中...',\r\n            mask: true\r\n        })\r\n        \r\n        // 构建查询参数\r\n        const params = {}\r\n        if (payStatus !== null) {\r\n            params.payStatus = payStatus\r\n        }\r\n        \r\n        const res = await getParkingOrderList(params)\r\n        console.log('停车订单数据:', res)\r\n        orderList.value = res.data || []\r\n        \r\n    } catch (error) {\r\n        console.error('获取停车订单失败:', error)\r\n        uni.showToast({\r\n            title: '获取订单失败',\r\n            icon: 'none'\r\n        })\r\n    } finally {\r\n        uni.hideLoading()\r\n    }\r\n}\r\n\r\n// 格式化停车时长（分钟转换为小时分钟）\r\nconst formatDuration = (minutes) => {\r\n    if (!minutes) return '0分钟'\r\n    \r\n    const hours = Math.floor(minutes / 60)\r\n    const mins = minutes % 60\r\n    \r\n    if (hours > 0) {\r\n        return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`\r\n    } else {\r\n        return `${mins}分钟`\r\n    }\r\n}\r\n\r\n// 获取支付状态对应的文本\r\nconst getStatusText = (status) => {\r\n    switch (status) {\r\n        case 1:\r\n            return '进行中'\r\n        case 4:\r\n            return '已退款'\r\n        case 5:\r\n            return '已支付'\r\n        default:\r\n            return '未知状态'\r\n    }\r\n}\r\n\r\n// 获取支付状态对应的样式类\r\nconst getStatusClass = (status) => {\r\n    switch (status) {\r\n        case 1:\r\n            return 'status-progress'\r\n        case 4:\r\n            return 'status-failed'\r\n        case 5:\r\n            return 'status-paid'\r\n        default:\r\n            return 'status-unknown'\r\n    }\r\n}\r\n\r\n// 跳转到支付页面\r\nconst goToPay = (item) => {\r\n    // 跳转到支付详情页\r\n    uni.navigateTo({\r\n        url: `/pages/payPlateDetail/payPlateDetail?plateNo=${item.plateNo}&warehouseId=${item.warehouseId}`\r\n    })\r\n}\r\n\r\n// 跳转到开发票页面\r\nconst routeToInvoice = (item) => {\r\n    // functionType 功能类型：1停车，2会员\r\n    if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {\r\n        // 发票重开\r\n        if (item.miniInvoiceRecord.isResume) {\r\n            uni.navigateTo({\r\n                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`\r\n            })\r\n        } else {\r\n            uni.navigateTo({\r\n                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}`\r\n            })\r\n        }\r\n    } else {\r\n        uni.navigateTo({\r\n            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1`\r\n        })\r\n    }\r\n}\r\n\r\n// 选择邮箱发送\r\nconst chooseEmail = (id) => {\r\n    invoiceId.value = id\r\n    showPopup.value = true\r\n}\r\n\r\n// 发送发票到邮箱\r\nconst handlePostInvoiceSend = async () => {\r\n    if (!notifyEmail.value) {\r\n        uni.showToast({\r\n            title: '请输入邮箱',\r\n            icon: 'none'\r\n        })\r\n        return\r\n    }\r\n    \r\n    try {\r\n        const params = {\r\n            id: invoiceId.value,\r\n            notifyEmail: notifyEmail.value\r\n        }\r\n        \r\n        await postInvoiceSend(params)\r\n        \r\n        uni.showToast({\r\n            title: '邮箱发送成功～',\r\n            icon: 'none',\r\n            duration: 2000\r\n        })\r\n        \r\n        setTimeout(() => {\r\n            showPopup.value = false\r\n            notifyEmail.value = ''\r\n        }, 1000)\r\n    } catch (error) {\r\n        console.error('发送邮箱失败:', error)\r\n        uni.showToast({\r\n            title: '发送失败，请重试',\r\n            icon: 'none'\r\n        })\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.parking-order {\r\n    min-height: 100vh;\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.filter-tabs-fixed {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    background: #ffffff;\r\n    z-index: 10;\r\n    padding: 20rpx 0;\r\n    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);\r\n    \r\n    display: flex;\r\n    border-radius: 0;\r\n    \r\n    .filter-tab {\r\n        flex: 1;\r\n        text-align: center;\r\n        padding: 16rpx 12rpx;\r\n        font-size: 26rpx;\r\n        color: #666666;\r\n        border-radius: 12rpx;\r\n        transition: all 0.3s ease;\r\n        \r\n        &.active {\r\n            background: #3b82f6;\r\n            color: white;\r\n            font-weight: 500;\r\n        }\r\n        \r\n        &:not(.active):active {\r\n            background: #f8fafc;\r\n        }\r\n    }\r\n}\r\n\r\n.content {\r\n    padding: 120rpx 24rpx 40rpx; /* 顶部增加padding避免被固定筛选器遮挡 */\r\n}\r\n\r\n.order-list {\r\n    .order-item {\r\n        margin-bottom: 20rpx;\r\n        \r\n        .order-card {\r\n            background: white;\r\n            border-radius: 20rpx;\r\n            padding: 32rpx;\r\n            box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);\r\n            \r\n            .card-header {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                margin-bottom: 16rpx;\r\n                \r\n                .order-info {\r\n                    .order-no {\r\n                        font-size: 20rpx;\r\n                        font-weight: bold;\r\n                        color: #797979;\r\n                    }\r\n                }\r\n                \r\n                .header-right {\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    align-items: flex-end;\r\n                    \r\n                    .status-badge {\r\n                        padding: 6rpx 12rpx;\r\n                        border-radius: 16rpx;\r\n                        font-size: 22rpx;\r\n                        font-weight: 500;\r\n                        \r\n                        &.status-progress {\r\n                            background: #f0fdf4;\r\n                            color: #16a34a;\r\n                            border: 1rpx solid #bbf7d0;\r\n                        }\r\n                        \r\n                        &.status-failed {\r\n                            background: #fef2f2;\r\n                            color: #dc2626;\r\n                            border: 1rpx solid #fecaca;\r\n                        }\r\n                        \r\n                        &.status-cancelled {\r\n                            background: #fef3c7;\r\n                            color: #d97706;\r\n                            border: 1rpx solid #fed7aa;\r\n                        }\r\n                        \r\n                        &.status-paid {\r\n                            background: #f0f9ff;\r\n                            color: #0ea5e9;\r\n                            border: 1rpx solid #bae6fd;\r\n                        }\r\n                        \r\n                        &.status-unknown {\r\n                            background: #f9fafb;\r\n                            color: #6b7280;\r\n                            border: 1rpx solid #e5e7eb;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            .card-body {\r\n                .info-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    margin-bottom: 16rpx;\r\n                    \r\n                    .info-label {\r\n                        font-size: 26rpx;\r\n                        color: #666666;\r\n                        margin-right: 16rpx;\r\n                        min-width: 120rpx;\r\n                        \r\n                        &.plate-label {\r\n                            margin-left: 40rpx;\r\n                            min-width: 80rpx;\r\n                        }\r\n                    }\r\n                    \r\n                    .info-value {\r\n                        font-size: 26rpx;\r\n                        color: #333333;\r\n                        flex: 1;\r\n                        \r\n                        &.time {\r\n                            color: #333333;\r\n                        }\r\n                    }\r\n                }\r\n                \r\n                .card-bottom {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding-top: 10rpx;\r\n                    border-top: 1rpx solid #f5f5f5;\r\n                    \r\n                    .price-info {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        \r\n                        .price-item {\r\n                            display: flex;\r\n                            align-items: center;\r\n                            margin-right: 20rpx;\r\n                            \r\n                            .price-label {\r\n                                font-size: 22rpx;\r\n                                color: #666666;\r\n                                margin-right: 8rpx;\r\n                            }\r\n                            \r\n                            .price-value {\r\n                                font-size: 26rpx;\r\n                                font-weight: bold;\r\n                                \r\n                                &.discount {\r\n                                    color: #16a34a;\r\n                                }\r\n                                \r\n                                &.actual {\r\n                                    color: #ff0000;\r\n                                    font-size: 30rpx;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    .invoice-actions {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        \r\n                        .invoice-btn {\r\n                            display: flex;\r\n                            align-items: center;\r\n                            padding: 12rpx 16rpx;\r\n                            border-radius: 8rpx;\r\n                            font-size: 24rpx;\r\n                            font-weight: 500;\r\n                            margin-left: 10rpx;\r\n                            \r\n                            text {\r\n                                margin-left: 6rpx;\r\n                            }\r\n                            \r\n                            &.send-btn {\r\n                                background: #f0f9ff;\r\n                                color: #3b82f6;\r\n                                border: 1rpx solid #bae6fd;\r\n                            }\r\n                            \r\n                            &.open-btn {\r\n                                background: #3b82f6;\r\n                                color: #ffffff;\r\n                            }\r\n                            \r\n                            &.reopen-btn {\r\n                                background: #f59e0b;\r\n                                color: #ffffff;\r\n                            }\r\n                            \r\n                            &.pay-btn {\r\n                                background: #16a34a;\r\n                                color: #ffffff;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 160rpx 40rpx;\r\n    margin-top: 60rpx;\r\n    \r\n    .empty-text {\r\n        font-size: 28rpx;\r\n        color: #64748b;\r\n        font-weight: 500;\r\n        margin-top: 32rpx;\r\n    }\r\n}\r\n\r\n.popup-cell {\r\n    width: 600rpx;\r\n    text-align: center;\r\n    padding: 44rpx 0;\r\n    \r\n    .email {\r\n        padding: 32rpx;\r\n        background: #ffffff;\r\n        border-radius: 20rpx;\r\n        \r\n        .content_item {\r\n            margin-bottom: 20rpx;\r\n            \r\n            .email-title {\r\n                font-size: 28rpx;\r\n                color: #212121;\r\n                font-weight: 500;\r\n                margin-bottom: 16rpx;\r\n                text-align: left;\r\n            }\r\n        }\r\n        \r\n        .desc {\r\n            font-size: 24rpx;\r\n            color: #f5820e;\r\n            line-height: 1.5;\r\n            margin-top: 16rpx;\r\n        }\r\n    }\r\n    \r\n    .choose_btn {\r\n        display: flex;\r\n        justify-content: space-around;\r\n        padding: 0 20rpx;\r\n        margin-top: 32rpx;\r\n        \r\n        .cancel_btn, .sure_btn {\r\n            width: 240rpx;\r\n            height: 80rpx;\r\n            line-height: 80rpx;\r\n            border-radius: 80rpx;\r\n            text-align: center;\r\n            font-size: 28rpx;\r\n            font-weight: 600;\r\n        }\r\n        \r\n        .cancel_btn {\r\n            background: #ffffff;\r\n            border: 2rpx solid #246bfd;\r\n            color: #246bfd;\r\n        }\r\n        \r\n        .sure_btn {\r\n            background: #246bfd;\r\n            color: #ffffff;\r\n        }\r\n    }\r\n}\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/parkingOrder/parkingOrder.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShow", "uni", "getParkingOrderList", "postInvoiceSend"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAoKA,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,eAAeA,cAAG,IAAC,CAAC;AAC1B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,YAAYA,cAAG,IAAC,IAAI;AAC1B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAG1B,UAAM,mBAAmB;AAAA,MACrB,OAAO;AAAA,IACX;AAEA,UAAM,kBAAkB;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,IAClB;AAEAC,kBAAAA,OAAO,MAAM;AACT,mBAAa,CAAC;AAAA,IAClB,CAAC;AAGD,UAAM,YAAY,OAAO,cAAc;AACnC,UAAI,aAAa,UAAU;AAAW;AAEtC,mBAAa,QAAQ;AACrB,YAAM,aAAa,SAAS;AAAA,IAChC;AAGA,UAAM,eAAe,OAAO,cAAc;AACtC,UAAI;AACAC,sBAAAA,MAAI,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAGD,cAAM,SAAS,CAAE;AACjB,YAAI,cAAc,MAAM;AACpB,iBAAO,YAAY;AAAA,QACtB;AAED,cAAM,MAAM,MAAMC,iBAAmB,oBAAC,MAAM;AAC5CD,sBAAAA,MAAY,MAAA,OAAA,8CAAA,WAAW,GAAG;AAC1B,kBAAU,QAAQ,IAAI,QAAQ,CAAE;AAAA,MAEnC,SAAQ,OAAO;AACZA,sBAAAA,MAAc,MAAA,SAAA,8CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACT,UAAc;AACNA,sBAAAA,MAAI,YAAa;AAAA,MACpB;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,YAAY;AAChC,UAAI,CAAC;AAAS,eAAO;AAErB,YAAM,QAAQ,KAAK,MAAM,UAAU,EAAE;AACrC,YAAM,OAAO,UAAU;AAEvB,UAAI,QAAQ,GAAG;AACX,eAAO,OAAO,IAAI,GAAG,KAAK,KAAK,IAAI,OAAO,GAAG,KAAK;AAAA,MAC1D,OAAW;AACH,eAAO,GAAG,IAAI;AAAA,MACjB;AAAA,IACL;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAC9B,cAAQ,QAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACd;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,WAAW;AAC/B,cAAQ,QAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACd;AAAA,IACL;AAGA,UAAM,UAAU,CAAC,SAAS;AAEtBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,gDAAgD,KAAK,OAAO,gBAAgB,KAAK,WAAW;AAAA,MACzG,CAAK;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAE7B,UAAI,KAAK,qBAAqB,KAAK,kBAAkB,IAAI;AAErD,YAAI,KAAK,kBAAkB,UAAU;AACjCA,wBAAAA,MAAI,WAAW;AAAA,YACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa,6BAA6B,KAAK,kBAAkB,EAAE,aAAa,KAAK,kBAAkB,QAAQ;AAAA,UACnM,CAAa;AAAA,QACb,OAAe;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa,6BAA6B,KAAK,kBAAkB,EAAE;AAAA,UACvJ,CAAa;AAAA,QACJ;AAAA,MACT,OAAW;AACHA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,yCAAyC,KAAK,EAAE,UAAU,KAAK,aAAa;AAAA,QAC7F,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,cAAc,CAAC,OAAO;AACxB,gBAAU,QAAQ;AAClB,gBAAU,QAAQ;AAAA,IACtB;AAGA,UAAM,wBAAwB,YAAY;AACtC,UAAI,CAAC,YAAY,OAAO;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAED,UAAI;AACA,cAAM,SAAS;AAAA,UACX,IAAI,UAAU;AAAA,UACd,aAAa,YAAY;AAAA,QAC5B;AAED,cAAME,YAAAA,gBAAgB,MAAM;AAE5BF,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACtB,CAAS;AAED,mBAAW,MAAM;AACb,oBAAU,QAAQ;AAClB,sBAAY,QAAQ;AAAA,QACvB,GAAE,GAAI;AAAA,MACV,SAAQ,OAAO;AACZA,sBAAAA,MAAA,MAAA,SAAA,8CAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9UA,GAAG,WAAW,eAAe;"}