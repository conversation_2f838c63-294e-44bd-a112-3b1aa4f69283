{"version": 3, "file": "payPlateDetail.js", "sources": ["pages/payPlateDetail/payPlateDetail.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF5UGxhdGVEZXRhaWwvcGF5UGxhdGVEZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"pay-plate-detail\">\r\n        <view class=\"cell\">\r\n            <view class=\"top-cell\">\r\n                <view class=\"top-cell-title\">\r\n                    <view class=\"title\"> 停车信息确认 </view>\r\n                    <view class=\"desc\"> Information confirmed </view>\r\n                </view>\r\n                <image src=\"/static/image/carRight.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"form_cell\">\r\n                <view class=\"form_cell_top\">\r\n                    <view class=\"form_cell_top_plateNo\">\r\n                        <image src=\"/static/order/car.png\" mode=\"aspectFit\"></image>\r\n                        <text class=\"tips\">{{ plateNo }}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/changku.png\" mode=\"aspectFit\"></image>\r\n                    场库信息：<text class=\"tips\">{{ orderInfo.warehouseName || '--' }}</text>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>\r\n                    开始时间：<text class=\"tips\">{{ orderInfo.beginParkingTime || '--' }}</text>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>\r\n                    结束时间：<text class=\"tips\">{{ orderInfo.endParkingTime || '--' }}</text>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/kaitong.png\" mode=\"aspectFit\"></image>\r\n                    停车时长：<text class=\"tips\">{{ orderInfo.parkingDuration || '--' }} 分钟</text>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/shifu.png\" mode=\"aspectFit\"></image>\r\n                    支付状态：<text class=\"tips\">{{ chenkPayStatus(orderInfo.payStatus)}}</text>\r\n                </view>\r\n                <view class=\"form_cell_word\">\r\n                    <image src=\"/static/package/taocanprice.png\" mode=\"aspectFit\"></image>\r\n                    订单金额：<text class=\"tips\">{{ orderInfo.paymentAmount || '--' }}元</text>\r\n                </view>\r\n                <view class=\"form_cell_bottom\">\r\n                    <text class=\"word\">实付金额：</text>\r\n                    <text class=\"number\">\r\n                        <text class=\"tips\"> ￥ </text>\r\n                        {{ orderInfo.actualPayment || '--' }}</text>\r\n                </view>\r\n            </view>\r\n            <view>\r\n                <button class=\"submit-btn\" @tap=\"handleCreateParkingOrder\"\r\n                    :disabled=\"orderInfo.payStatus === 5 || orderInfo.paymentAmount === 0 || !orderInfo.paymentAmount\">\r\n                    立即支付\r\n                </button>\r\n                <view class=\"payment-notice\">\r\n                    当前页面缴费后，15分钟内出场，不再计费，否则重新计费\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport { getParkingOrderDetail, createParkingOrder, payParkingOrderCallBack } from '@/api/parkingOrder.js'\r\n\r\n// 响应式数据\r\nconst orderInfo = ref({})\r\nconst plateNo = ref('')\r\nconst warehouseId = ref(0)\r\n// 页面加载时查询订单详情\r\nonLoad((options) => {\r\n    plateNo.value = options.plateNo\r\n    warehouseId.value = options.warehouseId\r\n    fetchParkingOrderDetail()\r\n})\r\n\r\n// 查询订单详情\r\nconst fetchParkingOrderDetail = () => {\r\n    uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n    })\r\n    if (!plateNo.value || !warehouseId.value) {\r\n        return\r\n    }\r\n    let params = {\r\n        plateNo: plateNo.value,\r\n        warehouseId: warehouseId.value\r\n    }\r\n    getParkingOrderDetail(params).then(res => {\r\n        orderInfo.value = res.data\r\n        uni.hideLoading()\r\n    })\r\n}\r\n\r\n// 支付状态\r\nconst chenkPayStatus = (status) => {\r\n    switch (status) {\r\n        case 1:\r\n            return '进行中'\r\n        // case 2:\r\n        //     return '支付中'\r\n        case 5:\r\n            return '已支付'\r\n        default:\r\n            return '--'\r\n    }\r\n}\r\n\r\n// 创建停车订单\r\nconst handleCreateParkingOrder = () => {\r\n    uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n    })\r\n    createParkingOrder(orderInfo.value).then(res => {\r\n        console.log('创建停车订单 res: ', res)\r\n        if (res.data.needPay) {\r\n            uni.requestPayment({\r\n                timeStamp: res.data.timeStamp,\r\n                nonceStr: res.data.nonceStr,\r\n                package: res.data.package,\r\n                signType: res.data.signType,\r\n                paySign: res.data.paySign,\r\n                success: function (result) {\r\n                    // 延迟一下显示toast，避免与complete中的hideLoading冲突\r\n                    uni.hideLoading()\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付成功~',\r\n                            icon: 'none',\r\n                            duration: 2000\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                    // let params = {\r\n                    //     tradeId: res.data.tradeId\r\n                    // }\r\n                    // payParkingOrderCallBack(params).then(item => {\r\n                    //     console.log('停车订单 支付成功回调 item: ', item)\r\n                    // })\r\n                },\r\n                fail: function (err) {\r\n                    uni.hideLoading()\r\n                    console.log('支付失败的回调：', err)\r\n                    // 延迟一下显示toast，避免与hideLoading冲突\r\n                    setTimeout(() => {\r\n                        uni.showToast({\r\n                            title: '支付失败',\r\n                            icon: 'none',\r\n                            duration: 1500\r\n                        })\r\n                    }, 100)\r\n                    setTimeout(() => {\r\n                        uni.navigateBack()\r\n                    }, 2000)\r\n                },\r\n                complete: function (res) {\r\n                    uni.hideLoading()\r\n                }\r\n            })\r\n        }\r\n    })\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.submit-btn {\r\n    margin-top: 40rpx;\r\n    width: 100%;\r\n    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);;\r\n    border-radius: 44rpx;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    border: none;\r\n\r\n    &[disabled] {\r\n        background: #cccccc;\r\n        color: #999999;\r\n    }\r\n}\r\n\r\n.payment-notice {\r\n    margin-top: 20rpx;\r\n    text-align: center;\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n    line-height: 1.4;\r\n}\r\n\r\n.pay-plate-detail {\r\n    height: 100vh;\r\n    background-color: #f5f5f5;\r\n\r\n    .cell {\r\n        padding: 40rpx 32rpx 0;\r\n\r\n        .top-cell {\r\n            margin-bottom: 20rpx;\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .top-cell-title {\r\n                margin-right: 8rpx;\r\n\r\n                .title {\r\n                    font-size: 40rpx;\r\n                    font-weight: bold;\r\n                    color: #212121;\r\n                    margin-bottom: 8rpx;\r\n                }\r\n\r\n                .desc {\r\n                    font-size: 28rpx;\r\n                    font-weight: 400;\r\n                    color: #9e9e9e;\r\n                }\r\n            }\r\n\r\n            image {\r\n                width: 284rpx;\r\n                height: 200rpx;\r\n            }\r\n        }\r\n\r\n        .form_cell {\r\n            padding: 32rpx;\r\n            border-radius: 20rpx;\r\n            background-color: #fff;\r\n\r\n            .form_cell_top {\r\n                margin-bottom: 32rpx;\r\n\r\n                .form_cell_top_plateNo {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    font-size: 36rpx;\r\n                    font-weight: bold;\r\n                    color: #212121;\r\n\r\n                    image {\r\n                        width: 48rpx;\r\n                        height: 48rpx;\r\n                        margin-right: 12rpx;\r\n                    }\r\n                }\r\n\r\n                .form_cell_top_edit {\r\n                    padding: 4rpx 12rpx;\r\n                    background: #e9f0ff;\r\n                    border-radius: 4rpx;\r\n                    font-size: 20rpx;\r\n                    font-weight: 400;\r\n                    color: #246bfd;\r\n\r\n                    image {\r\n                        width: 32rpx;\r\n                        height: 32rpx;\r\n                        margin-right: 8rpx;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .form_cell_word {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 28rpx;\r\n                font-weight: 400;\r\n                color: #9e9e9e;\r\n                margin-bottom: 32rpx;\r\n\r\n                image {\r\n                    width: 36rpx;\r\n                    height: 36rpx;\r\n                    margin-right: 12rpx;\r\n                }\r\n\r\n                .tips {\r\n                    font-size: 28rpx;\r\n                    font-weight: 400;\r\n                    color: #616161;\r\n                }\r\n            }\r\n\r\n            .dikou {\r\n                .unit {\r\n                    font-size: 24rpx;\r\n                    margin-right: 8rpx;\r\n                    color: #ff922e;\r\n                    padding-top: 8rpx;\r\n                }\r\n\r\n                .desc {\r\n                    font-size: 28rpx;\r\n                    margin-right: 20rpx;\r\n                    font-weight: 400;\r\n                    color: #ff922e;\r\n                }\r\n\r\n                .price {\r\n                    color: #ff922e;\r\n                    font-size: 36rpx;\r\n                    font-weight: Bold;\r\n                }\r\n\r\n                image {\r\n                    width: 28rpx;\r\n                    height: 28rpx;\r\n                    margin-left: 10rpx;\r\n                }\r\n            }\r\n\r\n            .form_cell_bottom {\r\n                border-top: 2rpx solid rgba(189, 189, 189, 0.2);\r\n                padding-top: 16rpx;\r\n                text-align: right;\r\n\r\n                .word {\r\n                    font-size: 28rpx;\r\n                    font-weight: bold;\r\n                    color: #212121;\r\n                }\r\n\r\n                .number {\r\n                    font-size: 48rpx;\r\n                    font-weight: bold;\r\n                    color: #f90355;\r\n\r\n                    .tips {\r\n                        font-size: 28rpx;\r\n                        font-weight: bold;\r\n                        color: #f90355;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/payPlateDetail/payPlateDetail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getParkingOrderDetail", "createParkingOrder", "res"], "mappings": ";;;;;;;AAkEA,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,cAAcA,cAAG,IAAC,CAAC;AAEzBC,kBAAM,OAAC,CAAC,YAAY;AAChB,cAAQ,QAAQ,QAAQ;AACxB,kBAAY,QAAQ,QAAQ;AAC5B,8BAAyB;AAAA,IAC7B,CAAC;AAGD,UAAM,0BAA0B,MAAM;AAClCC,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AACD,UAAI,CAAC,QAAQ,SAAS,CAAC,YAAY,OAAO;AACtC;AAAA,MACH;AACD,UAAI,SAAS;AAAA,QACT,SAAS,QAAQ;AAAA,QACjB,aAAa,YAAY;AAAA,MAC5B;AACDC,uBAAAA,sBAAsB,MAAM,EAAE,KAAK,SAAO;AACtC,kBAAU,QAAQ,IAAI;AACtBD,sBAAAA,MAAI,YAAa;AAAA,MACzB,CAAK;AAAA,IACL;AAGA,UAAM,iBAAiB,CAAC,WAAW;AAC/B,cAAQ,QAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,QAGX,KAAK;AACD,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACd;AAAA,IACL;AAGA,UAAM,2BAA2B,MAAM;AACnCA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AACDE,uBAAAA,mBAAmB,UAAU,KAAK,EAAE,KAAK,SAAO;AAC5CF,sBAAAA,MAAA,MAAA,OAAA,kDAAY,gBAAgB,GAAG;AAC/B,YAAI,IAAI,KAAK,SAAS;AAClBA,wBAAAA,MAAI,eAAe;AAAA,YACf,WAAW,IAAI,KAAK;AAAA,YACpB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,UAAU,IAAI,KAAK;AAAA,YACnB,SAAS,IAAI,KAAK;AAAA,YAClB,SAAS,SAAU,QAAQ;AAEvBA,4BAAAA,MAAI,YAAa;AACjB,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YAOV;AAAA,YACD,MAAM,SAAU,KAAK;AACjBA,4BAAAA,MAAI,YAAa;AACjBA,4BAAAA,MAAA,MAAA,OAAA,kDAAY,YAAY,GAAG;AAE3B,yBAAW,MAAM;AACbA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtC,CAAyB;AAAA,cACJ,GAAE,GAAG;AACN,yBAAW,MAAM;AACbA,8BAAAA,MAAI,aAAc;AAAA,cACrB,GAAE,GAAI;AAAA,YACV;AAAA,YACD,UAAU,SAAUG,MAAK;AACrBH,4BAAAA,MAAI,YAAa;AAAA,YACpB;AAAA,UACjB,CAAa;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpKA,GAAG,WAAW,eAAe;"}