{"version": 3, "file": "payPlateQuery.js", "sources": ["pages/payPlateQuery/payPlateQuery.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF5UGxhdGVRdWVyeS9wYXlQbGF0ZVF1ZXJ5LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"plate-query-container\">\r\n        <view class=\"cell\">\r\n            <view class=\"top-cell u-flex u-flex-y-center\">\r\n                <view class=\"top-cell-title\">\r\n                    <view class=\"title\"> 停车缴费 </view>\r\n                    <view class=\"desc\"> Parking payment </view>\r\n                </view>\r\n                <image src=\"/static/image/carRight.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"form-cell\">\r\n                <!-- 场库选择器 -->\r\n                <view class=\"warehouse-selector-section\">\r\n                    <view class=\"form-cell-title\">当前场库</view>\r\n                    <view class=\"warehouse-selector\" @tap=\"showWarehouseSelector\">\r\n                        <text class=\"warehouse-name\">{{ currentWarehouse.name || '请选择场库' }}</text>\r\n                        <u-icon name=\"arrow-down\" size=\"14\" color=\"#999\"></u-icon>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"form-cell-title\"> 车牌号 </view>\r\n                <view class=\"so-plate-body\" @tap=\"plateShow = true\">\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(0, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(1, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-dot\"></view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(2, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(3, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(4, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(5, 1) }}</text>\r\n                    </view>\r\n                    <view class=\"so-plate-word\">\r\n                        <text>{{ plateNo.substr(6, 1) }}</text>\r\n                    </view>\r\n                    <template v-if=\"PageCur == '2'\">\r\n                        <view class=\"so-plate-word\">\r\n                            <text>{{ plateNo.substr(7, 1) }}</text>\r\n                        </view>\r\n                    </template>\r\n                </view>\r\n                <view class=\"form-cell-title u-flex u-flex-y-center u-flex-between\">\r\n                    历史纪录\r\n                    <u-icon name=\"trash\" size=\"24\" color=\"#999\" @click=\"deleteHistory\"></u-icon>\r\n                </view>\r\n                <template v-if=\"plateNoHistoryList.length > 0\">\r\n                    <u-grid :border=\"false\" col=\"3\">\r\n                        <u-grid-item v-for=\"item in plateNoHistoryList\" :key=\"item\" @click=\"handleChoosePlateNo(item)\">\r\n                            <view class=\"center_cell_top_block\">\r\n                                <view class=\"package_name\"> {{ item }} </view>\r\n                            </view>\r\n                        </u-grid-item>\r\n                    </u-grid>\r\n                </template>\r\n                <template v-else>\r\n                    <view>\r\n                        <u-empty text=\"暂无历史记录\" mode=\"history\" iconSize=\"50\"></u-empty>\r\n                    </view>\r\n                </template>\r\n            </view>\r\n        </view>\r\n\r\n        <view>\r\n            <button @tap=\"handleRoutePage\" class=\"search-btn\">下一步</button>\r\n        </view>\r\n\r\n        <!-- 自定义键盘组件 -->\r\n        <plate-input @typeChange=\"typeChange\" v-if=\"plateShow\" :plate=\"plateNo\" @export=\"setPlate\"\r\n            @close=\"plateShow = false & close()\" />\r\n\r\n        <!-- 场库选择器组件 -->\r\n        <WarehouseSelector \r\n            :show=\"showSelector\" \r\n            :warehouseList=\"wareHouseList\" \r\n            :currentWarehouse=\"currentWarehouse\"\r\n            :windowHeightHalf=\"400\"\r\n            @close=\"closeWarehouseSelector\" \r\n            @select=\"selectWarehouse\" \r\n        />\r\n    </view>\r\n</template>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nimport {onShow } from '@dcloudio/uni-app'\r\nimport plateInput from '@/components/uni-plate-input/uni-plate-input.vue'\r\nimport WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue'\r\nimport { getParkWareHouseList } from '@/api/warehouse'\r\n\r\n// 响应式数据\r\nconst plateNo = ref('')\r\nconst plateShow = ref(false)\r\nconst PageCur = ref(1)\r\nconst isEdit = ref(false)\r\nconst plateNoHistoryList = ref([])\r\n\r\n// 场库选择器相关数据\r\nconst wareHouseList = ref([])\r\nconst showSelector = ref(false)\r\nconst currentWarehouse = ref({ id: 0, name: \"选择场库\" })\r\n\r\n// 监听车牌号变化\r\nwatch(plateNo, (newVal) => {\r\n    if (newVal.length === 7) {\r\n        PageCur.value = 1\r\n    }\r\n    if (newVal.length === 8) {\r\n        PageCur.value = 2\r\n    }\r\n})\r\n\r\n// 页面显示时\r\nonShow(() => {\r\n    plateNoHistoryList.value = uni.getStorageSync('plateNoHistoryList') || []\r\n    initWarehouseData()\r\n})\r\n\r\n// 方法\r\nconst setPlate = (plate) => {\r\n    if (plate.length >= 7) plateNo.value = plate\r\n    plateShow.value = false\r\n}\r\n\r\nconst typeChange = (e) => {\r\n    PageCur.value = e\r\n    plateNo.value = ''\r\n}\r\n\r\nconst close = () => {\r\n    if (!isEdit.value) {\r\n        PageCur.value = 1\r\n    }\r\n}\r\n\r\nconst handleChoosePlateNo = (plateNoValue) => {\r\n    plateNo.value = plateNoValue\r\n}\r\n\r\n// 初始化场库数据\r\nconst initWarehouseData = async () => {\r\n    try {\r\n        const res = await getParkWareHouseList()\r\n        wareHouseList.value = res.data.map(item => ({\r\n            id: item.id,\r\n            name: item.warehouseName,\r\n            latitude: item.latitude,\r\n            longitude: item.longitude\r\n        }))\r\n\r\n        // 先从缓存中查找场库信息\r\n        const cachedWarehouse = uni.getStorageSync('currentWarehouse')\r\n        if (cachedWarehouse && wareHouseList.value.some(w => w.id === cachedWarehouse.id)) {\r\n            currentWarehouse.value = cachedWarehouse\r\n        } else if (wareHouseList.value.length > 0) {\r\n            // 如果缓存中没有，使用场库列表的第一项\r\n            currentWarehouse.value = wareHouseList.value[0]\r\n            uni.setStorageSync('currentWarehouse', currentWarehouse.value)\r\n        }\r\n    } catch (error) {\r\n        uni.showToast({\r\n            title: '场库数据加载失败',\r\n            icon: 'none'\r\n        })\r\n    }\r\n}\r\n\r\n// 显示场库选择器\r\nconst showWarehouseSelector = () => {\r\n    showSelector.value = true\r\n}\r\n\r\n// 关闭场库选择器\r\nconst closeWarehouseSelector = () => {\r\n    showSelector.value = false\r\n}\r\n\r\n// 选择场库\r\nconst selectWarehouse = (warehouse) => {\r\n    currentWarehouse.value = warehouse\r\n    uni.setStorageSync('currentWarehouse', currentWarehouse.value)\r\n    closeWarehouseSelector()\r\n}\r\n\r\nconst handleRoutePage = () => {\r\n    if (!plateNo.value) {\r\n        return uni.showToast({\r\n            title: '请选择车牌~',\r\n            duration: 2000,\r\n            icon: 'none'\r\n        })\r\n    }\r\n    \r\n    // 检查是否选择了场库\r\n    if (!currentWarehouse.value || !currentWarehouse.value.id || currentWarehouse.value.id === 0) {\r\n        return uni.showToast({\r\n            title: '请选择场库~',\r\n            duration: 2000,\r\n            icon: 'none'\r\n        })\r\n    }\r\n    \r\n    let flag = true\r\n    plateNoHistoryList.value.forEach(item => {\r\n        if (plateNo.value === item) {\r\n            flag = false\r\n        }\r\n    })\r\n    if (flag) {\r\n        // 最多只显示六条历史记录\r\n        if (plateNoHistoryList.value.length === 6) {\r\n            plateNoHistoryList.value.pop()\r\n        }\r\n        plateNoHistoryList.value.unshift(plateNo.value)\r\n        uni.setStorageSync('plateNoHistoryList', plateNoHistoryList.value)\r\n    }\r\n    uni.navigateTo({\r\n        url: '/pages/payPlateDetail/payPlateDetail?plateNo=' + plateNo.value + '&warehouseId=' + currentWarehouse.value.id\r\n    })\r\n}\r\n\r\nconst deleteHistory = () => {\r\n    uni.showModal({\r\n        title: '提示',\r\n        content: '确认要清空历史记录吗?',\r\n        success(res) {\r\n            if (res.confirm) {\r\n                uni.removeStorageSync('plateNoHistoryList')\r\n                setTimeout(() => {\r\n                    plateNoHistoryList.value = uni.getStorageSync('plateNoHistoryList') || []\r\n                }, 300)\r\n            }\r\n        }\r\n    })\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.plate-query-container {\r\n    background-color: #f5f5f5;\r\n    height: 100vh;\r\n}\r\n\r\n.warehouse-selector-section {\r\n    margin-bottom: 20rpx;\r\n}\r\n\r\n.warehouse-selector {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 25rpx 0;\r\n}\r\n\r\n.warehouse-name {\r\n    font-size: 32rpx;\r\n    color: #333;\r\n    flex: 1;\r\n}\r\n\r\n.cell {\r\n    padding: 40rpx 32rpx 0;\r\n\r\n    .top-cell {\r\n        margin-bottom: 20rpx;\r\n\r\n        .top-cell-title {\r\n            margin-right: 8rpx;\r\n\r\n            .title {\r\n                font-size: 40rpx;\r\n                font-weight: bold;\r\n                color: #212121;\r\n                margin-bottom: 8rpx;\r\n            }\r\n\r\n            .desc {\r\n                font-size: 28rpx;\r\n                font-weight: 400;\r\n                color: #9e9e9e;\r\n            }\r\n        }\r\n\r\n        image {\r\n            width: 284rpx;\r\n            height: 200rpx;\r\n        }\r\n    }\r\n\r\n    .form-cell {\r\n        padding: 32rpx;\r\n        border-radius: 20rpx;\r\n        background-color: #fff;\r\n\r\n        .form-cell-title {\r\n            font-size: 32rpx;\r\n            font-weight: bold;\r\n            color: #000000;\r\n            margin-bottom: 20rpx;\r\n\r\n            image {\r\n                width: 36rpx;\r\n                height: 36rpx;\r\n            }\r\n        }\r\n\r\n        .center_cell_top_block {\r\n            background: #f3f5f7;\r\n            border-radius: 200rpx;\r\n            padding: 16rpx 0 18rpx;\r\n            font-size: 28rpx;\r\n            font-weight: 400;\r\n            color: #9e9e9e;\r\n            text-align: center;\r\n            word-break: keep-all;\r\n            margin-bottom: 20rpx;\r\n            width: 95%;\r\n        }\r\n    }\r\n}\r\n\r\n.so-plate-body {\r\n    box-sizing: border-box;\r\n    display: -webkit-box;\r\n    display: flex;\r\n    -webkit-box-pack: justify;\r\n    justify-content: space-between;\r\n    -webkit-box-align: center;\r\n    align-items: center;\r\n    margin-bottom: 40rpx;\r\n}\r\n\r\n.so-plate-word {\r\n    border: 1rpx solid #246bfd;\r\n    border-radius: 10upx;\r\n    height: 88rpx;\r\n    margin: 0 5upx;\r\n    box-sizing: border-box;\r\n    width: 71rpx;\r\n    position: relative;\r\n}\r\n\r\n.so-plate-word text {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    -webkit-transform: translateX(-50%) translateY(-50%);\r\n    transform: translateX(-50%) translateY(-50%);\r\n    font-weight: 700;\r\n    font-size: 32upx;\r\n}\r\n\r\n.so-plate-dot {\r\n    width: 15rpx;\r\n    height: 15rpx;\r\n    background: #246bfd;\r\n    border-radius: 50%;\r\n    margin: 0 5rpx;\r\n}\r\n\r\n.search-btn {\r\n    margin-top: 40rpx;\r\n    width: 85%;\r\n    // background-color: #4986ff;\r\n    // background: linear-gradient(135deg, #667eea 0%, #8d66b4 100%);\r\n    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);\r\n    border-radius: 44rpx;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/payPlateQuery/payPlateQuery.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "watch", "onShow", "uni", "getParkWareHouseList"], "mappings": ";;;;;;;;;;;;;;;;;;AA6FA,MAAM,aAAa,MAAW;AAC9B,MAAM,oBAAoB,MAAW;;;;AAIrC,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,UAAUA,cAAG,IAAC,CAAC;AACrB,UAAM,SAASA,cAAG,IAAC,KAAK;AACxB,UAAM,qBAAqBA,cAAG,IAAC,EAAE;AAGjC,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,eAAeA,cAAG,IAAC,KAAK;AAC9B,UAAM,mBAAmBA,cAAG,IAAC,EAAE,IAAI,GAAG,MAAM,QAAQ;AAGpDC,kBAAAA,MAAM,SAAS,CAAC,WAAW;AACvB,UAAI,OAAO,WAAW,GAAG;AACrB,gBAAQ,QAAQ;AAAA,MACnB;AACD,UAAI,OAAO,WAAW,GAAG;AACrB,gBAAQ,QAAQ;AAAA,MACnB;AAAA,IACL,CAAC;AAGDC,kBAAAA,OAAO,MAAM;AACT,yBAAmB,QAAQC,cAAAA,MAAI,eAAe,oBAAoB,KAAK,CAAE;AACzE,wBAAmB;AAAA,IACvB,CAAC;AAGD,UAAM,WAAW,CAAC,UAAU;AACxB,UAAI,MAAM,UAAU;AAAG,gBAAQ,QAAQ;AACvC,gBAAU,QAAQ;AAAA,IACtB;AAEA,UAAM,aAAa,CAAC,MAAM;AACtB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AAAA,IACpB;AAEA,UAAM,QAAQ,MAAM;AAChB,UAAI,CAAC,OAAO,OAAO;AACf,gBAAQ,QAAQ;AAAA,MACnB;AAAA,IACL;AAEA,UAAM,sBAAsB,CAAC,iBAAiB;AAC1C,cAAQ,QAAQ;AAAA,IACpB;AAGA,UAAM,oBAAoB,YAAY;AAClC,UAAI;AACA,cAAM,MAAM,MAAMC,mCAAsB;AACxC,sBAAc,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,UACxC,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,QAC5B,EAAU;AAGF,cAAM,kBAAkBD,cAAAA,MAAI,eAAe,kBAAkB;AAC7D,YAAI,mBAAmB,cAAc,MAAM,KAAK,OAAK,EAAE,OAAO,gBAAgB,EAAE,GAAG;AAC/E,2BAAiB,QAAQ;AAAA,QAC5B,WAAU,cAAc,MAAM,SAAS,GAAG;AAEvC,2BAAiB,QAAQ,cAAc,MAAM,CAAC;AAC9CA,wBAAAA,MAAI,eAAe,oBAAoB,iBAAiB,KAAK;AAAA,QAChE;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAAA,IACL;AAGA,UAAM,wBAAwB,MAAM;AAChC,mBAAa,QAAQ;AAAA,IACzB;AAGA,UAAM,yBAAyB,MAAM;AACjC,mBAAa,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,CAAC,cAAc;AACnC,uBAAiB,QAAQ;AACzBA,oBAAAA,MAAI,eAAe,oBAAoB,iBAAiB,KAAK;AAC7D,6BAAwB;AAAA,IAC5B;AAEA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,CAAC,QAAQ,OAAO;AAChB,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAGD,UAAI,CAAC,iBAAiB,SAAS,CAAC,iBAAiB,MAAM,MAAM,iBAAiB,MAAM,OAAO,GAAG;AAC1F,eAAOA,cAAAA,MAAI,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,QAClB,CAAS;AAAA,MACJ;AAED,UAAI,OAAO;AACX,yBAAmB,MAAM,QAAQ,UAAQ;AACrC,YAAI,QAAQ,UAAU,MAAM;AACxB,iBAAO;AAAA,QACV;AAAA,MACT,CAAK;AACD,UAAI,MAAM;AAEN,YAAI,mBAAmB,MAAM,WAAW,GAAG;AACvC,6BAAmB,MAAM,IAAK;AAAA,QACjC;AACD,2BAAmB,MAAM,QAAQ,QAAQ,KAAK;AAC9CA,sBAAAA,MAAI,eAAe,sBAAsB,mBAAmB,KAAK;AAAA,MACpE;AACDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,kDAAkD,QAAQ,QAAQ,kBAAkB,iBAAiB,MAAM;AAAA,MACxH,CAAK;AAAA,IACL;AAEA,UAAM,gBAAgB,MAAM;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ,KAAK;AACT,cAAI,IAAI,SAAS;AACbA,0BAAG,MAAC,kBAAkB,oBAAoB;AAC1C,uBAAW,MAAM;AACb,iCAAmB,QAAQA,cAAAA,MAAI,eAAe,oBAAoB,KAAK,CAAE;AAAA,YAC5E,GAAE,GAAG;AAAA,UACT;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChPA,GAAG,WAAW,eAAe;"}