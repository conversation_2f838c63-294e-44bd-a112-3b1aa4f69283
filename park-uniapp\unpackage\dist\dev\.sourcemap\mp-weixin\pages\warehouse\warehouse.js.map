{"version": 3, "file": "warehouse.js", "sources": ["pages/warehouse/warehouse.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2FyZWhvdXNlL3dhcmVob3VzZS52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"warehouse-container\">\r\n        <!-- 切换标签 - 固定在顶部 -->\r\n        <view class=\"tab-header\">\r\n            <view class=\"tab-item\" :class=\"{ active: currentTab === 'parking' }\" \r\n            @tap=\"switchTab('parking')\">停车场库</view>\r\n            <view class=\"tab-divider\"></view>\r\n            <view class=\"tab-item\" :class=\"{ active: currentTab === 'charging' }\" \r\n            @tap=\"switchTab('charging')\">充电场库\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 场库列表内容 - 可滑动区域 -->\r\n        <view class=\"warehouse-list-container\">\r\n            <scroll-view scroll-y class=\"warehouse-list\" @scrolltolower=\"loadMore\">\r\n                <!-- 加载中状态 -->\r\n                <view v-if=\"isLoading\" class=\"list-content\">\r\n                    <view class=\"loading-container\">\r\n                        <up-loading-page loading=\"true\" loading-text=\"加载中...\"></up-loading-page>\r\n                    </view>\r\n                </view>\r\n                \r\n                <!-- 停车场列表 -->\r\n                <view v-else-if=\"currentTab === 'parking' && parkWareHouseList.length > 0\" class=\"list-content\">\r\n                    <view class=\"warehouse-item\" v-for=\"(item, index) in parkWareHouseList\" :key=\"index\" @tap=\"goToDetail(item.id)\">\r\n                        <!-- 整体布局：图片-信息-操作 -->\r\n                        <view class=\"item-layout\">\r\n                            <!-- 左侧：图片 -->\r\n                            <view class=\"item-image\">\r\n                                <image :src=\"getFirstImage(item.carouselImages)\" mode=\"aspectFill\" class=\"image\"></image>\r\n                            </view>\r\n                            \r\n                            <!-- 中间：场库信息 -->\r\n                            <view class=\"item-info\">\r\n                                <text class=\"name\">{{ item.warehouseName }}</text>\r\n                                <text class=\"time\">00:00 - 23:59</text>\r\n                                <text class=\"address\">{{ item.address || '暂无地址信息' }}</text>\r\n                            </view>\r\n                            \r\n                            <!-- 右侧：距离和咨询按钮 -->\r\n                            <view class=\"item-actions\">\r\n                                <view class=\"distance-info\" \r\n                                      :class=\"[!userLocation || item.distance === undefined ? 'distance-hidden' : '']\"\r\n                                      @tap.stop=\"goToMap(item)\">\r\n                                    <image src=\"/static/image/map_arrow.png\" mode=\"widthFix\" class=\"distance-icon\"></image>\r\n                                    <text class=\"distance-text\">{{ item.distance ? `${item.distance}km` : '' }}</text>\r\n                                </view>\r\n                                <view class=\"consult-btn\" @tap.stop=\"makePhoneCall(item)\" v-if=\"item.managerPhone\">\r\n                                    <up-icon name=\"phone\" size=\"20\" color=\"#ffffff\"></up-icon>\r\n                                    <text class=\"consult-text\">咨询</text>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <view v-else-if=\"currentTab === 'parking' && parkWareHouseList.length === 0\" class=\"list-content\">\r\n                    <up-empty text=\"暂无停车场数据\" mode=\"data\"></up-empty>\r\n                </view>\r\n\r\n                <!-- 充电场列表 -->\r\n                <view v-else class=\"list-content\">\r\n                    <up-empty text=\"暂无充电场数据\" mode=\"data\"></up-empty>\r\n                </view>\r\n            </scroll-view>\r\n        </view>\r\n\r\n        <custom-tab-bar></custom-tab-bar>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport CustomTabBar from \"@/components/custom-tab-bar/index.vue\";\r\nimport { ref } from 'vue';\r\nimport { getParkWareHouseList } from '@/api/warehouse';\r\nimport { onShow } from '@dcloudio/uni-app';\r\n\r\n// 当前选中的标签\r\nconst currentTab = ref('parking');\r\nconst parkWareHouseList = ref([]);\r\nconst userLocation = ref(null);\r\nconst isLoading = ref(true);\r\n\r\nonShow(() => {\r\n    // 如果是首次加载或者还没有数据，则显示加载状态\r\n    if (parkWareHouseList.value.length === 0) {\r\n        isLoading.value = true;\r\n    }\r\n    getUserLocation();\r\n});\r\n\r\n// 获取用户位置\r\nconst getUserLocation = () => {\r\n    uni.getLocation({\r\n        type: 'gcj02',\r\n        success: (res) => {\r\n            const newLocation = {\r\n                latitude: res.latitude,\r\n                longitude: res.longitude\r\n            };\r\n            \r\n            // 检查位置是否有变化\r\n            const locationChanged = !userLocation.value || \r\n                userLocation.value.latitude !== newLocation.latitude || \r\n                userLocation.value.longitude !== newLocation.longitude;\r\n            \r\n            userLocation.value = newLocation;\r\n            \r\n            // 如果没有场库数据或位置发生了变化，重新加载数据\r\n            if (parkWareHouseList.value.length === 0 || locationChanged) {\r\n                initParkWarehouse();\r\n            } else {\r\n                // 位置没变化但需要重新计算距离（可能之前没有位置信息）\r\n                recalculateDistances();\r\n            }\r\n        },\r\n        fail: (err) => {\r\n            console.error('获取位置失败:', err);\r\n            // 清空用户位置\r\n            userLocation.value = null;\r\n            // 获取位置失败时也加载数据，但不显示距离\r\n            if (parkWareHouseList.value.length === 0) {\r\n                initParkWarehouse();\r\n            }\r\n        }\r\n    });\r\n};\r\n\r\nconst initParkWarehouse = () => {\r\n    getParkWareHouseList().then(res => {\r\n        const list = res.data;\r\n        // 计算每个场库的距离\r\n        if (userLocation.value) {\r\n            list.forEach(item => {\r\n                item.distance = calculateDistance(\r\n                    userLocation.value.latitude,\r\n                    userLocation.value.longitude,\r\n                    item.latitude,\r\n                    item.longitude\r\n                );\r\n            });\r\n            \r\n            // 按照距离从近到远排序\r\n            list.sort((a, b) => {\r\n                // 如果没有距离信息的放在最后\r\n                if (a.distance === undefined && b.distance === undefined) return 0;\r\n                if (a.distance === undefined) return 1;\r\n                if (b.distance === undefined) return -1;\r\n                return a.distance - b.distance;\r\n            });\r\n        }\r\n        parkWareHouseList.value = list;\r\n        isLoading.value = false;\r\n    }).catch(err => {\r\n        console.error('加载场库列表失败:', err);\r\n        isLoading.value = false;\r\n    });\r\n};\r\n\r\n// 计算两点之间的距离（km）\r\nconst calculateDistance = (lat1, lng1, lat2, lng2) => {\r\n    const radLat1 = lat1 * Math.PI / 180.0;\r\n    const radLat2 = lat2 * Math.PI / 180.0;\r\n    const a = radLat1 - radLat2;\r\n    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\r\n    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));\r\n    s = s * 6378.137; // 地球半径\r\n    s = Math.round(s * 100) / 100; // 保留两位小数\r\n    return s;\r\n};\r\n\r\n// 重新计算现有场库列表的距离\r\nconst recalculateDistances = () => {\r\n    if (!userLocation.value || parkWareHouseList.value.length === 0) {\r\n        return;\r\n    }\r\n    \r\n    // 重新计算距离\r\n    parkWareHouseList.value.forEach(item => {\r\n        item.distance = calculateDistance(\r\n            userLocation.value.latitude,\r\n            userLocation.value.longitude,\r\n            item.latitude,\r\n            item.longitude\r\n        );\r\n    });\r\n    \r\n    // 重新排序\r\n    parkWareHouseList.value.sort((a, b) => {\r\n        if (a.distance === undefined && b.distance === undefined) return 0;\r\n        if (a.distance === undefined) return 1;\r\n        if (b.distance === undefined) return -1;\r\n        return a.distance - b.distance;\r\n    });\r\n};\r\n\r\n// 获取第一张图片\r\nconst getFirstImage = (carouselImages) => {\r\n    if (!carouselImages) return '/static/image/parkPay.png'; // 默认图片\r\n\r\n    try {\r\n        // 处理字符串格式的图片数据\r\n        if (typeof carouselImages === 'string') {\r\n            // 尝试直接解析为数组\r\n            const parsedArray = JSON.parse(carouselImages);\r\n            if (Array.isArray(parsedArray) && parsedArray.length > 0) {\r\n                // 如果数组第一个元素是字符串（URL），直接返回\r\n                if (typeof parsedArray[0] === 'string') {\r\n                    return parsedArray[0];\r\n                }\r\n                // 如果数组第一个元素还是数组，再解析一层\r\n                if (Array.isArray(parsedArray[0]) && parsedArray[0].length > 0) {\r\n                    return parsedArray[0][0];\r\n                }\r\n            }\r\n        }\r\n        // 如果是数组格式\r\n        if (Array.isArray(carouselImages) && carouselImages.length > 0) {\r\n            return carouselImages[0];\r\n        }\r\n    } catch (error) {\r\n        console.error('解析图片数据失败:', error);\r\n        // 如果JSON解析失败，检查是否是直接的URL字符串\r\n        if (typeof carouselImages === 'string' && carouselImages.startsWith('http')) {\r\n            return carouselImages;\r\n        }\r\n    }\r\n\r\n    return '/static/image/parkPay.png'; // 默认图片\r\n};\r\n\r\n// 切换标签\r\nconst switchTab = (tab) => {\r\n    currentTab.value = tab;\r\n    if (tab === 'parking') {\r\n        isLoading.value = true;\r\n        initParkWarehouse();\r\n    }\r\n};\r\n\r\nconst goToMap = (item) => {\r\n    uni.openLocation({\r\n        latitude: item.latitude,\r\n        longitude: item.longitude,\r\n        name: item.warehouseName,\r\n        address: item.address,\r\n        success: () => {\r\n            console.log('打开地图成功');\r\n        }\r\n    });\r\n};\r\n\r\n// 拨打电话咨询\r\nconst makePhoneCall = (item) => {\r\n    if (!item.managerPhone) {\r\n        uni.showToast({\r\n            title: '暂无联系电话',\r\n            icon: 'none'\r\n        });\r\n        return;\r\n    }\r\n    \r\n    uni.showModal({\r\n        title: '拨打电话',\r\n        content: `是否拨打 ${item.managerPhone} 的咨询电话？`,\r\n        success: (res) => {\r\n            if (res.confirm) {\r\n                uni.makePhoneCall({\r\n                    phoneNumber: item.managerPhone,\r\n                    success: () => {\r\n                        console.log('拨打电话成功');\r\n                    },\r\n                    fail: (err) => {\r\n                        console.error('拨打电话失败:', err);\r\n                        uni.showToast({\r\n                            title: '拨打电话失败',\r\n                            icon: 'none'\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    });\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n    console.log('加载更多数据');\r\n};\r\n// 跳转详情\r\nconst goToDetail = (id)=>{\r\n    uni.navigateTo({\r\n        url: `/pages/warehouse/warehouseDetail?id=${id}`\r\n    })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.warehouse-container {\r\n    height: 100vh;\r\n    background-color: #f5f5f5;\r\n    position: relative;\r\n}\r\n\r\n.tab-header {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    background: linear-gradient(135deg, #4BA1FC, #7e6dff);\r\n    padding: 10rpx 30rpx;\r\n    z-index: 1000;\r\n    flex-shrink: 0;\r\n\r\n    .tab-item {\r\n        flex: 1;\r\n        text-align: center;\r\n        font-size: 28rpx;\r\n        color: rgba(255, 255, 255, 0.9);\r\n        padding: 20rpx 0;\r\n        position: relative;\r\n\r\n        &.active {\r\n            color: #ffffff;\r\n            font-weight: bold;\r\n\r\n            &::after {\r\n                content: '';\r\n                position: absolute;\r\n                bottom: 8rpx;\r\n                left: 50%;\r\n                transform: translateX(-50%);\r\n                width: 40rpx;\r\n                height: 4rpx;\r\n                background-color: #ffffff;\r\n                border-radius: 2rpx;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tab-divider {\r\n    width: 2rpx;\r\n    height: 40rpx;\r\n    background-color: rgba(255, 255, 255, 0.3);\r\n    margin: 0 20rpx;\r\n}\r\n\r\n.warehouse-list-container {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    margin-top: 100rpx; // 为固定的tab-header留出空间\r\n}\r\n\r\n.warehouse-list {\r\n    height: 100%;\r\n}\r\n\r\n.list-content {\r\n    padding: 20rpx;\r\n    padding-bottom: 190rpx;\r\n}\r\n\r\n.loading-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 400rpx;\r\n    padding: 100rpx 0;\r\n}\r\n\r\n.warehouse-item {\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    padding: 20rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);\r\n\r\n    // 整体布局：图片-信息-操作\r\n    .item-layout {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 20rpx;\r\n\r\n        // 左侧：图片\r\n        .item-image {\r\n            width: 200rpx;\r\n            height: 160rpx;\r\n            border-radius: 12rpx;\r\n            overflow: hidden;\r\n            flex-shrink: 0;\r\n            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\r\n            .image {\r\n                width: 100%;\r\n                height: 100%;\r\n            }\r\n        }\r\n\r\n        // 中间：场库信息\r\n        .item-info {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .name {\r\n                font-size: 30rpx;\r\n                font-weight: 600;\r\n                color: #333;\r\n            }\r\n\r\n            .time {\r\n                font-size: 26rpx;\r\n                color: #666;\r\n                margin: 22rpx 0;\r\n            }\r\n\r\n            .address {\r\n                font-size: 24rpx;\r\n                color: #333;\r\n                word-break: break-all;\r\n            }\r\n        }\r\n\r\n        // 右侧：距离和咨询按钮\r\n        .item-actions {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            gap: 12rpx;\r\n            flex-shrink: 0;\r\n\r\n            .distance-info {\r\n                margin-bottom: 30rpx;\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                height: 68rpx; // 固定高度：图标40rpx + 间距4rpx + 文本24rpx\r\n                justify-content: center;\r\n\r\n                &.distance-hidden {\r\n                    visibility: hidden;\r\n                    pointer-events: none;\r\n                }\r\n\r\n                .distance-icon {\r\n                    width: 40rpx;\r\n                    height: 40rpx;\r\n                    margin-bottom: 4rpx;\r\n                }\r\n\r\n                .distance-text {\r\n                    font-size: 20rpx;\r\n                    color: #4BA1FC;\r\n                    font-weight: 500;\r\n                    min-height: 24rpx;\r\n                    line-height: 24rpx;\r\n                    display: block;\r\n                }\r\n            }\r\n\r\n            .consult-btn {\r\n                display: flex;\r\n                align-items: center;\r\n   \r\n                background: linear-gradient(135deg, #4BA1FC, #7e6dff);\r\n                border-radius: 20rpx;\r\n                padding: 8rpx 16rpx;\r\n\r\n                .consult-text {\r\n                    color: #ffffff;\r\n                    font-size: 22rpx;\r\n                    font-weight: 500;\r\n                    margin-left: 6rpx;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.empty-tip {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 28rpx;\r\n    padding: 100rpx 0;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/warehouse/warehouse.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShow", "uni", "getParkWareHouseList"], "mappings": ";;;;;;;;;;;;;;;;AAuEA,MAAM,eAAe,MAAW;;;;AAMhC,UAAM,aAAaA,cAAAA,IAAI,SAAS;AAChC,UAAM,oBAAoBA,cAAAA,IAAI,CAAA,CAAE;AAChC,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAC7B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAE1BC,kBAAAA,OAAO,MAAM;AAET,UAAI,kBAAkB,MAAM,WAAW,GAAG;AACtC,kBAAU,QAAQ;AAAA,MACrB;AACD;IACJ,CAAC;AAGD,UAAM,kBAAkB,MAAM;AAC1BC,oBAAAA,MAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AACd,gBAAM,cAAc;AAAA,YAChB,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA,UAC/B;AAGY,gBAAM,kBAAkB,CAAC,aAAa,SAClC,aAAa,MAAM,aAAa,YAAY,YAC5C,aAAa,MAAM,cAAc,YAAY;AAEjD,uBAAa,QAAQ;AAGrB,cAAI,kBAAkB,MAAM,WAAW,KAAK,iBAAiB;AACzD;UAChB,OAAmB;AAEH;UACH;AAAA,QACJ;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,GAAG;AAE5B,uBAAa,QAAQ;AAErB,cAAI,kBAAkB,MAAM,WAAW,GAAG;AACtC;UACH;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAEA,UAAM,oBAAoB,MAAM;AAC5BC,yCAAsB,EAAC,KAAK,SAAO;AAC/B,cAAM,OAAO,IAAI;AAEjB,YAAI,aAAa,OAAO;AACpB,eAAK,QAAQ,UAAQ;AACjB,iBAAK,WAAW;AAAA,cACZ,aAAa,MAAM;AAAA,cACnB,aAAa,MAAM;AAAA,cACnB,KAAK;AAAA,cACL,KAAK;AAAA,YACzB;AAAA,UACA,CAAa;AAGD,eAAK,KAAK,CAAC,GAAG,MAAM;AAEhB,gBAAI,EAAE,aAAa,UAAa,EAAE,aAAa;AAAW,qBAAO;AACjE,gBAAI,EAAE,aAAa;AAAW,qBAAO;AACrC,gBAAI,EAAE,aAAa;AAAW,qBAAO;AACrC,mBAAO,EAAE,WAAW,EAAE;AAAA,UACtC,CAAa;AAAA,QACJ;AACD,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ;AAAA,MAC1B,CAAK,EAAE,MAAM,SAAO;AACZD,sBAAc,MAAA,MAAA,SAAA,wCAAA,aAAa,GAAG;AAC9B,kBAAU,QAAQ;AAAA,MAC1B,CAAK;AAAA,IACL;AAGA,UAAM,oBAAoB,CAAC,MAAM,MAAM,MAAM,SAAS;AAClD,YAAM,UAAU,OAAO,KAAK,KAAK;AACjC,YAAM,UAAU,OAAO,KAAK,KAAK;AACjC,YAAM,IAAI,UAAU;AACpB,YAAM,IAAI,OAAO,KAAK,KAAK,MAAQ,OAAO,KAAK,KAAK;AACpD,UAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACpI,UAAI,IAAI;AACR,UAAI,KAAK,MAAM,IAAI,GAAG,IAAI;AAC1B,aAAO;AAAA,IACX;AAGA,UAAM,uBAAuB,MAAM;AAC/B,UAAI,CAAC,aAAa,SAAS,kBAAkB,MAAM,WAAW,GAAG;AAC7D;AAAA,MACH;AAGD,wBAAkB,MAAM,QAAQ,UAAQ;AACpC,aAAK,WAAW;AAAA,UACZ,aAAa,MAAM;AAAA,UACnB,aAAa,MAAM;AAAA,UACnB,KAAK;AAAA,UACL,KAAK;AAAA,QACjB;AAAA,MACA,CAAK;AAGD,wBAAkB,MAAM,KAAK,CAAC,GAAG,MAAM;AACnC,YAAI,EAAE,aAAa,UAAa,EAAE,aAAa;AAAW,iBAAO;AACjE,YAAI,EAAE,aAAa;AAAW,iBAAO;AACrC,YAAI,EAAE,aAAa;AAAW,iBAAO;AACrC,eAAO,EAAE,WAAW,EAAE;AAAA,MAC9B,CAAK;AAAA,IACL;AAGA,UAAM,gBAAgB,CAAC,mBAAmB;AACtC,UAAI,CAAC;AAAgB,eAAO;AAE5B,UAAI;AAEA,YAAI,OAAO,mBAAmB,UAAU;AAEpC,gBAAM,cAAc,KAAK,MAAM,cAAc;AAC7C,cAAI,MAAM,QAAQ,WAAW,KAAK,YAAY,SAAS,GAAG;AAEtD,gBAAI,OAAO,YAAY,CAAC,MAAM,UAAU;AACpC,qBAAO,YAAY,CAAC;AAAA,YACvB;AAED,gBAAI,MAAM,QAAQ,YAAY,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE,SAAS,GAAG;AAC5D,qBAAO,YAAY,CAAC,EAAE,CAAC;AAAA,YAC1B;AAAA,UACJ;AAAA,QACJ;AAED,YAAI,MAAM,QAAQ,cAAc,KAAK,eAAe,SAAS,GAAG;AAC5D,iBAAO,eAAe,CAAC;AAAA,QAC1B;AAAA,MACJ,SAAQ,OAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,wCAAc,aAAa,KAAK;AAEhC,YAAI,OAAO,mBAAmB,YAAY,eAAe,WAAW,MAAM,GAAG;AACzE,iBAAO;AAAA,QACV;AAAA,MACJ;AAED,aAAO;AAAA,IACX;AAGA,UAAM,YAAY,CAAC,QAAQ;AACvB,iBAAW,QAAQ;AACnB,UAAI,QAAQ,WAAW;AACnB,kBAAU,QAAQ;AAClB;MACH;AAAA,IACL;AAEA,UAAM,UAAU,CAAC,SAAS;AACtBA,oBAAAA,MAAI,aAAa;AAAA,QACb,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS,MAAM;AACXA,wBAAAA,2DAAY,QAAQ;AAAA,QACvB;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC5B,UAAI,CAAC,KAAK,cAAc;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAClB,CAAS;AACD;AAAA,MACH;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,YAAY;AAAA,QAClC,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACbA,0BAAAA,MAAI,cAAc;AAAA,cACd,aAAa,KAAK;AAAA,cAClB,SAAS,MAAM;AACXA,8BAAAA,2DAAY,QAAQ;AAAA,cACvB;AAAA,cACD,MAAM,CAAC,QAAQ;AACXA,8BAAc,MAAA,MAAA,SAAA,wCAAA,WAAW,GAAG;AAC5BA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,gBAClC,CAAyB;AAAA,cACJ;AAAA,YACrB,CAAiB;AAAA,UACJ;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,WAAW,MAAM;AACnBA,oBAAAA,MAAA,MAAA,OAAA,wCAAY,QAAQ;AAAA,IACxB;AAEA,UAAM,aAAa,CAAC,OAAK;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,uCAAuC,EAAE;AAAA,MACtD,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpSA,GAAG,WAAW,eAAe;"}