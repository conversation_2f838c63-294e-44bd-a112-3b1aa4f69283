{"version": 3, "file": "warehouseDetail.js", "sources": ["pages/warehouse/warehouseDetail.vue", "E:/3/HbuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2FyZWhvdXNlL3dhcmVob3VzZURldGFpbC52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"warehouse-detail\">\r\n        <!-- 顶部轮播图 -->\r\n        <view class=\"carousel-container\">\r\n            <swiper class=\"carousel\" :indicator-dots=\"carouselImages.length > 1\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\r\n                <swiper-item v-for=\"(image, index) in carouselImages\" :key=\"index\">\r\n                    <image :src=\"image\" mode=\"aspectFill\" class=\"carousel-image\" />\r\n                </swiper-item>\r\n            </swiper>\r\n        </view>\r\n\r\n        <!-- 场库基本信息 -->\r\n        <view class=\"info-section\">\r\n            <view class=\"warehouse-header\">\r\n                <text class=\"warehouse-name\">{{ warehouseDetail.warehouseName || warehouseDetail.projectName }}</text>\r\n                <view class=\"status-tag\" :class=\"{ active: warehouseDetail.status === 1 }\">\r\n                    {{ warehouseDetail.status === 1 ? '营业中' : '暂停营业' }}\r\n                </view>\r\n            </view>\r\n            \r\n            <view class=\"address-row\" @tap=\"openMap\">\r\n                <image src=\"/static/icon/address.png\" class=\"icon\" />\r\n                <text class=\"address-text\">{{ warehouseDetail.address }}</text>\r\n                <text class=\"nav-text\">导航</text>\r\n            </view>\r\n            \r\n            <view class=\"contact-row\" v-if=\"warehouseDetail.managerPhone\" @tap=\"makeCall\">\r\n                <image src=\"/static/icon/phone.png\" class=\"icon\" />\r\n                <text class=\"contact-text\">{{ warehouseDetail.managerPhone }}</text>\r\n                <text class=\"call-text\">拨打</text>\r\n            </view>\r\n        </view>\r\n        <!-- 收费规则 -->\r\n        <view class=\"info-section\" v-if=\"warehouseDetail.remark\">\r\n            <view class=\"section-title\">收费规则</view>\r\n            <view class=\"remark-content\">\r\n                <text class=\"remark-text\">{{ warehouseDetail.remark }}</text>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from \"vue\";\r\nimport { onLoad } from \"@dcloudio/uni-app\";\r\nimport { getWarehouseDetail } from \"@/api/warehouse\";\r\n\r\nconst id = ref(0);\r\nconst warehouseDetail = ref({});\r\n\r\n// 处理轮播图数据\r\nconst carouselImages = computed(() => {\r\n    if (!warehouseDetail.value.carouselImages) return [];\r\n    try {\r\n        const images = JSON.parse(warehouseDetail.value.carouselImages);\r\n        return Array.isArray(images) ? images : [images];\r\n    } catch (e) {\r\n        return [];\r\n    }\r\n});\r\n\r\nonLoad((options) => {\r\n    console.log(options);\r\n    id.value = options.id;\r\n    fetchWarehouseDetail();\r\n})\r\n\r\nconst fetchWarehouseDetail = async () => {\r\n    const res = await getWarehouseDetail(id.value);\r\n    warehouseDetail.value = res.data;\r\n    console.log(warehouseDetail.value);\r\n}\r\n\r\n// 打开地图导航\r\nconst openMap = () => {\r\n    const { latitude, longitude, warehouseName, address } = warehouseDetail.value;\r\n    uni.openLocation({\r\n        latitude: parseFloat(latitude),\r\n        longitude: parseFloat(longitude),\r\n        name: warehouseName,\r\n        address: address,\r\n        success: () => {\r\n            console.log('打开地图成功');\r\n        },\r\n        fail: (err) => {\r\n            console.log('打开地图失败', err);\r\n            uni.showToast({\r\n                title: '无法打开地图',\r\n                icon: 'none'\r\n            });\r\n        }\r\n    });\r\n};\r\n\r\n// 拨打电话\r\nconst makeCall = () => {\r\n    uni.makePhoneCall({\r\n        phoneNumber: warehouseDetail.value.managerPhone,\r\n        success: () => {\r\n            console.log('拨打电话成功');\r\n        },\r\n        fail: (err) => {\r\n            console.log('拨打电话失败', err);\r\n        }\r\n    });\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.warehouse-detail {\r\n    background-color: #f5f5f5;\r\n    min-height: 100vh;\r\n}\r\n\r\n.carousel-container {\r\n    width: 100%;\r\n    height: 400rpx;\r\n    \r\n    .carousel {\r\n        width: 100%;\r\n        height: 100%;\r\n        \r\n        .carousel-image {\r\n            width: 100%;\r\n            height: 100%;\r\n        }\r\n    }\r\n}\r\n\r\n.info-section {\r\n    background: white;\r\n    margin: 20rpx 0;\r\n    padding: 30rpx;\r\n    \r\n    &:first-child {\r\n        margin-top: 0;\r\n    }\r\n}\r\n\r\n.warehouse-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .warehouse-name {\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        flex: 1;\r\n    }\r\n    \r\n    .status-tag {\r\n        padding: 8rpx 20rpx;\r\n        background: #f0f0f0;\r\n        color: #999;\r\n        border-radius: 20rpx;\r\n        font-size: 24rpx;\r\n        \r\n        &.active {\r\n            background: #e8f5e8;\r\n            color: #52c41a;\r\n        }\r\n    }\r\n}\r\n\r\n.address-row, .contact-row {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    \r\n    &:last-child {\r\n        border-bottom: none;\r\n        margin-bottom: 0;\r\n    }\r\n    \r\n    .icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        margin-right: 20rpx;\r\n    }\r\n    \r\n    .address-text, .contact-text {\r\n        flex: 1;\r\n        color: #333;\r\n        font-size: 28rpx;\r\n        line-height: 1.4;\r\n    }\r\n    \r\n    .nav-text, .call-text {\r\n        color: #1890ff;\r\n        font-size: 28rpx;\r\n    }\r\n}\r\n\r\n.section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 30rpx;\r\n    padding-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n\r\n\r\n.remark-content {\r\n    .remark-text {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        line-height: 1.6;\r\n    }\r\n}\r\n\r\n\r\n</style>", "import MiniProgramPage from 'F:/parking/park-uniapp/pages/warehouse/warehouseDetail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "uni", "getWarehouseDetail"], "mappings": ";;;;;;;AA+CA,UAAM,KAAKA,cAAAA,IAAI,CAAC;AAChB,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AAClC,UAAI,CAAC,gBAAgB,MAAM;AAAgB,eAAO,CAAA;AAClD,UAAI;AACA,cAAM,SAAS,KAAK,MAAM,gBAAgB,MAAM,cAAc;AAC9D,eAAO,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAAA,MAClD,SAAQ,GAAG;AACR,eAAO;MACV;AAAA,IACL,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAChBC,oBAAAA,MAAA,MAAA,OAAA,6CAAY,OAAO;AACnB,SAAG,QAAQ,QAAQ;AACnB;IACJ,CAAC;AAED,UAAM,uBAAuB,YAAY;AACrC,YAAM,MAAM,MAAMC,cAAAA,mBAAmB,GAAG,KAAK;AAC7C,sBAAgB,QAAQ,IAAI;AAC5BD,oBAAY,MAAA,MAAA,OAAA,6CAAA,gBAAgB,KAAK;AAAA,IACrC;AAGA,UAAM,UAAU,MAAM;AAClB,YAAM,EAAE,UAAU,WAAW,eAAe,QAAS,IAAG,gBAAgB;AACxEA,oBAAAA,MAAI,aAAa;AAAA,QACb,UAAU,WAAW,QAAQ;AAAA,QAC7B,WAAW,WAAW,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN;AAAA,QACA,SAAS,MAAM;AACXA,wBAAAA,gEAAY,QAAQ;AAAA,QACvB;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAY,MAAA,MAAA,OAAA,6CAAA,UAAU,GAAG;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACtB,CAAa;AAAA,QACJ;AAAA,MACT,CAAK;AAAA,IACL;AAGA,UAAM,WAAW,MAAM;AACnBA,oBAAAA,MAAI,cAAc;AAAA,QACd,aAAa,gBAAgB,MAAM;AAAA,QACnC,SAAS,MAAM;AACXA,wBAAAA,iEAAY,QAAQ;AAAA,QACvB;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAY,MAAA,MAAA,OAAA,8CAAA,UAAU,GAAG;AAAA,QAC5B;AAAA,MACT,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxGA,GAAG,WAAW,eAAe;"}