{"version": 3, "file": "amap.js", "sources": ["utils/amap.js"], "sourcesContent": ["// 高德地图SDK包装模块\r\n// 解决uni-app中导入第三方JS库的兼容性问题\r\n\r\n// 高德地图SDK代码\r\nfunction AMapWX(a){this.key=a.key;this.requestConfig={key:a.key,s:\"rsx\",platform:\"WXJS\",appname:a.key,sdkversion:\"1.2.0\",logversion:\"2.0\"};this.MeRequestConfig={key:a.key,serviceName:\"https://restapi.amap.com/rest/me\"}}\r\nAMapWX.prototype.getWxLocation=function(a,b){wx.getLocation({type:\"gcj02\",success:function(c){c=c.longitude+\",\"+c.latitude;wx.setStorage({key:\"userLocation\",data:c});b(c)},fail:function(c){wx.getStorage({key:\"userLocation\",success:function(d){d.data&&b(d.data)}});a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\nAMapWX.prototype.getMEKeywordsSearch=function(a){if(!a.options)return a.fail({errCode:\"0\",errMsg:\"\\u7f3a\\u5c11\\u5fc5\\u8981\\u53c2\\u6570\"});var b=a.options,c=this.MeRequestConfig,d={key:c.key,s:\"rsx\",platform:\"WXJS\",appname:a.key,sdkversion:\"1.2.0\",logversion:\"2.0\"};b.layerId&&(d.layerId=b.layerId);b.keywords&&(d.keywords=b.keywords);b.city&&(d.city=b.city);b.filter&&(d.filter=b.filter);b.sortrule&&(d.sortrule=b.sortrule);b.pageNum&&(d.pageNum=b.pageNum);b.pageSize&&(d.pageSize=b.pageSize);b.sig&&(d.sig=b.sig);wx.request({url:c.serviceName+\"/cpoint/datasearch/local\",data:d,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(e){(e=e.data)&&e.status&&\"1\"===e.status&&0===e.code?a.success(e.data):a.fail({errCode:\"0\",errMsg:e})},fail:function(e){a.fail({errCode:\"0\",errMsg:e.errMsg||\"\"})}})};\r\nAMapWX.prototype.getMEIdSearch=function(a){if(!a.options)return a.fail({errCode:\"0\",errMsg:\"\\u7f3a\\u5c11\\u5fc5\\u8981\\u53c2\\u6570\"});var b=a.options,c=this.MeRequestConfig,d={key:c.key,s:\"rsx\",platform:\"WXJS\",appname:a.key,sdkversion:\"1.2.0\",logversion:\"2.0\"};b.layerId&&(d.layerId=b.layerId);b.id&&(d.id=b.id);b.sig&&(d.sig=b.sig);wx.request({url:c.serviceName+\"/cpoint/datasearch/id\",data:d,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(e){(e=e.data)&&e.status&&\"1\"===e.status&&0===e.code?a.success(e.data):a.fail({errCode:\"0\",errMsg:e})},fail:function(e){a.fail({errCode:\"0\",errMsg:e.errMsg||\"\"})}})};\r\nAMapWX.prototype.getMEPolygonSearch=function(a){if(!a.options)return a.fail({errCode:\"0\",errMsg:\"\\u7f3a\\u5c11\\u5fc5\\u8981\\u53c2\\u6570\"});var b=a.options,c=this.MeRequestConfig,d={key:c.key,s:\"rsx\",platform:\"WXJS\",appname:a.key,sdkversion:\"1.2.0\",logversion:\"2.0\"};b.layerId&&(d.layerId=b.layerId);b.keywords&&(d.keywords=b.keywords);b.polygon&&(d.polygon=b.polygon);b.filter&&(d.filter=b.filter);b.sortrule&&(d.sortrule=b.sortrule);b.pageNum&&(d.pageNum=b.pageNum);b.pageSize&&(d.pageSize=b.pageSize);b.sig&&(d.sig=b.sig);wx.request({url:c.serviceName+\"/cpoint/datasearch/polygon\",data:d,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(e){(e=e.data)&&e.status&&\"1\"===e.status&&0===e.code?a.success(e.data):a.fail({errCode:\"0\",errMsg:e})},fail:function(e){a.fail({errCode:\"0\",errMsg:e.errMsg||\"\"})}})};\r\nAMapWX.prototype.getMEaroundSearch=function(a){if(!a.options)return a.fail({errCode:\"0\",errMsg:\"\\u7f3a\\u5c11\\u5fc5\\u8981\\u53c2\\u6570\"});var b=a.options,c=this.MeRequestConfig,d={key:c.key,s:\"rsx\",platform:\"WXJS\",appname:a.key,sdkversion:\"1.2.0\",logversion:\"2.0\"};b.layerId&&(d.layerId=b.layerId);b.keywords&&(d.keywords=b.keywords);b.center&&(d.center=b.center);b.radius&&(d.radius=b.radius);b.filter&&(d.filter=b.filter);b.sortrule&&(d.sortrule=b.sortrule);b.pageNum&&(d.pageNum=b.pageNum);b.pageSize&&(d.pageSize=b.pageSize);b.sig&&(d.sig=b.sig);wx.request({url:c.serviceName+\"/cpoint/datasearch/around\",data:d,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(e){(e=e.data)&&e.status&&\"1\"===e.status&&0===e.code?a.success(e.data):a.fail({errCode:\"0\",errMsg:e})},fail:function(e){a.fail({errCode:\"0\",errMsg:e.errMsg||\"\"})}})};\r\nAMapWX.prototype.getGeo=function(a){var b=this.requestConfig,c=a.options;b={key:this.key,extensions:\"all\",s:b.s,platform:b.platform,appname:this.key,sdkversion:b.sdkversion,logversion:b.logversion};c.address&&(b.address=c.address);c.city&&(b.city=c.city);c.batch&&(b.batch=c.batch);c.sig&&(b.sig=c.sig);wx.request({url:\"https://restapi.amap.com/v3/geocode/geo\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(d){(d=d.data)&&d.status&&\"1\"===d.status?a.success(d):a.fail({errCode:\"0\",errMsg:d})},fail:function(d){a.fail({errCode:\"0\",errMsg:d.errMsg||\"\"})}})};\r\nAMapWX.prototype.getRegeo=function(a){function b(d){var e=c.requestConfig;wx.request({url:\"https://restapi.amap.com/v3/geocode/regeo\",data:{key:c.key,location:d,extensions:\"all\",s:e.s,platform:e.platform,appname:c.key,sdkversion:e.sdkversion,logversion:e.logversion},method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(g){if(g.data.status&&\"1\"==g.data.status){g=g.data.regeocode;var h=g.addressComponent,f=[],k=g.roads[0].name+\"\\u9644\\u8fd1\",m=d.split(\",\")[0],n=d.split(\",\")[1];if(g.pois&&g.pois[0]){k=g.pois[0].name+\"\\u9644\\u8fd1\";var l=g.pois[0].location;l&&(m=parseFloat(l.split(\",\")[0]),n=parseFloat(l.split(\",\")[1]))}h.provice&&f.push(h.provice);h.city&&f.push(h.city);h.district&&f.push(h.district);h.streetNumber&&h.streetNumber.street&&h.streetNumber.number?(f.push(h.streetNumber.street),f.push(h.streetNumber.number)):f.push(g.roads[0].name);f=f.join(\"\");a.success([{iconPath:a.iconPath,width:a.iconWidth,height:a.iconHeight,name:f,desc:k,longitude:m,latitude:n,id:0,regeocodeData:g}])}else a.fail({errCode:g.data.infocode,errMsg:g.data.info})},fail:function(g){a.fail({errCode:\"0\",errMsg:g.errMsg||\"\"})}})}var c=this;a.location?b(a.location):c.getWxLocation(a,function(d){b(d)})};\r\nAMapWX.prototype.getWeather=function(a){function b(g){var h=\"base\";a.type&&\"forecast\"==a.type&&(h=\"all\");wx.request({url:\"https://restapi.amap.com/v3/weather/weatherInfo\",data:{key:d.key,city:g,extensions:h,s:e.s,platform:e.platform,appname:d.key,sdkversion:e.sdkversion,logversion:e.logversion},method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(f){if(f.data.status&&\"1\"==f.data.status)if(f.data.lives){if((f=f.data.lives)&&0<f.length){f=f[0];var k={city:{text:\"\\u57ce\\u5e02\",data:f.city},weather:{text:\"\\u5929\\u6c14\",data:f.weather},temperature:{text:\"\\u6e29\\u5ea6\",data:f.temperature},winddirection:{text:\"\\u98ce\\u5411\",data:f.winddirection+\"\\u98ce\"},windpower:{text:\"\\u98ce\\u529b\",data:f.windpower+\"\\u7ea7\"},humidity:{text:\"\\u6e7f\\u5ea6\",data:f.humidity+\"%\"}};k.liveData=f;a.success(k)}}else f.data.forecasts&&f.data.forecasts[0]&&a.success({forecast:f.data.forecasts[0]});else a.fail({errCode:f.data.infocode,errMsg:f.data.info})},fail:function(f){a.fail({errCode:\"0\",errMsg:f.errMsg||\"\"})}})}function c(g){wx.request({url:\"https://restapi.amap.com/v3/geocode/regeo\",data:{key:d.key,location:g,extensions:\"all\",s:e.s,platform:e.platform,appname:d.key,sdkversion:e.sdkversion,logversion:e.logversion},method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(h){if(h.data.status&&\"1\"==h.data.status){h=h.data.regeocode;if(h.addressComponent)var f=h.addressComponent.adcode;else h.aois&&0<h.aois.length&&(f=h.aois[0].adcode);b(f)}else a.fail({errCode:h.data.infocode,errMsg:h.data.info})},fail:function(h){a.fail({errCode:\"0\",errMsg:h.errMsg||\"\"})}})}var d=this,e=d.requestConfig;a.city?b(a.city):d.getWxLocation(a,function(g){c(g)})};\r\nAMapWX.prototype.getPoiAround=function(a){function b(e){e={key:c.key,location:e,s:d.s,platform:d.platform,appname:c.key,sdkversion:d.sdkversion,logversion:d.logversion};a.querytypes&&(e.types=a.querytypes);a.querykeywords&&(e.keywords=a.querykeywords);wx.request({url:\"https://restapi.amap.com/v3/place/around\",data:e,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(g){if(g.data.status&&\"1\"==g.data.status){if((g=g.data)&&g.pois){for(var h=[],f=0;f<g.pois.length;f++){var k=0==f?a.iconPathSelected:a.iconPath;h.push({latitude:parseFloat(g.pois[f].location.split(\",\")[1]),longitude:parseFloat(g.pois[f].location.split(\",\")[0]),iconPath:k,width:22,height:32,id:f,name:g.pois[f].name,address:g.pois[f].address})}a.success({markers:h,poisData:g.pois})}}else a.fail({errCode:g.data.infocode,errMsg:g.data.info})},fail:function(g){a.fail({errCode:\"0\",errMsg:g.errMsg||\"\"})}})}var c=this,d=c.requestConfig;a.location?b(a.location):c.getWxLocation(a,function(e){b(e)})};\r\nAMapWX.prototype.getStaticmap=function(a){function b(e){c.push(\"location=\"+e);a.zoom&&c.push(\"zoom=\"+a.zoom);a.size&&c.push(\"size=\"+a.size);a.scale&&c.push(\"scale=\"+a.scale);a.markers&&c.push(\"markers=\"+a.markers);a.labels&&c.push(\"labels=\"+a.labels);a.paths&&c.push(\"paths=\"+a.paths);a.traffic&&c.push(\"traffic=\"+a.traffic);e=\"https://restapi.amap.com/v3/staticmap?\"+c.join(\"&\");a.success({url:e})}var c=[];c.push(\"key=\"+this.key);var d=this.requestConfig;c.push(\"s=\"+d.s);c.push(\"platform=\"+d.platform);c.push(\"appname=\"+d.appname);c.push(\"sdkversion=\"+d.sdkversion);c.push(\"logversion=\"+d.logversion);a.location?b(a.location):this.getWxLocation(a,function(e){b(e)})};\r\nAMapWX.prototype.getInputtips=function(a){var b=Object.assign({},this.requestConfig);a.location&&(b.location=a.location);a.keywords&&(b.keywords=a.keywords);a.type&&(b.type=a.type);a.city&&(b.city=a.city);a.citylimit&&(b.citylimit=a.citylimit);wx.request({url:\"https://restapi.amap.com/v3/assistant/inputtips\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(c){c&&c.data&&c.data.tips&&a.success({tips:c.data.tips})},fail:function(c){a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\nAMapWX.prototype.getDrivingRoute=function(a){var b=Object.assign({},this.requestConfig);a.origin&&(b.origin=a.origin);a.destination&&(b.destination=a.destination);a.strategy&&(b.strategy=a.strategy);a.waypoints&&(b.waypoints=a.waypoints);a.avoidpolygons&&(b.avoidpolygons=a.avoidpolygons);a.avoidroad&&(b.avoidroad=a.avoidroad);wx.request({url:\"https://restapi.amap.com/v3/direction/driving\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(c){c&&c.data&&c.data.route&&a.success({paths:c.data.route.paths,taxi_cost:c.data.route.taxi_cost||\"\"})},fail:function(c){a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\nAMapWX.prototype.getWalkingRoute=function(a){var b=Object.assign({},this.requestConfig);a.origin&&(b.origin=a.origin);a.destination&&(b.destination=a.destination);wx.request({url:\"https://restapi.amap.com/v3/direction/walking\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(c){c&&c.data&&c.data.route&&a.success({paths:c.data.route.paths})},fail:function(c){a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\nAMapWX.prototype.getTransitRoute=function(a){var b=Object.assign({},this.requestConfig);a.origin&&(b.origin=a.origin);a.destination&&(b.destination=a.destination);a.strategy&&(b.strategy=a.strategy);a.city&&(b.city=a.city);a.cityd&&(b.cityd=a.cityd);wx.request({url:\"https://restapi.amap.com/v3/direction/transit/integrated\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(c){c&&c.data&&c.data.route&&(c=c.data.route,a.success({distance:c.distance||\"\",taxi_cost:c.taxi_cost||\"\",transits:c.transits}))},fail:function(c){a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\nAMapWX.prototype.getRidingRoute=function(a){var b=Object.assign({},this.requestConfig);a.origin&&(b.origin=a.origin);a.destination&&(b.destination=a.destination);wx.request({url:\"https://restapi.amap.com/v3/direction/riding\",data:b,method:\"GET\",header:{\"content-type\":\"application/json\"},success:function(c){c&&c.data&&c.data.route&&a.success({paths:c.data.route.paths})},fail:function(c){a.fail({errCode:\"0\",errMsg:c.errMsg||\"\"})}})};\r\n\r\n// 导出模块\r\nexport { AMapWX }\r\n"], "names": ["wx"], "mappings": ";;AAIA,SAAS,OAAO,GAAE;AAAC,OAAK,MAAI,EAAE;AAAI,OAAK,gBAAc,EAAC,KAAI,EAAE,KAAI,GAAE,OAAM,UAAS,QAAO,SAAQ,EAAE,KAAI,YAAW,SAAQ,YAAW,MAAK;AAAE,OAAK,kBAAgB,EAAC,KAAI,EAAE,KAAI,aAAY,mCAAkC;AAAC;AAC1N,OAAO,UAAU,gBAAc,SAAS,GAAE,GAAE;AAACA,gBAAE,KAAC,YAAY,EAAC,MAAK,SAAQ,SAAQ,SAAS,GAAE;AAAC,QAAE,EAAE,YAAU,MAAI,EAAE;AAASA,kBAAAA,KAAG,WAAW,EAAC,KAAI,gBAAe,MAAK,EAAC,CAAC;AAAE,MAAE,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAACA,kBAAE,KAAC,WAAW,EAAC,KAAI,gBAAe,SAAQ,SAAS,GAAE;AAAC,QAAE,QAAM,EAAE,EAAE,IAAI;AAAA,IAAC,EAAC,CAAC;AAAE,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACpT,OAAO,UAAU,sBAAoB,SAAS,GAAE;AAAC,MAAG,CAAC,EAAE;AAAQ,WAAO,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,SAAsC,CAAC;AAAE,MAAI,IAAE,EAAE,SAAQ,IAAE,KAAK,iBAAgB,IAAE,EAAC,KAAI,EAAE,KAAI,GAAE,OAAM,UAAS,QAAO,SAAQ,EAAE,KAAI,YAAW,SAAQ,YAAW,MAAK;AAAE,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,SAAO,EAAE,OAAK,EAAE;AAAM,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,QAAM,EAAE,MAAI,EAAE;AAAKA,gBAAE,KAAC,QAAQ,EAAC,KAAI,EAAE,cAAY,4BAA2B,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,KAAC,IAAE,EAAE,SAAO,EAAE,UAAQ,QAAM,EAAE,UAAQ,MAAI,EAAE,OAAK,EAAE,QAAQ,EAAE,IAAI,IAAE,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AAC9yB,OAAO,UAAU,gBAAc,SAAS,GAAE;AAAC,MAAG,CAAC,EAAE;AAAQ,WAAO,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,SAAsC,CAAC;AAAE,MAAI,IAAE,EAAE,SAAQ,IAAE,KAAK,iBAAgB,IAAE,EAAC,KAAI,EAAE,KAAI,GAAE,OAAM,UAAS,QAAO,SAAQ,EAAE,KAAI,YAAW,SAAQ,YAAW,MAAK;AAAE,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,OAAK,EAAE,KAAG,EAAE;AAAI,IAAE,QAAM,EAAE,MAAI,EAAE;AAAKA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,EAAE,cAAY,yBAAwB,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,KAAC,IAAE,EAAE,SAAO,EAAE,UAAQ,QAAM,EAAE,UAAQ,MAAI,EAAE,OAAK,EAAE,QAAQ,EAAE,IAAI,IAAE,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACpnB,OAAO,UAAU,qBAAmB,SAAS,GAAE;AAAC,MAAG,CAAC,EAAE;AAAQ,WAAO,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,SAAsC,CAAC;AAAE,MAAI,IAAE,EAAE,SAAQ,IAAE,KAAK,iBAAgB,IAAE,EAAC,KAAI,EAAE,KAAI,GAAE,OAAM,UAAS,QAAO,SAAQ,EAAE,KAAI,YAAW,SAAQ,YAAW,MAAK;AAAE,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,QAAM,EAAE,MAAI,EAAE;AAAKA,gBAAE,KAAC,QAAQ,EAAC,KAAI,EAAE,cAAY,8BAA6B,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,KAAC,IAAE,EAAE,SAAO,EAAE,UAAQ,QAAM,EAAE,UAAQ,MAAI,EAAE,OAAK,EAAE,QAAQ,EAAE,IAAI,IAAE,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACxzB,OAAO,UAAU,oBAAkB,SAAS,GAAE;AAAC,MAAG,CAAC,EAAE;AAAQ,WAAO,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,SAAsC,CAAC;AAAE,MAAI,IAAE,EAAE,SAAQ,IAAE,KAAK,iBAAgB,IAAE,EAAC,KAAI,EAAE,KAAI,GAAE,OAAM,UAAS,QAAO,SAAQ,EAAE,KAAI,YAAW,SAAQ,YAAW,MAAK;AAAE,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,QAAM,EAAE,MAAI,EAAE;AAAKA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,EAAE,cAAY,6BAA4B,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,KAAC,IAAE,EAAE,SAAO,EAAE,UAAQ,QAAM,EAAE,UAAQ,MAAI,EAAE,OAAK,EAAE,QAAQ,EAAE,IAAI,IAAE,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACj1B,OAAO,UAAU,SAAO,SAAS,GAAE;AAAC,MAAI,IAAE,KAAK,eAAc,IAAE,EAAE;AAAQ,MAAE,EAAC,KAAI,KAAK,KAAI,YAAW,OAAM,GAAE,EAAE,GAAE,UAAS,EAAE,UAAS,SAAQ,KAAK,KAAI,YAAW,EAAE,YAAW,YAAW,EAAE,WAAU;AAAE,IAAE,YAAU,EAAE,UAAQ,EAAE;AAAS,IAAE,SAAO,EAAE,OAAK,EAAE;AAAM,IAAE,UAAQ,EAAE,QAAM,EAAE;AAAO,IAAE,QAAM,EAAE,MAAI,EAAE;AAAKA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,2CAA0C,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,KAAC,IAAE,EAAE,SAAO,EAAE,UAAQ,QAAM,EAAE,SAAO,EAAE,QAAQ,CAAC,IAAE,EAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AAC3kB,OAAO,UAAU,WAAS,SAAS,GAAE;AAAC,WAAS,EAAE,GAAE;AAAC,QAAI,IAAE,EAAE;AAAcA,kBAAE,KAAC,QAAQ,EAAC,KAAI,6CAA4C,MAAK,EAAC,KAAI,EAAE,KAAI,UAAS,GAAE,YAAW,OAAM,GAAE,EAAE,GAAE,UAAS,EAAE,UAAS,SAAQ,EAAE,KAAI,YAAW,EAAE,YAAW,YAAW,EAAE,WAAU,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,UAAG,EAAE,KAAK,UAAQ,OAAK,EAAE,KAAK,QAAO;AAAC,YAAE,EAAE,KAAK;AAAU,YAAI,IAAE,EAAE,kBAAiB,IAAE,CAAA,GAAG,IAAE,EAAE,MAAM,CAAC,EAAE,OAAK,MAAe,IAAE,EAAE,MAAM,GAAG,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,GAAG,EAAE,CAAC;AAAE,YAAG,EAAE,QAAM,EAAE,KAAK,CAAC,GAAE;AAAC,cAAE,EAAE,KAAK,CAAC,EAAE,OAAK;AAAe,cAAI,IAAE,EAAE,KAAK,CAAC,EAAE;AAAS,gBAAI,IAAE,WAAW,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,GAAE,IAAE,WAAW,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,QAAE;AAAC,UAAE,WAAS,EAAE,KAAK,EAAE,OAAO;AAAE,UAAE,QAAM,EAAE,KAAK,EAAE,IAAI;AAAE,UAAE,YAAU,EAAE,KAAK,EAAE,QAAQ;AAAE,UAAE,gBAAc,EAAE,aAAa,UAAQ,EAAE,aAAa,UAAQ,EAAE,KAAK,EAAE,aAAa,MAAM,GAAE,EAAE,KAAK,EAAE,aAAa,MAAM,KAAG,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI;AAAE,YAAE,EAAE,KAAK,EAAE;AAAE,UAAE,QAAQ,CAAC,EAAC,UAAS,EAAE,UAAS,OAAM,EAAE,WAAU,QAAO,EAAE,YAAW,MAAK,GAAE,MAAK,GAAE,WAAU,GAAE,UAAS,GAAE,IAAG,GAAE,eAAc,EAAC,CAAC,CAAC;AAAA,MAAC;AAAM,UAAE,KAAK,EAAC,SAAQ,EAAE,KAAK,UAAS,QAAO,EAAE,KAAK,KAAI,CAAC;AAAA,IAAC,GAAE,MAAK,SAAS,GAAE;AAAC,QAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE;AAAK,IAAE,WAAS,EAAE,EAAE,QAAQ,IAAE,EAAE,cAAc,GAAE,SAAS,GAAE;AAAC,MAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AAC1rC,OAAO,UAAU,aAAW,SAAS,GAAE;AAAC,WAAS,EAAE,GAAE;AAAC,QAAI,IAAE;AAAO,MAAE,QAAM,cAAY,EAAE,SAAO,IAAE;AAAOA,kBAAE,KAAC,QAAQ,EAAC,KAAI,mDAAkD,MAAK,EAAC,KAAI,EAAE,KAAI,MAAK,GAAE,YAAW,GAAE,GAAE,EAAE,GAAE,UAAS,EAAE,UAAS,SAAQ,EAAE,KAAI,YAAW,EAAE,YAAW,YAAW,EAAE,WAAU,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,UAAG,EAAE,KAAK,UAAQ,OAAK,EAAE,KAAK;AAAO,YAAG,EAAE,KAAK,OAAM;AAAC,eAAI,IAAE,EAAE,KAAK,UAAQ,IAAE,EAAE,QAAO;AAAC,gBAAE,EAAE,CAAC;AAAE,gBAAI,IAAE,EAAC,MAAK,EAAC,MAAK,MAAe,MAAK,EAAE,KAAI,GAAE,SAAQ,EAAC,MAAK,MAAe,MAAK,EAAE,QAAO,GAAE,aAAY,EAAC,MAAK,MAAe,MAAK,EAAE,YAAW,GAAE,eAAc,EAAC,MAAK,MAAe,MAAK,EAAE,gBAAc,IAAQ,GAAE,WAAU,EAAC,MAAK,MAAe,MAAK,EAAE,YAAU,IAAQ,GAAE,UAAS,EAAC,MAAK,MAAe,MAAK,EAAE,WAAS,IAAG,EAAC;AAAE,cAAE,WAAS;AAAE,cAAE,QAAQ,CAAC;AAAA,UAAC;AAAA,QAAC;AAAM,YAAE,KAAK,aAAW,EAAE,KAAK,UAAU,CAAC,KAAG,EAAE,QAAQ,EAAC,UAAS,EAAE,KAAK,UAAU,CAAC,EAAC,CAAC;AAAA;AAAO,UAAE,KAAK,EAAC,SAAQ,EAAE,KAAK,UAAS,QAAO,EAAE,KAAK,KAAI,CAAC;AAAA,IAAC,GAAE,MAAK,SAAS,GAAE;AAAC,QAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,WAAS,EAAE,GAAE;AAACA,kBAAE,KAAC,QAAQ,EAAC,KAAI,6CAA4C,MAAK,EAAC,KAAI,EAAE,KAAI,UAAS,GAAE,YAAW,OAAM,GAAE,EAAE,GAAE,UAAS,EAAE,UAAS,SAAQ,EAAE,KAAI,YAAW,EAAE,YAAW,YAAW,EAAE,WAAU,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,UAAG,EAAE,KAAK,UAAQ,OAAK,EAAE,KAAK,QAAO;AAAC,YAAE,EAAE,KAAK;AAAU,YAAG,EAAE;AAAiB,cAAI,IAAE,EAAE,iBAAiB;AAAA;AAAY,YAAE,QAAM,IAAE,EAAE,KAAK,WAAS,IAAE,EAAE,KAAK,CAAC,EAAE;AAAQ,UAAE,CAAC;AAAA,MAAC;AAAM,UAAE,KAAK,EAAC,SAAQ,EAAE,KAAK,UAAS,QAAO,EAAE,KAAK,KAAI,CAAC;AAAA,IAAC,GAAE,MAAK,SAAS,GAAE;AAAC,QAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,MAAK,IAAE,EAAE;AAAc,IAAE,OAAK,EAAE,EAAE,IAAI,IAAE,EAAE,cAAc,GAAE,SAAS,GAAE;AAAC,MAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AACzoD,OAAO,UAAU,eAAa,SAAS,GAAE;AAAC,WAAS,EAAE,GAAE;AAAC,QAAE,EAAC,KAAI,EAAE,KAAI,UAAS,GAAE,GAAE,EAAE,GAAE,UAAS,EAAE,UAAS,SAAQ,EAAE,KAAI,YAAW,EAAE,YAAW,YAAW,EAAE,WAAU;AAAE,MAAE,eAAa,EAAE,QAAM,EAAE;AAAY,MAAE,kBAAgB,EAAE,WAAS,EAAE;AAAeA,uBAAG,QAAQ,EAAC,KAAI,4CAA2C,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,UAAG,EAAE,KAAK,UAAQ,OAAK,EAAE,KAAK,QAAO;AAAC,aAAI,IAAE,EAAE,SAAO,EAAE,MAAK;AAAC,mBAAQ,IAAE,CAAE,GAAC,IAAE,GAAE,IAAE,EAAE,KAAK,QAAO,KAAI;AAAC,gBAAI,IAAE,KAAG,IAAE,EAAE,mBAAiB,EAAE;AAAS,cAAE,KAAK,EAAC,UAAS,WAAW,EAAE,KAAK,CAAC,EAAE,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC,GAAE,WAAU,WAAW,EAAE,KAAK,CAAC,EAAE,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC,GAAE,UAAS,GAAE,OAAM,IAAG,QAAO,IAAG,IAAG,GAAE,MAAK,EAAE,KAAK,CAAC,EAAE,MAAK,SAAQ,EAAE,KAAK,CAAC,EAAE,QAAO,CAAC;AAAA,UAAC;AAAC,YAAE,QAAQ,EAAC,SAAQ,GAAE,UAAS,EAAE,KAAI,CAAC;AAAA,QAAC;AAAA,MAAC;AAAM,UAAE,KAAK,EAAC,SAAQ,EAAE,KAAK,UAAS,QAAO,EAAE,KAAK,KAAI,CAAC;AAAA,IAAC,GAAE,MAAK,SAAS,GAAE;AAAC,QAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,MAAK,IAAE,EAAE;AAAc,IAAE,WAAS,EAAE,EAAE,QAAQ,IAAE,EAAE,cAAc,GAAE,SAAS,GAAE;AAAC,MAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AACz9B,OAAO,UAAU,eAAa,SAAS,GAAE;AAAC,WAAS,EAAE,GAAE;AAAC,MAAE,KAAK,cAAY,CAAC;AAAE,MAAE,QAAM,EAAE,KAAK,UAAQ,EAAE,IAAI;AAAE,MAAE,QAAM,EAAE,KAAK,UAAQ,EAAE,IAAI;AAAE,MAAE,SAAO,EAAE,KAAK,WAAS,EAAE,KAAK;AAAE,MAAE,WAAS,EAAE,KAAK,aAAW,EAAE,OAAO;AAAE,MAAE,UAAQ,EAAE,KAAK,YAAU,EAAE,MAAM;AAAE,MAAE,SAAO,EAAE,KAAK,WAAS,EAAE,KAAK;AAAE,MAAE,WAAS,EAAE,KAAK,aAAW,EAAE,OAAO;AAAE,QAAE,2CAAyC,EAAE,KAAK,GAAG;AAAE,MAAE,QAAQ,EAAC,KAAI,EAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,CAAE;AAAC,IAAE,KAAK,SAAO,KAAK,GAAG;AAAE,MAAI,IAAE,KAAK;AAAc,IAAE,KAAK,OAAK,EAAE,CAAC;AAAE,IAAE,KAAK,cAAY,EAAE,QAAQ;AAAE,IAAE,KAAK,aAAW,EAAE,OAAO;AAAE,IAAE,KAAK,gBAAc,EAAE,UAAU;AAAE,IAAE,KAAK,gBAAc,EAAE,UAAU;AAAE,IAAE,WAAS,EAAE,EAAE,QAAQ,IAAE,KAAK,cAAc,GAAE,SAAS,GAAE;AAAC,MAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AAC5pB,OAAO,UAAU,eAAa,SAAS,GAAE;AAAC,MAAI,IAAE,OAAO,OAAO,CAAA,GAAG,KAAK,aAAa;AAAE,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,SAAO,EAAE,OAAK,EAAE;AAAM,IAAE,SAAO,EAAE,OAAK,EAAE;AAAM,IAAE,cAAY,EAAE,YAAU,EAAE;AAAWA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,mDAAkD,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,SAAG,EAAE,QAAM,EAAE,KAAK,QAAM,EAAE,QAAQ,EAAC,MAAK,EAAE,KAAK,KAAI,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AAC7f,OAAO,UAAU,kBAAgB,SAAS,GAAE;AAAC,MAAI,IAAE,OAAO,OAAO,IAAG,KAAK,aAAa;AAAE,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,gBAAc,EAAE,cAAY,EAAE;AAAa,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,cAAY,EAAE,YAAU,EAAE;AAAW,IAAE,kBAAgB,EAAE,gBAAc,EAAE;AAAe,IAAE,cAAY,EAAE,YAAU,EAAE;AAAWA,qBAAG,QAAQ,EAAC,KAAI,iDAAgD,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,SAAG,EAAE,QAAM,EAAE,KAAK,SAAO,EAAE,QAAQ,EAAC,OAAM,EAAE,KAAK,MAAM,OAAM,WAAU,EAAE,KAAK,MAAM,aAAW,GAAE,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AAC7nB,OAAO,UAAU,kBAAgB,SAAS,GAAE;AAAC,MAAI,IAAE,OAAO,OAAO,CAAA,GAAG,KAAK,aAAa;AAAE,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,gBAAc,EAAE,cAAY,EAAE;AAAaA,gBAAE,KAAC,QAAQ,EAAC,KAAI,iDAAgD,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,SAAG,EAAE,QAAM,EAAE,KAAK,SAAO,EAAE,QAAQ,EAAC,OAAM,EAAE,KAAK,MAAM,MAAK,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACnb,OAAO,UAAU,kBAAgB,SAAS,GAAE;AAAC,MAAI,IAAE,OAAO,OAAO,IAAG,KAAK,aAAa;AAAE,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,gBAAc,EAAE,cAAY,EAAE;AAAa,IAAE,aAAW,EAAE,WAAS,EAAE;AAAU,IAAE,SAAO,EAAE,OAAK,EAAE;AAAM,IAAE,UAAQ,EAAE,QAAM,EAAE;AAAOA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,4DAA2D,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,SAAG,EAAE,QAAM,EAAE,KAAK,UAAQ,IAAE,EAAE,KAAK,OAAM,EAAE,QAAQ,EAAC,UAAS,EAAE,YAAU,IAAG,WAAU,EAAE,aAAW,IAAG,UAAS,EAAE,SAAQ,CAAC;AAAA,EAAE,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AACnlB,OAAO,UAAU,iBAAe,SAAS,GAAE;AAAC,MAAI,IAAE,OAAO,OAAO,CAAA,GAAG,KAAK,aAAa;AAAE,IAAE,WAAS,EAAE,SAAO,EAAE;AAAQ,IAAE,gBAAc,EAAE,cAAY,EAAE;AAAaA,gBAAAA,KAAG,QAAQ,EAAC,KAAI,gDAA+C,MAAK,GAAE,QAAO,OAAM,QAAO,EAAC,gBAAe,mBAAkB,GAAE,SAAQ,SAAS,GAAE;AAAC,SAAG,EAAE,QAAM,EAAE,KAAK,SAAO,EAAE,QAAQ,EAAC,OAAM,EAAE,KAAK,MAAM,MAAK,CAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,MAAE,KAAK,EAAC,SAAQ,KAAI,QAAO,EAAE,UAAQ,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAC;;"}