{"version": 3, "file": "permission.js", "sources": ["utils/permission.js"], "sourcesContent": ["/*\r\n *  添加路由拦截器，不在白名单内的页面须登录才能访问\r\n */\r\n//白名单 不需要验证token\r\nconst whiteList = [\r\n  '/pages/home/<USER>',\r\n  '/pages/warehouse/warehouse',\r\n  '/pages/warehouse/warehouseDetail',\r\n\t'/pages/login/login',\r\n  '/pages/mine/mine',\r\n  '/pages/noPlate/noPlateIn',\r\n  '/pages/noPlate/noPlateOut',\r\n  '/pages/aggrement/user-aggrement',\r\n  '/pages/aggrement/privacy-aggrement',\r\n  '/pages/carStop/carStop'\r\n]\r\n//登录页\r\nexport default function initPermission() {\r\n  /**\r\n   * 页面跳转拦截器\r\n   */\r\n  const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']\r\n  list.forEach(item => {\r\n    //用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器\r\n    uni.addInterceptor(item, {\r\n      invoke(e) {\r\n        // 调用前拦截\r\n        const userInfo = uni.getStorageSync('wxUser')\r\n        //获取用户的token\r\n        const token = uni.getStorageSync('token')\r\n        //获取要跳转的页面路径（url去掉\"?\"和\"?\"后的参数）\r\n        const url = e.url.split('?')[0]\r\n        let notNeed = whiteList.includes(url)\r\n        // 如果在whiteList里面就不需要登录\r\n        if (notNeed) {\r\n          return e\r\n        } else {\r\n          //需要登录\r\n          if (!token) {\r\n            // 判断用户是否要登录\r\n            uni.showModal({\r\n              title: '温馨提示',\r\n              content: '您还未登录，请先登录',\r\n              success: function (res) {\r\n                if (res.confirm) {\r\n                  uni.reLaunch({\r\n                    url: '/pages/login/login'\r\n                  })\r\n                }\r\n              }\r\n            })\r\n            return false\r\n          } else {\r\n            return e\r\n          }\r\n        }\r\n      },\r\n      fail(err) {\r\n        // 失败回调拦截\r\n        console.log(err)\r\n      }\r\n    })\r\n  })\r\n}\r\n"], "names": ["uni"], "mappings": ";;AAIA,MAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACD;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEe,SAAS,iBAAiB;AAIvC,QAAM,OAAO,CAAC,cAAc,cAAc,YAAY,WAAW;AACjE,OAAK,QAAQ,UAAQ;AAEnBA,kBAAG,MAAC,eAAe,MAAM;AAAA,MACvB,OAAO,GAAG;AAESA,sBAAG,MAAC,eAAe,QAAQ;AAE5C,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AAExC,cAAM,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAC9B,YAAI,UAAU,UAAU,SAAS,GAAG;AAEpC,YAAI,SAAS;AACX,iBAAO;AAAA,QACjB,OAAe;AAEL,cAAI,CAAC,OAAO;AAEVA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,SAAS,SAAU,KAAK;AACtB,oBAAI,IAAI,SAAS;AACfA,gCAAAA,MAAI,SAAS;AAAA,oBACX,KAAK;AAAA,kBACzB,CAAmB;AAAA,gBACF;AAAA,cACF;AAAA,YACf,CAAa;AACD,mBAAO;AAAA,UACnB,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD,KAAK,KAAK;AAERA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,GAAG;AAAA,MAChB;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;;"}