{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["import { getStorageSync } from './utils'\r\nimport RequestManager from '@/utils/requestManager.js'\r\nimport { URL } from '@/config/index.js'\r\n\r\n// 获取小程序环境信息\r\nconst accountInfo = wx.getAccountInfoSync()\r\nconst envVersion = accountInfo.miniProgram.envVersion\r\n\r\n// 请求管理实例\r\nconst manager = new RequestManager()\r\n\r\n/**\r\n * 设置请求头\r\n * @param {string} type - 请求类型（json/form）\r\n * @returns {Object} 请求头\r\n */\r\nconst setHeaders = (type) => {\r\n  const header = {\r\n    Authorization: 'WxBearer ' + getStorageSync('token') || '',\r\n  }\r\n  if (type === 'form') {\r\n    header['content-type'] = 'application/x-www-form-urlencoded'\r\n  }\r\n  return header\r\n}\r\n\r\n/**\r\n * 处理未授权（401）情况\r\n */\r\nconst handleUnauthorized = () => {\r\n  uni.removeStorageSync('token')\r\n  uni.removeStorageSync('wxUser')\r\n}\r\n\r\n/**\r\n * 显示错误提示\r\n * @param {string} message - 错误信息\r\n */\r\nconst showErrorToast = (message) => {\r\n  uni.showToast({\r\n    title: message || '网络连接失败，请稍后重试',\r\n    icon: 'none',\r\n    duration: 2000,\r\n  })\r\n}\r\n\r\n/**\r\n * 基础请求方法\r\n * @param {string} url - 请求地址\r\n * @param {string} method - 请求方法\r\n * @param {Object} data - 请求数据\r\n * @param {string} type - 请求类型（json/form）\r\n * @param {boolean} loading - 是否显示加载状态\r\n * @returns {Promise} 请求结果\r\n */\r\nconst baseRequest = async (url, method, data = {}, type = 'json', loading = true) => {\r\n  // 生成请求 ID，防止重复请求\r\n  const requestId = manager.generateId(method, url, data)\r\n  if (!requestId) {\r\n    showErrorToast('请勿重复请求')\r\n    return Promise.reject('重复请求')\r\n  }\r\n\r\n  try {\r\n    // 发起请求\r\n    const result = await new Promise((resolve, reject) => {\r\n      uni.request({\r\n        url: URL + url,\r\n        method: method || 'get',\r\n        header: setHeaders(type),\r\n        timeout: method === 'get' ? 10000 : 30000, \r\n        data: data,\r\n        success: (res) => {\r\n          const response = res.data\r\n          if (response.code === 200) {\r\n            resolve(response)\r\n          } else if (response.code === 401) {\r\n            handleUnauthorized()\r\n            reject(response)\r\n          } else {\r\n            showErrorToast(response.msg)\r\n            reject(response)\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          showErrorToast(err.errMsg)\r\n          reject(err)\r\n        },\r\n        complete: () => {\r\n          manager.deleteById(requestId)\r\n        },\r\n      })\r\n    })\r\n    return result\r\n  } catch (error) {\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * 请求方法封装\r\n */\r\nconst request = {}\r\n;['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {\r\n    request[method] = (api, data, type, loading) => baseRequest(api, method, data, type, loading)\r\n  })\r\n\r\nexport default request\r\n"], "names": ["wx", "RequestManager", "getStorageSync", "uni", "URL"], "mappings": ";;;;;AAKA,MAAM,cAAcA,cAAE,KAAC,mBAAoB;AACxB,YAAY,YAAY;AAG3C,MAAM,UAAU,IAAIC,qBAAAA,eAAgB;AAOpC,MAAM,aAAa,CAAC,SAAS;AAC3B,QAAM,SAAS;AAAA,IACb,eAAe,cAAcC,2BAAe,OAAO,KAAK;AAAA,EACzD;AACD,MAAI,SAAS,QAAQ;AACnB,WAAO,cAAc,IAAI;AAAA,EAC1B;AACD,SAAO;AACT;AAKA,MAAM,qBAAqB,MAAM;AAC/BC,gBAAG,MAAC,kBAAkB,OAAO;AAC7BA,gBAAG,MAAC,kBAAkB,QAAQ;AAChC;AAMA,MAAM,iBAAiB,CAAC,YAAY;AAClCA,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO,WAAW;AAAA,IAClB,MAAM;AAAA,IACN,UAAU;AAAA,EACd,CAAG;AACH;AAWA,MAAM,cAAc,OAAO,KAAK,QAAQ,OAAO,CAAE,GAAE,OAAO,QAAQ,UAAU,SAAS;AAEnF,QAAM,YAAY,QAAQ,WAAW,QAAQ,KAAK,IAAI;AACtD,MAAI,CAAC,WAAW;AACd,mBAAe,QAAQ;AACvB,WAAO,QAAQ,OAAO,MAAM;AAAA,EAC7B;AAED,MAAI;AAEF,UAAM,SAAS,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAKC,aAAG,MAAG;AAAA,QACX,QAAQ,UAAU;AAAA,QAClB,QAAQ,WAAW,IAAI;AAAA,QACvB,SAAS,WAAW,QAAQ,MAAQ;AAAA,QACpC;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,gBAAM,WAAW,IAAI;AACrB,cAAI,SAAS,SAAS,KAAK;AACzB,oBAAQ,QAAQ;AAAA,UAC5B,WAAqB,SAAS,SAAS,KAAK;AAChC,+BAAoB;AACpB,mBAAO,QAAQ;AAAA,UAC3B,OAAiB;AACL,2BAAe,SAAS,GAAG;AAC3B,mBAAO,QAAQ;AAAA,UAChB;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,yBAAe,IAAI,MAAM;AACzB,iBAAO,GAAG;AAAA,QACX;AAAA,QACD,UAAU,MAAM;AACd,kBAAQ,WAAW,SAAS;AAAA,QAC7B;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AACD,WAAO;AAAA,EACR,SAAQ,OAAO;AACd,UAAM;AAAA,EACP;AACH;AAKK,MAAC,UAAU,CAAE;AACjB,CAAC,WAAW,OAAO,QAAQ,OAAO,QAAQ,UAAU,SAAS,SAAS,EAAE,QAAQ,CAAC,WAAW;AACzF,UAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,MAAM,YAAY,YAAY,KAAK,QAAQ,MAAM,MAAM,OAAO;AAC7F,CAAA;;"}