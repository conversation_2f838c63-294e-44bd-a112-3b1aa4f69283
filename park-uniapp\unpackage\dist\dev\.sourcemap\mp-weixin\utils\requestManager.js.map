{"version": 3, "file": "requestManager.js", "sources": ["utils/requestManager.js"], "sourcesContent": ["/*\r\n * \r\n * @Description:\r\n */\r\nclass RequestManager {\r\n  constructor() {\r\n    this.idMap = new Map()\r\n  }\r\n  /**\r\n   * 生成唯一ID，并将ID和请求信息存储到map对象中\r\n   * @param {string} method - 请求方法\r\n   * @param {string} url - 请求URL\r\n   * @param {object} params - 请求参数\r\n   * @returns {string|boolean} - 生成的唯一ID，如果存在相同请求则返回false\r\n   */\r\n  generateId(method, url, params) {\r\n    const id = this.generateUniqueId(method, url, params)\r\n    if (this.idMap.has(id)) {\r\n      return false\r\n    }\r\n    this.idMap.set(id, { method, url, params })\r\n    return id\r\n  }\r\n\r\n  /**\r\n   * 根据ID删除map对象中的请求信息\r\n   * @param {string} id - 要删除的唯一ID\r\n   */\r\n  deleteById(id) {\r\n    this.idMap.delete(id)\r\n  }\r\n\r\n  /**\r\n   * 生成唯一ID的方法\r\n   * @param {string} method - 请求方法\r\n   * @param {string} url - 请求URL\r\n   * @param {object} params - 请求参数\r\n   * @returns {string} - 生成的唯一ID\r\n   */\r\n  generateUniqueId(method, url, params) {\r\n    const idString = `${method}-${url}-${this.serializeObject(params)}`\r\n    let id = 0\r\n    for (let i = 0; i < idString.length; i++) {\r\n      id = (id << 5) - id + idString.charCodeAt(i)\r\n      id |= 0\r\n    }\r\n    return id.toString()\r\n  }\r\n\r\n  /**\r\n   * 序列化对象为字符串\r\n   * @param {object} obj - 要序列化的对象\r\n   * @returns {string} - 序列化后的字符串\r\n   */\r\n  serializeObject(obj) {\r\n    const keys = Object.keys(obj).sort()\r\n    const serializedObj = {}\r\n    for (let key of keys) {\r\n      const value = obj[key]\r\n      if (value !== null && typeof value === 'object') {\r\n        serializedObj[key] = this.serializeObject(value)\r\n      } else {\r\n        serializedObj[key] = value\r\n      }\r\n    }\r\n    return JSON.stringify(serializedObj)\r\n  }\r\n}\r\n\r\nexport default RequestManager\r\n"], "names": [], "mappings": ";AAIA,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,QAAQ,KAAK,QAAQ;AAC9B,UAAM,KAAK,KAAK,iBAAiB,QAAQ,KAAK,MAAM;AACpD,QAAI,KAAK,MAAM,IAAI,EAAE,GAAG;AACtB,aAAO;AAAA,IACR;AACD,SAAK,MAAM,IAAI,IAAI,EAAE,QAAQ,KAAK,QAAQ;AAC1C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,IAAI;AACb,SAAK,MAAM,OAAO,EAAE;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,iBAAiB,QAAQ,KAAK,QAAQ;AACpC,UAAM,WAAW,GAAG,MAAM,IAAI,GAAG,IAAI,KAAK,gBAAgB,MAAM,CAAC;AACjE,QAAI,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,MAAM,KAAK,KAAK,SAAS,WAAW,CAAC;AAC3C,YAAM;AAAA,IACP;AACD,WAAO,GAAG,SAAU;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB,KAAK;AACnB,UAAM,OAAO,OAAO,KAAK,GAAG,EAAE,KAAM;AACpC,UAAM,gBAAgB,CAAE;AACxB,aAAS,OAAO,MAAM;AACpB,YAAM,QAAQ,IAAI,GAAG;AACrB,UAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C,sBAAc,GAAG,IAAI,KAAK,gBAAgB,KAAK;AAAA,MACvD,OAAa;AACL,sBAAc,GAAG,IAAI;AAAA,MACtB;AAAA,IACF;AACD,WAAO,KAAK,UAAU,aAAa;AAAA,EACpC;AACH;;"}