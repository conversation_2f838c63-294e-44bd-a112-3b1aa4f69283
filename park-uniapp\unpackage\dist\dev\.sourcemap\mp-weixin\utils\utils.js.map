{"version": 3, "file": "utils.js", "sources": ["utils/utils.js"], "sourcesContent": ["import { URL } from '@/config/index.js'\r\n\r\n/**\r\n * 提示方法\r\n * @param {String} title 提示文字\r\n * @param {String}  icon icon图片\r\n * @param {Number}  duration 提示时间\r\n */\r\nfunction toast(title, icon = 'none', duration = 2000) {\r\n  if (title) {\r\n    uni.showToast({\r\n      title,\r\n      icon,\r\n      duration\r\n    })\r\n  }\r\n}\r\n\r\n/**\r\n * 设置缓存\r\n * @param {String} key 键名\r\n * @param {String} data 值\r\n */\r\nexport function setStorageSync(key, data) {\r\n  uni.setStorageSync(key, data)\r\n}\r\n\r\n/**\r\n * 获取缓存\r\n * @param {String} key 键名\r\n */\r\nexport function getStorageSync(key) {\r\n  return uni.getStorageSync(key)\r\n}\r\n\r\n/**\r\n * 删除缓存\r\n * @param {String} key 键名\r\n */\r\nexport function removeStorageSync(key) {\r\n  return uni.removeStorageSync(key)\r\n}\r\n\r\n/**\r\n * 清空缓存\r\n * @param {String} key 键名\r\n */\r\nexport function clearStorageSync() {\r\n  return uni.clearStorageSync()\r\n}\r\n\r\n/**\r\n * 深拷贝\r\n * @param {Object} data\r\n **/\r\nconst deepClone = data => JSON.parse(JSON.stringify(data))\r\n\r\nfunction DateInitFromStr(inStr) {\r\n  // 这个函数是支持你现在提供的2种数据格式的，包括了'2020-07-08 13:24:27'和'2020-07-01'\r\n  // 对于'2020-07-01'，等效于'2020-07-01 00:00:00.000'\r\n  return new Date(inStr)\r\n}\r\nfunction getTimeDiff(inDStr1, inDStr2) {\r\n  let D1 = DateInitFromStr(inDStr1)\r\n  let D2 = DateInitFromStr(inDStr2)\r\n  return (D1.getTime() - D2.getTime()) / 1000\r\n}\r\n\r\nexport default {\r\n  toast,\r\n  deepClone,\r\n  getTimeDiff\r\n}\r\n\r\n// 获取微信导航栏信息\r\nexport const getWechatNavBarInfo = () => {\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  const menuButton = wx.getMenuButtonBoundingClientRect();\r\n  \r\n  // 关键计算公式（微信小程序专用）\r\n  return {\r\n    statusBarHeight: systemInfo.statusBarHeight, // 状态栏高度\r\n    navBarHeight: menuButton.bottom + menuButton.top - systemInfo.statusBarHeight, // 导航栏总高度\r\n    menuButtonRight: systemInfo.windowWidth - menuButton.right, // 胶囊按钮右侧距离\r\n    warehouseTop: menuButton.top + (menuButton.height - 30.5) / 2, // 30.5为选择器高度\r\n    warehouseLeft: 8, // 与胶囊按钮左对齐间距\r\n    windowHeightHalf: systemInfo.windowHeight / 2, // 窗口宽度一半\r\n  };\r\n};\r\n\r\n// 跨平台兼容的日期比较函数\r\nexport const isSameDate = (date1, date2) => {\r\n    if (!date1 || !date2) return false;\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n           date1.getMonth() === date2.getMonth() &&\r\n           date1.getDate() === date2.getDate();\r\n};\r\n\r\n// 安全提取日期部分（YYYY-MM-DD格式）\r\nexport const extractDatePart = (dateTimeString) => {\r\n    if (!dateTimeString) return '';\r\n    \r\n    try {\r\n        // 尝试多种方式提取日期部分\r\n        if (dateTimeString.includes(' ')) {\r\n            return dateTimeString.split(' ')[0];\r\n        } else if (dateTimeString.includes('T')) {\r\n            return dateTimeString.split('T')[0];\r\n        } else {\r\n            // 如果是纯日期格式，验证格式后返回\r\n            const dateRegex = /^\\d{4}-\\d{2}-\\d{2}$/;\r\n            if (dateRegex.test(dateTimeString)) {\r\n                return dateTimeString;\r\n            } else {\r\n                console.warn('日期格式不正确:', dateTimeString);\r\n                return '';\r\n            }\r\n        }\r\n    } catch (error) {\r\n        console.error('提取日期部分失败:', error, dateTimeString);\r\n        return '';\r\n    }\r\n};\r\n\r\n// 格式化日期为YYYY-MM-DD格式（跨平台兼容）\r\nexport const formatDate = (date) => {\r\n    if (!date) return '';\r\n    \r\n    try {\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0');\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        \r\n        return `${year}-${month}-${day}`;\r\n    } catch (error) {\r\n        console.error('格式化日期失败:', error, date);\r\n        return '';\r\n    }\r\n};\r\n\r\n// 安全创建日期对象（避免平台差异）\r\nexport const createDate = (dateString) => {\r\n    if (!dateString) return new Date();\r\n    \r\n    try {\r\n        // 如果是 YYYY-MM-DD 格式，确保正确解析\r\n        if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateString)) {\r\n            const [year, month, day] = dateString.split('-');\r\n            return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\r\n        } else {\r\n            // 其他格式使用默认构造函数\r\n            return new Date(dateString);\r\n        }\r\n    } catch (error) {\r\n        console.error('创建日期对象失败:', error, dateString);\r\n        return new Date();\r\n    }\r\n};\r\n\r\n// 上传头像文件\r\nexport const uploadAvatar = (filePath) => {\r\n    return new Promise((resolve, reject) => {\r\n        uni.uploadFile({\r\n            url: URL+'/file/upload/path',\r\n            filePath: filePath,\r\n            name: 'file',\r\n            formData: {\r\n                path: 'avatar'\r\n            },\r\n            header: {\r\n                'Authorization': 'WxBearer ' + (uni.getStorageSync('token') || '')\r\n            },\r\n            success: (uploadRes) => {\r\n                try {\r\n                    const result = JSON.parse(uploadRes.data);\r\n                    if (result.code === 200) {\r\n                        resolve(result.data.url);\r\n                    } else {\r\n                        reject(new Error(result.msg || '上传失败'));\r\n                    }\r\n                } catch (error) {\r\n                    reject(new Error('解析响应失败'));\r\n                }\r\n            },\r\n            fail: (error) => {\r\n                reject(error);\r\n            }\r\n        });\r\n    });\r\n};"], "names": ["uni", "URL"], "mappings": ";;;AAuBO,SAAS,eAAe,KAAK,MAAM;AACxCA,sBAAI,eAAe,KAAK,IAAI;AAC9B;AAMO,SAAS,eAAe,KAAK;AAClC,SAAOA,cAAG,MAAC,eAAe,GAAG;AAC/B;AA0DY,MAAC,aAAa,CAAC,OAAO,UAAU;AACxC,MAAI,CAAC,SAAS,CAAC;AAAO,WAAO;AAC7B,SAAO,MAAM,kBAAkB,MAAM,YAAa,KAC3C,MAAM,SAAQ,MAAO,MAAM,SAAU,KACrC,MAAM,QAAO,MAAO,MAAM,QAAO;AAC5C;AAGY,MAAC,kBAAkB,CAAC,mBAAmB;AAC/C,MAAI,CAAC;AAAgB,WAAO;AAE5B,MAAI;AAEA,QAAI,eAAe,SAAS,GAAG,GAAG;AAC9B,aAAO,eAAe,MAAM,GAAG,EAAE,CAAC;AAAA,IACrC,WAAU,eAAe,SAAS,GAAG,GAAG;AACrC,aAAO,eAAe,MAAM,GAAG,EAAE,CAAC;AAAA,IAC9C,OAAe;AAEH,YAAM,YAAY;AAClB,UAAI,UAAU,KAAK,cAAc,GAAG;AAChC,eAAO;AAAA,MACvB,OAAmB;AACHA,sBAAA,MAAA,MAAA,QAAA,yBAAa,YAAY,cAAc;AACvC,eAAO;AAAA,MACV;AAAA,IACJ;AAAA,EACJ,SAAQ,OAAO;AACZA,gEAAc,aAAa,OAAO,cAAc;AAChD,WAAO;AAAA,EACV;AACL;AAGY,MAAC,aAAa,CAAC,SAAS;AAChC,MAAI,CAAC;AAAM,WAAO;AAElB,MAAI;AACA,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,UAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAElD,WAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,EACjC,SAAQ,OAAO;AACZA,kBAAc,MAAA,MAAA,SAAA,yBAAA,YAAY,OAAO,IAAI;AACrC,WAAO;AAAA,EACV;AACL;AAGY,MAAC,aAAa,CAAC,eAAe;AACtC,MAAI,CAAC;AAAY,WAAO,oBAAI;AAE5B,MAAI;AAEA,QAAI,sBAAsB,KAAK,UAAU,GAAG;AACxC,YAAM,CAAC,MAAM,OAAO,GAAG,IAAI,WAAW,MAAM,GAAG;AAC/C,aAAO,IAAI,KAAK,SAAS,IAAI,GAAG,SAAS,KAAK,IAAI,GAAG,SAAS,GAAG,CAAC;AAAA,IAC9E,OAAe;AAEH,aAAO,IAAI,KAAK,UAAU;AAAA,IAC7B;AAAA,EACJ,SAAQ,OAAO;AACZA,kBAAc,MAAA,MAAA,SAAA,yBAAA,aAAa,OAAO,UAAU;AAC5C,WAAO,oBAAI,KAAI;AAAA,EAClB;AACL;AAGY,MAAC,eAAe,CAAC,aAAa;AACtC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpCA,kBAAAA,MAAI,WAAW;AAAA,MACX,KAAKC,aAAG,MAAC;AAAA,MACT;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,QACN,MAAM;AAAA,MACT;AAAA,MACD,QAAQ;AAAA,QACJ,iBAAiB,eAAeD,cAAG,MAAC,eAAe,OAAO,KAAK;AAAA,MAClE;AAAA,MACD,SAAS,CAAC,cAAc;AACpB,YAAI;AACA,gBAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AACxC,cAAI,OAAO,SAAS,KAAK;AACrB,oBAAQ,OAAO,KAAK,GAAG;AAAA,UAC/C,OAA2B;AACH,mBAAO,IAAI,MAAM,OAAO,OAAO,MAAM,CAAC;AAAA,UACzC;AAAA,QACJ,SAAQ,OAAO;AACZ,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC7B;AAAA,MACJ;AAAA,MACD,MAAM,CAAC,UAAU;AACb,eAAO,KAAK;AAAA,MACf;AAAA,IACb,CAAS;AAAA,EACT,CAAK;AACL;;;;;;;;"}