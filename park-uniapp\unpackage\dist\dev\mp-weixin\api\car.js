"use strict";
const utils_request = require("../utils/request.js");
const getCarList = () => utils_request.request.get("/wx/car/list");
const addCar = (data) => utils_request.request.post("/wx/car/insert", data);
const editCar = (data) => utils_request.request.put("/wx/car/update", data);
const deleteCar = (data) => utils_request.request.delete("/wx/car/delete", data);
const getCarDetailById = (data) => utils_request.request.get("/wx/car/detail", data);
exports.addCar = addCar;
exports.deleteCar = deleteCar;
exports.editCar = editCar;
exports.getCarDetailById = getCarDetailById;
exports.getCarList = getCarList;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/car.js.map
