"use strict";
const utils_request = require("../utils/request.js");
const getInvoiceTitleList = () => utils_request.request.get("/wx/invoiceTitle/list");
const addInvoiceTitle = (data) => utils_request.request.post("/wx/invoiceTitle/insert", data);
const editInvoiceTitle = (data) => utils_request.request.put("/wx/invoiceTitle/update", data);
const deleteInvoiceTitle = (data) => utils_request.request.delete("/wx/invoiceTitle/delete", data);
const postInvoiceSend = (data) => utils_request.request.post("/wx//invoice/record/send", data);
const postSaveInvoiceRecord = (data) => utils_request.request.post("/wx/invoice/record/insert", data);
const postResumeInvoice = (data) => utils_request.request.post("/wx/invoice/resume", data);
const getInvoiceRecordList = () => utils_request.request.post("/wx/invoice/record/list");
exports.addInvoiceTitle = addInvoiceTitle;
exports.deleteInvoiceTitle = deleteInvoiceTitle;
exports.editInvoiceTitle = editInvoiceTitle;
exports.getInvoiceRecordList = getInvoiceRecordList;
exports.getInvoiceTitleList = getInvoiceTitleList;
exports.postInvoiceSend = postInvoiceSend;
exports.postResumeInvoice = postResumeInvoice;
exports.postSaveInvoiceRecord = postSaveInvoiceRecord;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/invoice.js.map
