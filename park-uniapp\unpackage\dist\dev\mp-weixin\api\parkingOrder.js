"use strict";
const utils_request = require("../utils/request.js");
const getParkingOrderDetail = (params) => utils_request.request.post("/wx/parking/order/plateNo", params);
const getParkingOrderList = (params) => utils_request.request.post("/wx/parking/order/list", params);
const createParkingOrder = (params) => utils_request.request.post("/wx/parking/order/create", params);
const paymentTemporary = (params) => utils_request.request.post("/wx/parking/order/paymentTemporary", params);
const noPlateIn = (params) => utils_request.request.post("/wx/parking/order/noPlateIn", params);
const noPlateOut = (params) => utils_request.request.post("/wx/parking/order/noPlateOut", params);
exports.createParkingOrder = createParkingOrder;
exports.getParkingOrderDetail = getParkingOrderDetail;
exports.getParkingOrderList = getParkingOrderList;
exports.noPlateIn = noPlateIn;
exports.noPlateOut = noPlateOut;
exports.paymentTemporary = paymentTemporary;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/parkingOrder.js.map
