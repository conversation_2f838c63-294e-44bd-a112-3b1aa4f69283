"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_permission = require("./utils/permission.js");
if (!Math) {
  "./pages/home/<USER>";
  "./pages/warehouse/warehouse.js";
  "./pages/warehouse/warehouseDetail.js";
  "./pages/package/package.js";
  "./pages/mine/mine.js";
  "./pages/mine/mineEdit.js";
  "./pages/login/login.js";
  "./pages/payPlateQuery/payPlateQuery.js";
  "./pages/payPlateDetail/payPlateDetail.js";
  "./pages/noPlate/noPlateIn.js";
  "./pages/noPlate/noPlateOut.js";
  "./pages/myCar/myCar.js";
  "./pages/myCar/myCarAdd.js";
  "./pages/carStop/carStop.js";
  "./pages/package/packageBuy.js";
  "./pages/package/packageRecord.js";
  "./pages/parkingOrder/parkingOrder.js";
  "./pages/invoice/invoiceTitle.js";
  "./pages/invoice/addInvoiceTitle.js";
  "./pages/invoice/openInvoice.js";
  "./pages/invoice/invoiceManage.js";
  "./pages/package/packageVipBuy.js";
  "./pages/aggrement/user-aggrement.js";
  "./pages/aggrement/privacy-aggrement.js";
  "./pages/index/index.js";
}
const _sfc_main = {
  onLaunch: function() {
    utils_permission.initPermission();
    common_vendor.index.__f__("log", "at App.vue:6", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:9", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:12", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(common_vendor.uviewPlus);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
