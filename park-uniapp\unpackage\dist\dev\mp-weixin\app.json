{"pages": ["pages/home/<USER>", "pages/warehouse/warehouse", "pages/warehouse/warehouseDetail", "pages/package/package", "pages/mine/mine", "pages/mine/mineEdit", "pages/login/login", "pages/payPlateQuery/payPlateQuery", "pages/payPlateDetail/payPlateDetail", "pages/noPlate/noPlateIn", "pages/noPlate/noPlateOut", "pages/myCar/myCar", "pages/myCar/myCarAdd", "pages/carStop/carStop", "pages/package/packageBuy", "pages/package/packageRecord", "pages/parkingOrder/parkingOrder", "pages/invoice/invoiceTitle", "pages/invoice/addInvoiceTitle", "pages/invoice/openInvoice", "pages/invoice/invoiceManage", "pages/package/packageVipBuy", "pages/aggrement/user-aggrement", "pages/aggrement/privacy-aggrement", "pages/index/index"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#F8F8F8"}, "tabBar": {"custom": true, "color": "#999999", "selectedColor": "#40a9ff", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/home/<USER>", "text": "首页"}, {"pagePath": "pages/warehouse/warehouse", "text": "厂库"}, {"pagePath": "pages/package/package", "text": "套餐"}, {"pagePath": "pages/mine/mine", "text": "我的"}]}, "requiredPrivateInfos": ["getLocation", "onLocationChange", "startLocationUpdate"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于地图定位"}}, "usingComponents": {}}