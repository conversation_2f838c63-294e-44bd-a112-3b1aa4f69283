"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const current = common_vendor.ref(0);
    const tabs = [
      {
        pagePath: "pages/home/<USER>",
        text: "首页",
        icon: "/static/tabbar/home_off.png",
        selectedIcon: "/static/tabbar/home_on.png"
      },
      {
        pagePath: "pages/warehouse/warehouse",
        text: "场库",
        icon: "/static/tabbar/customer_off.png",
        selectedIcon: "/static/tabbar/customer_on.png"
      },
      {
        pagePath: "pages/package/package",
        text: "套餐",
        icon: "/static/tabbar/find_off.png",
        selectedIcon: "/static/tabbar/find_on.png"
      },
      {
        pagePath: "pages/mine/mine",
        text: "我的",
        icon: "/static/tabbar/work_off.png",
        selectedIcon: "/static/tabbar/work_on.png"
      }
    ];
    const currentPath = common_vendor.computed(() => {
      var _a;
      const pages = getCurrentPages();
      return ((_a = pages[pages.length - 1]) == null ? void 0 : _a.route) || "";
    });
    const isActive = (index) => currentPath.value === tabs[index].pagePath;
    const getIconPath = (index) => {
      return isActive(index) ? tabs[index].selectedIcon : tabs[index].icon;
    };
    const switchTab = (path, index) => {
      if (current.value === index)
        return;
      common_vendor.index.switchTab({
        url: `/${path}`
      });
    };
    const updateCurrent = () => {
      const pages = getCurrentPages();
      if (!pages || pages.length === 0) {
        current.value = 0;
        return;
      }
      const currentPage = pages[pages.length - 1];
      const currentPath2 = currentPage.route;
      const index = tabs.findIndex((item) => item.pagePath === currentPath2);
      current.value = index !== -1 ? index : 0;
    };
    common_vendor.index.$on("tabPageShow", updateCurrent);
    common_vendor.onMounted(() => {
      updateCurrent();
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("tabPageShow", updateCurrent);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(tabs, (item, index, i0) => {
          return {
            a: getIconPath(index),
            b: common_vendor.t(item.text),
            c: index,
            d: isActive(index) ? 1 : "",
            e: common_vendor.o(($event) => switchTab(item.pagePath, index), index)
          };
        }),
        b: common_vendor.gei(_ctx, "")
      };
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/custom-tab-bar/index.js.map
