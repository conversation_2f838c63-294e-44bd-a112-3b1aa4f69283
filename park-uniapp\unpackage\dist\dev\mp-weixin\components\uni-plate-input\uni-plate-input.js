"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "uni-plate-input",
  props: {
    plate: {
      type: String,
      default: ""
    }
  },
  emits: ["close", "typeChange", "export"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const type = common_vendor.ref(1);
    const currentInputIndex = common_vendor.ref(0);
    const currentInputValue = common_vendor.ref(["", "", "", "", "", "", ""]);
    const keyboardHeightInit = common_vendor.ref(false);
    const keyboardHeight = common_vendor.ref("auto");
    const fillBlock = [{ num: 11 }, { num: 12 }, { num: 13 }, { num: 14 }, { num: 15 }, { num: 16 }];
    const provinceText = [
      "粤",
      "京",
      "冀",
      "沪",
      "津",
      "晋",
      "蒙",
      "辽",
      "吉",
      "黑",
      "苏",
      "浙",
      "皖",
      "闽",
      "赣",
      "鲁",
      "豫",
      "鄂",
      "湘",
      "桂",
      "琼",
      "渝",
      "川",
      "贵",
      "云",
      "藏",
      "陕",
      "甘",
      "青",
      "宁",
      "新"
    ];
    const numberText = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"];
    const wordText = [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "J",
      "K",
      "L",
      "M",
      "N",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z"
    ];
    const lastWordText = ["挂", "港", "学", "领", "警"];
    const inputType = common_vendor.computed(() => {
      switch (currentInputIndex.value) {
        case 0:
          return 1;
        case 1:
          return 2;
        case 2:
        case 3:
        case 4:
        case 5:
          return 3;
        case 6:
          return type.value == 2 ? 3 : 4;
        case 7:
          return 4;
        default:
          return 1;
      }
    });
    common_vendor.watch(currentInputIndex, (n, o) => {
      if (!keyboardHeightInit.value)
        return;
      common_vendor.nextTick$1(() => {
        changeKeyboardHeight();
      });
    });
    const typeChange = (e) => {
      emit("typeChange", e.detail.value);
      const { value } = e.detail;
      type.value = parseInt(value);
      currentInputIndex.value = 0;
      if (value == 1) {
        currentInputValue.value = ["", "", "", "", "", "", ""];
      } else {
        currentInputValue.value = ["", "", "", "", "", "", "", ""];
      }
    };
    const inputSwitch = (e) => {
      const { index } = e.currentTarget.dataset;
      currentInputIndex.value = parseInt(index);
    };
    const chooseKey = (e) => {
      const { value } = e.currentTarget.dataset;
      currentInputValue.value[currentInputIndex.value] = value;
      if (type.value == 1 && currentInputIndex.value < 6) {
        currentInputIndex.value++;
      }
      if (type.value == 2 && currentInputIndex.value < 7) {
        currentInputIndex.value++;
      }
    };
    const deleteKey = () => {
      currentInputValue.value[currentInputIndex.value] = "";
      if (currentInputIndex.value != 0)
        currentInputIndex.value--;
    };
    const exportPlate = () => {
      const plate = currentInputValue.value.join("");
      let err = false;
      if (type.value === 1 && plate.length != 7) {
        err = true;
      } else if (type.value === 2 && plate.length != 8) {
        err = true;
      }
      if (err)
        return common_vendor.index.showToast({
          title: "请输入完整的车牌号码",
          icon: "none"
        });
      emit("export", plate);
    };
    const changeKeyboardHeight = () => {
      try {
        setTimeout(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select("#keyboard").boundingClientRect();
          query.exec(function(res) {
            if (res && res[0]) {
              keyboardHeight.value = res[0].height + common_vendor.index.upx2px(30) + "px";
              keyboardHeightInit.value = true;
            }
          });
        }, 50);
      } catch (error) {
        common_vendor.index.__f__("warn", "at components/uni-plate-input/uni-plate-input.vue:217", "changeKeyboardHeight error:", error);
        keyboardHeight.value = "300px";
        keyboardHeightInit.value = true;
      }
    };
    common_vendor.onMounted(() => {
      const plateKey = props.plate.split("");
      if (plateKey.length === 7) {
        type.value = 1;
      } else if (plateKey.length === 8) {
        type.value = 2;
      }
      if (plateKey.length === 7 || plateKey.length === 8) {
        currentInputValue.value = plateKey;
        currentInputIndex.value = plateKey.length - 1;
      }
      setTimeout(() => {
        common_vendor.nextTick$1(() => {
          changeKeyboardHeight();
        });
      }, 500);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: type.value === 1,
        b: type.value === 2,
        c: common_vendor.o(typeChange),
        d: common_vendor.t(currentInputValue.value[0]),
        e: currentInputIndex.value == 0 ? 1 : "",
        f: common_vendor.o(inputSwitch),
        g: common_vendor.t(currentInputValue.value[1]),
        h: currentInputIndex.value == 1 ? 1 : "",
        i: common_vendor.o(inputSwitch),
        j: common_vendor.t(currentInputValue.value[2]),
        k: currentInputIndex.value == 2 ? 1 : "",
        l: common_vendor.o(inputSwitch),
        m: common_vendor.t(currentInputValue.value[3]),
        n: currentInputIndex.value == 3 ? 1 : "",
        o: common_vendor.o(inputSwitch),
        p: common_vendor.t(currentInputValue.value[4]),
        q: currentInputIndex.value == 4 ? 1 : "",
        r: common_vendor.o(inputSwitch),
        s: common_vendor.t(currentInputValue.value[5]),
        t: currentInputIndex.value == 5 ? 1 : "",
        v: common_vendor.o(inputSwitch),
        w: common_vendor.t(currentInputValue.value[6]),
        x: currentInputIndex.value == 6 ? 1 : "",
        y: common_vendor.o(inputSwitch),
        z: type.value == 2
      }, type.value == 2 ? {
        A: common_vendor.t(currentInputValue.value[7]),
        B: currentInputIndex.value == 7 ? 1 : "",
        C: common_vendor.o(inputSwitch)
      } : {}, {
        D: inputType.value == 1
      }, inputType.value == 1 ? {
        E: common_vendor.f(provinceText, (el, k0, i0) => {
          return {
            a: common_vendor.t(el),
            b: el,
            c: el,
            d: common_vendor.o(chooseKey, el)
          };
        })
      } : {}, {
        F: inputType.value == 1
      }, inputType.value == 1 ? {} : {}, {
        G: inputType.value >= 3
      }, inputType.value >= 3 ? {
        H: common_vendor.f(numberText, (el, k0, i0) => {
          return {
            a: common_vendor.t(el),
            b: el,
            c: el,
            d: common_vendor.o(chooseKey, el)
          };
        })
      } : {}, {
        I: inputType.value >= 2
      }, inputType.value >= 2 ? {
        J: common_vendor.f(wordText, (el, k0, i0) => {
          return {
            a: common_vendor.t(el),
            b: el,
            c: el,
            d: common_vendor.o(chooseKey, el)
          };
        })
      } : {}, {
        K: inputType.value == 3
      }, inputType.value == 3 ? {
        L: common_vendor.f(fillBlock, (el, k0, i0) => {
          return {
            a: el.num
          };
        })
      } : {}, {
        M: inputType.value == 4
      }, inputType.value == 4 ? {
        N: common_vendor.f(lastWordText, (el, k0, i0) => {
          return {
            a: common_vendor.t(el),
            b: el,
            c: el,
            d: common_vendor.o(chooseKey, el)
          };
        })
      } : {}, {
        O: inputType.value == 4
      }, inputType.value == 4 ? {} : {}, {
        P: keyboardHeight.value,
        Q: common_vendor.o(($event) => _ctx.$emit("close")),
        R: common_vendor.o(deleteKey),
        S: common_vendor.o(exportPlate),
        T: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a5ab1be4"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/uni-plate-input/uni-plate-input.js.map
