<view class="{{['so-mask', 'data-v-a5ab1be4', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{T}}"><view class="so-plate animation-scale-up data-v-a5ab1be4"><view class="so-plate-head data-v-a5ab1be4"><view class="so-plate-type data-v-a5ab1be4"><radio-group class="u-flex data-v-a5ab1be4" bindchange="{{c}}"><label class="data-v-a5ab1be4"><radio class="data-v-a5ab1be4" value="1" checked="{{a}}"/><view class="name data-v-a5ab1be4">普通车牌</view></label><label class="data-v-a5ab1be4"><radio class="data-v-a5ab1be4" value="2" checked="{{b}}"/><view class="name data-v-a5ab1be4"> 新能源车牌 </view></label></radio-group></view></view><view class="so-plate-body data-v-a5ab1be4"><view class="{{['so-plate-word', 'data-v-a5ab1be4', e && 'active']}}" bindtap="{{f}}" data-index="0"><text class="data-v-a5ab1be4">{{d}}</text></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', h && 'active']}}" bindtap="{{i}}" data-index="1"><text class="data-v-a5ab1be4">{{g}}</text></view><view class="so-plate-dot data-v-a5ab1be4"></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', k && 'active']}}" bindtap="{{l}}" data-index="2"><text class="data-v-a5ab1be4">{{j}}</text></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', n && 'active']}}" bindtap="{{o}}" data-index="3"><text class="data-v-a5ab1be4">{{m}}</text></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', q && 'active']}}" bindtap="{{r}}" data-index="4"><text class="data-v-a5ab1be4">{{p}}</text></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', t && 'active']}}" bindtap="{{v}}" data-index="5"><text class="data-v-a5ab1be4">{{s}}</text></view><view class="{{['so-plate-word', 'data-v-a5ab1be4', x && 'active']}}" bindtap="{{y}}" data-index="6"><text class="data-v-a5ab1be4">{{w}}</text></view><view wx:if="{{z}}" class="{{['so-plate-word', 'data-v-a5ab1be4', B && 'active']}}" bindtap="{{C}}" data-index="7"><text class="data-v-a5ab1be4">{{A}}</text></view></view><view class="so-plate-foot data-v-a5ab1be4"><view class="so-plate-keyboard data-v-a5ab1be4" style="{{'height:' + P}}"><view class="data-v-a5ab1be4" id="keyboard"><block wx:if="{{D}}"><view wx:for="{{E}}" wx:for-item="el" wx:key="b" hover-class="hover" class="so-plate-key data-v-a5ab1be4" data-value="{{el.c}}" bindtap="{{el.d}}">{{el.a}}</view></block><block wx:if="{{F}}"><text class="so-plate-key fill-block data-v-a5ab1be4"></text></block><block wx:if="{{G}}"><view wx:for="{{H}}" wx:for-item="el" wx:key="b" hover-class="hover" class="so-plate-key data-v-a5ab1be4" data-value="{{el.c}}" bindtap="{{el.d}}">{{el.a}}</view></block><block wx:if="{{I}}"><view wx:for="{{J}}" wx:for-item="el" wx:key="b" hover-class="hover" class="so-plate-key data-v-a5ab1be4" data-value="{{el.c}}" bindtap="{{el.d}}">{{el.a}}</view></block><block wx:if="{{K}}"><text wx:for="{{L}}" wx:for-item="el" wx:key="a" class="so-plate-key fill-block data-v-a5ab1be4"></text></block><block wx:if="{{M}}"><view wx:for="{{N}}" wx:for-item="el" wx:key="b" hover-class="hover" class="so-plate-key data-v-a5ab1be4" data-value="{{el.c}}" bindtap="{{el.d}}">{{el.a}}</view></block><text wx:if="{{O}}" class="so-plate-key fill-block data-v-a5ab1be4"></text></view></view><view class="so-plate-btn-group data-v-a5ab1be4"><view class="data-v-a5ab1be4"><button class="so-plate-btn so-plate-btn--cancel data-v-a5ab1be4" bindtap="{{Q}}">取消</button></view><view class="data-v-a5ab1be4"><button class="so-plate-btn so-plate-btn--delete data-v-a5ab1be4" bindtap="{{R}}">删除</button><button class="so-plate-btn so-plate-btn--submit data-v-a5ab1be4" bindtap="{{S}}">完成</button></view></view></view></view><view class="safe-area-inset-bottom data-v-a5ab1be4"></view></view>