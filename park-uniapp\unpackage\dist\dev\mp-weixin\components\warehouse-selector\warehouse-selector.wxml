<view class="{{['data-v-e7c75adf', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{g}}"><up-popup wx:if="{{f}}" class="data-v-e7c75adf" virtualHostClass="data-v-e7c75adf" u-s="{{['d']}}" bindclose="{{e}}" u-i="e7c75adf-0" bind:__l="__l" u-p="{{f}}"><view class="warehouse-popup data-v-e7c75adf"><view class="popup-header data-v-e7c75adf"><text class="popup-title data-v-e7c75adf">选择场库</text><up-icon wx:if="{{b}}" class="data-v-e7c75adf" virtualHostClass="data-v-e7c75adf" bindclick="{{a}}" u-i="e7c75adf-1,e7c75adf-0" bind:__l="__l" u-p="{{b}}"></up-icon></view><scroll-view class="warehouse-list data-v-e7c75adf" scroll-y style="{{'max-height:' + d}}"><view wx:for="{{c}}" wx:for-item="warehouse" wx:key="e" class="{{['warehouse-item', 'data-v-e7c75adf', warehouse.f && 'active']}}" bindtap="{{warehouse.g}}"><text class="warehouse-item-name data-v-e7c75adf">{{warehouse.a}}</text><up-icon wx:if="{{warehouse.b}}" class="data-v-e7c75adf" virtualHostClass="data-v-e7c75adf" u-i="{{warehouse.c}}" bind:__l="__l" u-p="{{warehouse.d}}"></up-icon></view></scroll-view></view></up-popup></view>