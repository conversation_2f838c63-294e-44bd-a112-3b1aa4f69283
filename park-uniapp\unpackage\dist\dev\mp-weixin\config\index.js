"use strict";
const common_vendor = require("../common/vendor.js");
const ENV_CONFIG = {
  // 开发环境（本地调试）
  develop: {
    BASE_URL: "http://localhost:8080",
    // BASE_URL: 'http://**************:8080',
    API_PREFIX: ""
  },
  // 测试环境
  trial: {
    BASE_URL: "https://test-parknew.lgfw24hours.com:3443",
    API_PREFIX: "/api"
  },
  // 生产环境
  release: {
    BASE_URL: "https://test-parknew.lgfw24hours.com:3443",
    API_PREFIX: "/api"
  }
};
const env = common_vendor.wx$1.getAccountInfoSync().miniProgram.envVersion || "develop";
const FULL_URL = `${ENV_CONFIG[env].BASE_URL}${ENV_CONFIG[env].API_PREFIX}`;
const URL = FULL_URL;
exports.URL = URL;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/index.js.map
