<view wx:if="{{a}}" style="{{j + ';' + virtualHostStyle}}" class="{{['u-empty', 'data-v-bd84101d', virtualHostClass]}}" hidden="{{virtualHostHidden || false}}" id="{{k}}"><u-icon wx:if="{{b}}" class="data-v-bd84101d" virtualHostClass="data-v-bd84101d" u-i="bd84101d-0" bind:__l="__l" u-p="{{c}}"></u-icon><image wx:else class="data-v-bd84101d" style="{{'width:' + d + ';' + ('height:' + e)}}" src="{{f}}" mode="widthFix"></image><text class="u-empty__text data-v-bd84101d" style="{{h}}">{{g}}</text><view wx:if="{{i}}" class="u-empty__wrap data-v-bd84101d"><slot/></view></view>