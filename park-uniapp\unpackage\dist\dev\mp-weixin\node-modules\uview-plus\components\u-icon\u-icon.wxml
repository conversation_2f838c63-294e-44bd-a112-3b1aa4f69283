<view bindtap="{{s}}" class="{{['u-icon', 'data-v-1c933a9a', t, virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{v}}"><image wx:if="{{a}}" class="u-icon__img data-v-1c933a9a" src="{{b}}" mode="{{c}}" style="{{d + ';' + e}}"></image><text wx:else class="{{['u-icon__icon', 'data-v-1c933a9a', g]}}" style="{{h + ';' + i}}" hover-class="{{j}}">{{f}}</text><text wx:if="{{k}}" class="u-icon__label data-v-1c933a9a" style="{{'color:' + m + ';' + ('font-size:' + n) + ';' + ('margin-left:' + o) + ';' + ('margin-top:' + p) + ';' + ('margin-right:' + q) + ';' + ('margin-bottom:' + r)}}">{{l}}</text></view>