<view class="{{['u-popup', 'data-v-74921bef', q, virtualHostClass]}}" style="{{'width:' + r + ';' + ('height:' + s) + ';' + virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{t}}"><view class="u-popup__trigger data-v-74921bef"><slot name="trigger"></slot><view bindtap="{{a}}" class="u-popup__trigger__cover data-v-74921bef"></view></view><u-overlay wx:if="{{b}}" class="data-v-74921bef" virtualHostClass="data-v-74921bef" bindclick="{{c}}" u-i="74921bef-0" bind:__l="__l" u-p="{{d}}"></u-overlay><u-transition wx:if="{{p}}" class="data-v-74921bef" virtualHostClass="data-v-74921bef" u-s="{{['d']}}" bindafterEnter="{{n}}" bindclick="{{o}}" u-i="74921bef-1" bind:__l="__l" u-p="{{p}}"><view class="u-popup__content data-v-74921bef" style="{{k}}" catchtap="{{l}}" catchtouchmove="{{m}}"><u-status-bar wx:if="{{e}}" class="data-v-74921bef" virtualHostClass="data-v-74921bef" u-i="74921bef-2,74921bef-1" bind:__l="__l"></u-status-bar><slot></slot><view wx:if="{{f}}" catchtap="{{h}}" class="{{['u-popup__content__close', 'data-v-74921bef', i]}}" hover-class="u-popup__content__close--hover" hover-stay-time="150"><u-icon wx:if="{{g}}" class="data-v-74921bef" virtualHostClass="data-v-74921bef" u-i="74921bef-3,74921bef-1" bind:__l="__l" u-p="{{g}}"></u-icon></view><u-safe-bottom wx:if="{{j}}" class="data-v-74921bef" virtualHostClass="data-v-74921bef" u-i="74921bef-4,74921bef-1" bind:__l="__l"></u-safe-bottom></view><slot name="bottom"></slot></u-transition></view>