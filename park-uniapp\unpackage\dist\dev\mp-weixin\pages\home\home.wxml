<view class="{{['home-container', 'data-v-07e72d3c', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{A}}"><view class="map-container data-v-07e72d3c"><map class="data-v-07e72d3c" id="myMap" style="{{'width:' + '100%' + ';' + ('height:' + '85%')}}" latitude="{{a}}" longitude="{{b}}" scale="{{c}}" markers="{{d}}" show-location="true"></map><view class="map-controls data-v-07e72d3c" style="{{i}}"><view class="{{['control-button', 'data-v-07e72d3c', e && 'active']}}" bindtap="{{f}}"><view class="button-text-container data-v-07e72d3c"><text class="button-text data-v-07e72d3c">停车</text><text class="button-text data-v-07e72d3c">场库</text></view></view><view class="{{['control-button', 'data-v-07e72d3c', g && 'active']}}" bindtap="{{h}}"><view class="button-text-container data-v-07e72d3c"><text class="button-text data-v-07e72d3c">充电</text><text class="button-text data-v-07e72d3c">场库</text></view></view></view><view class="location-control data-v-07e72d3c" style="{{k}}" bindtap="{{l}}"><image class="data-v-07e72d3c" src="{{j}}" mode="widthFix" style="width:38rpx;height:38rpx"></image></view></view><view class="{{['floating-overlay', 'data-v-07e72d3c', y]}}" style="{{z}}"><view class="drag-handle-container data-v-07e72d3c" bindtouchstart="{{m}}" bindtouchmove="{{n}}" bindtouchend="{{o}}" bindtouchcancel="{{p}}"><view class="drag-handle-bar data-v-07e72d3c"></view></view><view class="buttons-container data-v-07e72d3c"><view class="action-button data-v-07e72d3c" bindtap="{{r}}"><image class="data-v-07e72d3c" src="{{q}}" mode="widthFix" style="width:100rpx;height:100rpx"></image><text class="button-text data-v-07e72d3c">停车缴费</text></view><view class="action-button data-v-07e72d3c" bindtap="{{t}}"><image class="data-v-07e72d3c" src="{{s}}" mode="widthFix" style="width:100rpx;height:100rpx"></image><text class="button-text data-v-07e72d3c">扫码充电</text></view></view><view class="{{['overlay-content', 'data-v-07e72d3c', x]}}"><view class="{{['banner-section', 'data-v-07e72d3c', w]}}"><up-swiper wx:if="{{v}}" class="data-v-07e72d3c" virtualHostClass="data-v-07e72d3c" u-i="07e72d3c-0" bind:__l="__l" u-p="{{v}}"></up-swiper></view></view></view><custom-tab-bar class="data-v-07e72d3c" virtualHostClass="data-v-07e72d3c" u-i="07e72d3c-1" bind:__l="__l"></custom-tab-bar></view>