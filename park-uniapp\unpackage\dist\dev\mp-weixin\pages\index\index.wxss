
.container.data-v-1cf27b2a {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}
.header.data-v-1cf27b2a {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 60rpx;
}
.logo.data-v-1cf27b2a {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background-color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.app-name.data-v-1cf27b2a {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.version.data-v-1cf27b2a {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}
.content.data-v-1cf27b2a {
  flex: 1;
  text-align: center;
}
.maintenance-icon.data-v-1cf27b2a {
  font-size: 100rpx;
  margin-bottom: 40rpx;
  animation: rotate-1cf27b2a 3s linear infinite;
}
@keyframes rotate-1cf27b2a {
from { transform: rotate(0deg);
}
to { transform: rotate(360deg);
}
}
.title.data-v-1cf27b2a {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.subtitle.data-v-1cf27b2a {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  opacity: 0.9;
  line-height: 1.5;
}
.description.data-v-1cf27b2a {
  text-align: left;
  margin: 40rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.feature.data-v-1cf27b2a {
  display: block;
  margin: 15rpx 0;
  font-size: 26rpx;
  line-height: 1.4;
}
.time-info.data-v-1cf27b2a {
  font-size: 24rpx;
  opacity: 0.8;
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 15rpx;
}
.button-area.data-v-1cf27b2a {
  margin: 60rpx 0 40rpx 0;
}
.main-btn.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  padding: 20rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.main-btn.data-v-1cf27b2a:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}
.secondary-btn.data-v-1cf27b2a {
  background: transparent;
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  font-size: 28rpx;
  padding: 15rpx;
}
.secondary-btn.data-v-1cf27b2a:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}
.footer.data-v-1cf27b2a {
  text-align: center;
  opacity: 0.7;
  margin-top: 40rpx;
}
.contact-info.data-v-1cf27b2a {
  display: block;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}
.company-info.data-v-1cf27b2a {
  font-size: 20rpx;
  opacity: 0.6;
}
