/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.invoice-manage.data-v-4863d9d0 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.invoice-content.data-v-4863d9d0 {
  padding: 32rpx;
}
.invoice-list .invoice-item.data-v-4863d9d0 {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.invoice-list .invoice-item.last-item.data-v-4863d9d0 {
  margin-bottom: 0;
}
.invoice-list .invoice-item .item-header.data-v-4863d9d0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.invoice-list .invoice-item .item-header .order-type.data-v-4863d9d0 {
  display: flex;
  align-items: center;
}
.invoice-list .invoice-item .item-header .order-type image.data-v-4863d9d0 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.invoice-list .invoice-item .item-header .order-type .type-name.data-v-4863d9d0 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.invoice-list .invoice-item .item-header .status-badge.data-v-4863d9d0 {
  font-size: 26rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(59, 130, 246, 0.1);
}
.invoice-list .invoice-item .item-content.data-v-4863d9d0 {
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
}
.invoice-list .invoice-item .item-content .info-row.data-v-4863d9d0 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.invoice-list .invoice-item .item-content .info-row.data-v-4863d9d0:last-child {
  margin-bottom: 0;
}
.invoice-list .invoice-item .item-content .info-row .info-label.data-v-4863d9d0 {
  font-size: 26rpx;
  color: #666666;
  min-width: 140rpx;
}
.invoice-list .invoice-item .item-content .info-row .info-value.data-v-4863d9d0 {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}
.invoice-list .invoice-item .item-content .info-row .info-value.error.data-v-4863d9d0 {
  color: #ef4444;
}
.invoice-list .invoice-item .item-footer.data-v-4863d9d0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.invoice-list .invoice-item .item-footer .issue-time.data-v-4863d9d0 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
}
.invoice-list .invoice-item .item-footer .issue-time text.data-v-4863d9d0 {
  margin-left: 8rpx;
}
.invoice-list .invoice-item .item-footer .invoice-amount.data-v-4863d9d0 {
  display: flex;
  align-items: center;
}
.invoice-list .invoice-item .item-footer .invoice-amount .amount-label.data-v-4863d9d0 {
  font-size: 24rpx;
  color: #3b82f6;
}
.invoice-list .invoice-item .item-footer .invoice-amount .amount-value.data-v-4863d9d0 {
  font-size: 28rpx;
  font-weight: bold;
  color: #3b82f6;
  margin-left: 8rpx;
}
.empty-state.data-v-4863d9d0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
  margin-top: 60rpx;
}