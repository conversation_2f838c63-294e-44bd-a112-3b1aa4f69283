"use strict";
const common_vendor = require("../../common/vendor.js");
const api_invoice = require("../../api/invoice.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_popup2 = common_vendor.resolveComponent("up-popup");
  (_easycom_up_icon2 + _easycom_up_input2 + _easycom_up_loading_icon2 + _easycom_up_popup2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_input = () => "../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_loading_icon = () => "../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_input + _easycom_up_loading_icon + _easycom_up_popup)();
}
const _sfc_main = {
  __name: "openInvoice",
  setup(__props) {
    const showPopup = common_vendor.ref(false);
    const invoiceTitleList = common_vendor.ref([]);
    const invoiceInfo = common_vendor.reactive({});
    const money = common_vendor.ref(null);
    const functionId = common_vendor.ref(null);
    const functionType = common_vendor.ref(null);
    const email = common_vendor.ref("");
    const isLoading = common_vendor.ref(false);
    const isResume = common_vendor.ref(false);
    const invoiceId = common_vendor.ref("");
    common_vendor.onShow(() => {
      fetchInvoiceTitleList();
    });
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/invoice/openInvoice.vue:183", options);
      money.value = options.money;
      functionId.value = options.functionId;
      functionType.value = options.functionType;
      if (options.invoiceId) {
        invoiceId.value = options.invoiceId;
      }
      if (options.isResume) {
        isResume.value = true;
      } else {
        isResume.value = false;
      }
    });
    const handleAddInvoice = async () => {
      if (!functionId.value) {
        common_vendor.index.showToast({
          title: "订单id不存在~",
          icon: "none"
        });
        return;
      }
      if (!functionType.value) {
        common_vendor.index.showToast({
          title: "功能类型不存在~",
          icon: "none"
        });
        return;
      }
      if (!invoiceInfo.invoiceTitleContent) {
        common_vendor.index.showToast({
          title: "请选择抬头~",
          icon: "none"
        });
        return;
      }
      if (!email.value) {
        common_vendor.index.showToast({
          title: "电子邮箱必填~",
          icon: "none"
        });
        return;
      }
      const params = {
        invoiceType: invoiceInfo.invoiceType,
        functionType: functionType.value,
        functionId: functionId.value,
        invoiceTitleContent: invoiceInfo.invoiceTitleContent,
        unitDutyParagraph: invoiceInfo.unitDutyParagraph,
        registerAddress: invoiceInfo.registerAddress,
        registerPhone: invoiceInfo.registerPhone,
        depositBank: invoiceInfo.depositBank,
        bankAccount: invoiceInfo.bankAccount,
        notifyEmail: email.value,
        titleId: invoiceInfo.id,
        id: invoiceId.value
      };
      common_vendor.index.__f__("log", "at pages/invoice/openInvoice.vue:247", params);
      isLoading.value = true;
      try {
        const apiCall = isResume.value ? api_invoice.postResumeInvoice : api_invoice.postSaveInvoiceRecord;
        const res = await apiCall(params);
        common_vendor.index.showModal({
          content: res.msg,
          showCancel: false,
          success: (res2) => {
            if (res2.confirm) {
              common_vendor.index.navigateBack();
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/invoice/openInvoice.vue:265", "开票失败:", error);
        common_vendor.index.showToast({
          title: "开票失败，请重试",
          icon: "none"
        });
      } finally {
        isLoading.value = false;
      }
    };
    const fetchInvoiceTitleList = async () => {
      try {
        const res = await api_invoice.getInvoiceTitleList();
        invoiceTitleList.value = res.data || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/invoice/openInvoice.vue:281", "获取发票抬头列表失败:", error);
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      }
    };
    const chooseInvoice = (item) => {
      showPopup.value = false;
      Object.assign(invoiceInfo, item);
    };
    const handleEdit = (item) => {
      const obj = JSON.stringify(item);
      common_vendor.index.navigateTo({
        url: "/pages/invoice/addInvoiceTitle?isEdit=true&obj=" + encodeURIComponent(obj)
      });
    };
    const addInvoiceTitle = () => {
      common_vendor.index.navigateTo({
        url: "/pages/invoice/addInvoiceTitle?isEdit=false"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(invoiceInfo.id ? invoiceInfo.invoiceType === 1 ? "专票" : invoiceInfo.titleType === 1 ? "公司" : "个人" : "请选择抬头"),
        b: common_vendor.p({
          name: "arrow-right",
          color: "#BDBDBD",
          size: "16"
        }),
        c: common_vendor.o(($event) => showPopup.value = true),
        d: common_vendor.t(invoiceInfo.invoiceTitleContent || ""),
        e: common_vendor.t(money.value || "0"),
        f: invoiceInfo.titleType === 1 || invoiceInfo.invoiceType === 1
      }, invoiceInfo.titleType === 1 || invoiceInfo.invoiceType === 1 ? {
        g: common_vendor.t(invoiceInfo.unitDutyParagraph || ""),
        h: common_vendor.t(invoiceInfo.registerAddress || ""),
        i: common_vendor.t(invoiceInfo.registerPhone || ""),
        j: common_vendor.t(invoiceInfo.depositBank || ""),
        k: common_vendor.t(invoiceInfo.bankAccount || "")
      } : {}, {
        l: common_vendor.o(($event) => email.value = $event),
        m: common_vendor.p({
          border: "none",
          placeholder: "请输入您的电子邮箱",
          clearable: true,
          fontSize: "28rpx",
          color: "#616161",
          placeholderStyle: {
            color: "#616161"
          },
          modelValue: email.value
        }),
        n: isLoading.value
      }, isLoading.value ? {
        o: common_vendor.p({
          color: "#ffffff",
          size: "20"
        })
      } : {}, {
        p: !isLoading.value
      }, !isLoading.value ? {
        q: common_vendor.t(isResume.value ? "发票重开" : "提交开票")
      } : {}, {
        r: common_vendor.o(handleAddInvoice),
        s: isLoading.value ? 1 : "",
        t: common_vendor.f(invoiceTitleList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: item.invoiceType === 1
          }, item.invoiceType === 1 ? {
            b: "b7ea1ec7-4-" + i0 + ",b7ea1ec7-3",
            c: common_vendor.p({
              name: "file-text",
              color: "#4BA1FC",
              size: "16"
            })
          } : common_vendor.e({
            d: item.titleType === 1
          }, item.titleType === 1 ? {
            e: "b7ea1ec7-5-" + i0 + ",b7ea1ec7-3",
            f: common_vendor.p({
              name: "home",
              color: "#FF9500",
              size: "16"
            })
          } : {
            g: "b7ea1ec7-6-" + i0 + ",b7ea1ec7-3",
            h: common_vendor.p({
              name: "account",
              color: "#34C759",
              size: "16"
            })
          }), {
            i: common_vendor.t(item.invoiceTitleContent || "-"),
            j: common_vendor.t(item.unitDutyParagraph || ""),
            k: "b7ea1ec7-7-" + i0 + ",b7ea1ec7-3",
            l: common_vendor.o(($event) => handleEdit(item), item.id),
            m: item.id,
            n: common_vendor.o(($event) => chooseInvoice(item), item.id)
          });
        }),
        v: common_vendor.p({
          name: "edit-pen",
          color: "#9e9e9e",
          size: "14"
        }),
        w: common_vendor.p({
          name: "plus",
          color: "#ffffff",
          size: "16"
        }),
        x: common_vendor.o(addInvoiceTitle),
        y: common_vendor.o(($event) => showPopup.value = false),
        z: common_vendor.p({
          show: showPopup.value,
          safeAreaInsetBottom: false,
          round: 20,
          closeOnClickOverlay: true
        }),
        A: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b7ea1ec7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/invoice/openInvoice.js.map
