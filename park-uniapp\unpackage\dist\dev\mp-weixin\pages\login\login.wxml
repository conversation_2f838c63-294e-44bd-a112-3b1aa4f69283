<view class="{{['login-container', 'data-v-e4e4508d', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{z}}"><view class="custom-navbar data-v-e4e4508d" style="{{'padding-top:' + c + ';' + ('height:' + d)}}"><view class="nav-back data-v-e4e4508d" bindtap="{{b}}"><up-icon wx:if="{{a}}" class="data-v-e4e4508d" virtualHostClass="data-v-e4e4508d" u-i="e4e4508d-0" bind:__l="__l" u-p="{{a}}"></up-icon></view></view><view class="login-header data-v-e4e4508d"><image src="{{e}}" mode="aspectFill" class="login-header-image data-v-e4e4508d"/></view><view class="login-content data-v-e4e4508d"><view class="login-form data-v-e4e4508d"><view class="login-form-phone data-v-e4e4508d"><text class="login-form-text data-v-e4e4508d">手机号</text><text class="login-form-text data-v-e4e4508d">|</text><input type="text" placeholder="请输入手机号" class="login-form-input data-v-e4e4508d" maxlength="11" value="{{f}}" bindinput="{{g}}"/></view><view class="login-form-code data-v-e4e4508d"><text class="login-form-text data-v-e4e4508d">验证码</text><text class="login-form-text data-v-e4e4508d">|</text><input type="text" placeholder="请输入验证码" class="login-form-input data-v-e4e4508d" maxlength="6" value="{{h}}" bindinput="{{i}}"/><button class="login-form-code-button data-v-e4e4508d" bindtap="{{k}}" disabled="{{l}}">{{j}}</button></view></view><view class="login-button data-v-e4e4508d" bindtap="{{m}}"><text class="login-button-text data-v-e4e4508d">登录</text></view><view class="agreement data-v-e4e4508d"><view class="{{['checkbox', 'data-v-e4e4508d', p && 'checked']}}" bindtap="{{q}}"><up-icon wx:if="{{n}}" class="data-v-e4e4508d" virtualHostClass="data-v-e4e4508d" u-i="e4e4508d-1" bind:__l="__l" u-p="{{o}}"></up-icon></view><text class="agreement-text data-v-e4e4508d">我已阅读并同意</text><text class="agreement-link data-v-e4e4508d" bindtap="{{r}}">《用户协议》</text><text class="agreement-text data-v-e4e4508d">和</text><text class="agreement-link data-v-e4e4508d" bindtap="{{s}}">《隐私政策》</text></view><button wx:if="{{t}}" class="wechat-login data-v-e4e4508d" bindtap="{{w}}"><view class="wechat-icon data-v-e4e4508d"><up-icon wx:if="{{v}}" class="data-v-e4e4508d" virtualHostClass="data-v-e4e4508d" u-i="e4e4508d-2" bind:__l="__l" u-p="{{v}}"></up-icon></view></button><button wx:else class="wechat-login data-v-e4e4508d" open-type="getPhoneNumber" bindgetphonenumber="{{y}}"><view class="wechat-icon data-v-e4e4508d"><up-icon wx:if="{{x}}" class="data-v-e4e4508d" virtualHostClass="data-v-e4e4508d" u-i="e4e4508d-3" bind:__l="__l" u-p="{{x}}"></up-icon></view></button></view></view>