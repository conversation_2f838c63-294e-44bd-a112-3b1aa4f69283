"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_car = require("../../api/car.js");
const api_specialUser = require("../../api/specialUser.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  _easycom_u_icon2();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  (_easycom_u_icon + CustomTabBar)();
}
const CustomTabBar = () => "../../components/custom-tab-bar/index.js";
const _sfc_main = {
  __name: "mine",
  setup(__props) {
    const userInfo = common_vendor.ref(null);
    const isLogin = common_vendor.ref(false);
    const defaultCar = common_vendor.ref(null);
    const specialUser = common_vendor.ref({});
    const initUserData = () => {
      userInfo.value = common_vendor.index.getStorageSync("wxUser");
      common_vendor.index.__f__("log", "at pages/mine/mine.vue:80", userInfo.value);
      if (common_vendor.index.getStorageSync("token")) {
        isLogin.value = true;
      } else {
        isLogin.value = false;
      }
      if (isLogin.value) {
        api_car.getCarList().then((res) => {
          var _a;
          common_vendor.index.__f__("log", "at pages/mine/mine.vue:89", res);
          defaultCar.value = ((_a = res.data) == null ? void 0 : _a.length) > 0 ? res.data[0].plateNo : "--";
        });
        api_specialUser.getSpecialUser().then((res) => {
          specialUser.value = res.data || {};
          common_vendor.index.__f__("log", "at pages/mine/mine.vue:96", "specialUser:", specialUser.value);
        });
      } else {
        defaultCar.value = "--";
        specialUser.value = {};
      }
    };
    common_vendor.onShow(() => {
      initUserData();
    });
    const judgeUserType = (userType) => {
      if (userType === "VIP客户") {
        return "VIP客户";
      } else if (userType === "集团客户") {
        return "集团客户";
      } else {
        return "普通用户";
      }
    };
    const getTagClass = (userType) => {
      if (userType === "VIP客户") {
        return "tag-vip";
      } else if (userType === "集团客户") {
        return "tag-enterprise";
      } else {
        return "tag-normal";
      }
    };
    const functionList = common_vendor.ref([
      // {
      //     name: '充电订单',
      //     icon: '/static/mine/chargeOrder.png',
      //     path: '/pages/chargeOrder'
      // },
      {
        name: "抬头管理",
        icon: "/static/mine/invoiceTitle​.png",
        path: "/pages/invoice/invoiceTitle"
      },
      {
        name: "停车发票",
        icon: "/static/mine/invoiceManage.png",
        path: "/pages/invoice/invoiceManage"
      },
      // {
      //     name: '充电开票',
      //     icon: '/static/mine/chargeInvoice.png',
      //     path: '/pages/chargeInvoice'
      // },
      // {
      //     name: '帮人购买',
      //     icon: '/static/mine/vipGift.png',
      //     path: '/pages/vipGift'
      // },
      // {
      //     name: '我的卡包',
      //     icon: '/static/mine/​​couponMine.png',
      //     path: '/pages/couponMine'
      // },
      // {
      //     name: '用户设置',
      //     icon: '/static/mine/userSet.png',
      //     path: '/pages/userSet'
      // },
      {
        name: "临停订单",
        icon: "/static/mine/chargeOrder.png",
        path: "/pages/parkingOrder/parkingOrder"
      },
      {
        name: "停车套餐订单",
        icon: "/static/mine/chargeOrder.png",
        path: "/pages/package/packageRecord"
      },
      {
        name: "退出登录",
        icon: "/static/mine/logout.png",
        action: "logout"
      }
    ]);
    const routePage = (url) => {
      common_vendor.index.navigateTo({
        url
      });
    };
    const showDevelopingTip = () => {
      common_vendor.index.showToast({
        title: "开发中...",
        icon: "none",
        duration: 2e3
      });
    };
    const routeEdit = () => {
      common_vendor.index.navigateTo({
        url: "/pages/mine/mineEdit"
      });
    };
    const handleFunctionClick = (item) => {
      if (item.action === "logout") {
        if (isLogin.value) {
          common_vendor.index.showModal({
            content: "确定要退出登录吗？",
            cancelText: "取消",
            confirmText: "确认",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.removeStorageSync("token");
                common_vendor.index.removeStorageSync("wxUser");
                common_vendor.index.removeStorageSync("currentWarehouse");
                common_vendor.index.showToast({
                  title: "退出登录成功",
                  duration: 2e3
                });
                initUserData();
              }
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "温馨提示",
            content: "您还未登录，是否先去登录？",
            cancelText: "取消",
            confirmText: "确定",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.navigateTo({
                  url: "/pages/login/login"
                });
              }
            }
          });
        }
      } else if (item.path) {
        routePage(item.path);
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userInfo.value && userInfo.value.img ? userInfo.value.img : "/static/mine/avatar.png",
        b: common_vendor.t(userInfo.value && userInfo.value.nickName ? userInfo.value.nickName : "未登录"),
        c: specialUser.value && specialUser.value.userType
      }, specialUser.value && specialUser.value.userType ? {
        d: common_vendor.t(judgeUserType(specialUser.value.userType)),
        e: common_vendor.n(getTagClass(specialUser.value.userType))
      } : {}, {
        f: common_vendor.p({
          name: "arrow-right",
          size: "24",
          color: "#666666"
        }),
        g: common_vendor.o(routeEdit),
        h: common_vendor.o(showDevelopingTip),
        i: common_assets._imports_0$3,
        j: common_vendor.t(defaultCar.value == null ? "--" : defaultCar.value),
        k: common_vendor.o(($event) => routePage("/pages/myCar/myCar")),
        l: common_vendor.f(functionList.value, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: index,
            d: common_vendor.o(($event) => handleFunctionClick(item), index)
          };
        }),
        m: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7c2ebfa5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/mine.js.map
