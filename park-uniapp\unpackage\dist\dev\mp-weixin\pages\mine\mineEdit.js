"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const utils_utils = require("../../utils/utils.js");
const _sfc_main = {
  __name: "mineEdit",
  setup(__props) {
    const userInfo = common_vendor.ref(null);
    const isLogin = common_vendor.ref(false);
    const isAvatarChanged = common_vendor.ref(false);
    const userEdit = common_vendor.ref({
      img: "",
      nickName: "",
      phoneNumber: "",
      userType: 0
    });
    common_vendor.onLoad(() => {
      userInfo.value = common_vendor.index.getStorageSync("wxUser");
      if (userInfo.value) {
        userEdit.value.img = userInfo.value.img || "";
        userEdit.value.nickName = userInfo.value.nickName || "";
        userEdit.value.phoneNumber = userInfo.value.phoneNumber || "";
        userEdit.value.userType = userInfo.value.userType || 0;
      }
      if (common_vendor.index.getStorageSync("token")) {
        isLogin.value = true;
      }
    });
    const chooseavatar = (e) => {
      common_vendor.index.__f__("log", "at pages/mine/mineEdit.vue:86", e);
      const avatarUrl = e.detail.avatarUrl;
      if (avatarUrl) {
        userEdit.value.img = avatarUrl;
        isAvatarChanged.value = true;
      }
    };
    const getPhoneNumber = (e) => {
      const phoneCode = e.detail.code;
      if (phoneCode) {
        api_user.getPhoneNumberByCode(phoneCode).then((res) => {
          userEdit.value.phoneNumber = res.data;
        });
      } else {
        common_vendor.index.__f__("log", "at pages/mine/mineEdit.vue:103", "验证手机号失败");
      }
    };
    const handlePhoneEdit = () => {
      const userType = userEdit.value.userType;
      if (userType === 1) {
        common_vendor.index.showModal({
          title: "温馨提示",
          content: "集团客户修改手机号，请联系客服",
          showCancel: false,
          confirmText: "我知道了"
        });
      } else if (userType === 2) {
        common_vendor.index.showModal({
          title: "温馨提示",
          content: "VIP客户修改手机号，请联系客服",
          showCancel: false,
          confirmText: "我知道了"
        });
      } else {
        common_vendor.index.showModal({
          title: "温馨提示",
          content: "当前用户类型无法修改手机号，请联系客服",
          showCancel: false,
          confirmText: "我知道了"
        });
      }
    };
    const handleSave = async () => {
      const cachedUserInfo = common_vendor.index.getStorageSync("wxUser") || {};
      const hasChanges = userEdit.value.img && userEdit.value.img !== cachedUserInfo.img || userEdit.value.nickName !== cachedUserInfo.nickName || userEdit.value.phoneNumber !== cachedUserInfo.phoneNumber;
      if (!hasChanges) {
        common_vendor.index.showToast({
          title: "信息未更新",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中...",
        mask: true
      });
      try {
        let finalImgUrl = userEdit.value.img;
        if (isAvatarChanged.value && userEdit.value.img && (userEdit.value.img.startsWith("wxfile://") || userEdit.value.img.includes("tmp"))) {
          try {
            const uploadResult = await utils_utils.uploadAvatar(userEdit.value.img);
            finalImgUrl = uploadResult;
            common_vendor.index.__f__("log", "at pages/mine/mineEdit.vue:173", "头像上传成功:", finalImgUrl);
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/mine/mineEdit.vue:175", "头像上传失败:", error);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "头像上传失败",
              icon: "none",
              duration: 2e3
            });
            return;
          }
        }
        const saveData = {
          img: finalImgUrl,
          nickName: userEdit.value.nickName,
          phoneNumber: userEdit.value.phoneNumber,
          userName: userEdit.value.phoneNumber
        };
        common_vendor.index.__f__("log", "at pages/mine/mineEdit.vue:193", saveData);
        const res = await api_user.updateUserInfo(saveData);
        common_vendor.index.setStorageSync("token", res.data.token);
        common_vendor.index.setStorageSync("wxUser", res.data.wxUser);
        common_vendor.index.hideLoading();
        isAvatarChanged.value = false;
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/mineEdit.vue:217", "保存失败:", error);
        common_vendor.index.hideLoading();
        const errorMsg = error.msg || error.message || "保存失败，请重试";
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2e3
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userEdit.value.img || "/static/mine/avatar.png",
        b: common_vendor.o(chooseavatar),
        c: userEdit.value.nickName,
        d: common_vendor.o(($event) => userEdit.value.nickName = $event.detail.value),
        e: userEdit.value.userType === 0 || userEdit.value.userType === "0"
      }, userEdit.value.userType === 0 || userEdit.value.userType === "0" ? {
        f: common_vendor.t(userEdit.value.phoneNumber || "请选择手机号码"),
        g: common_vendor.o(getPhoneNumber),
        h: common_vendor.o(() => {
        })
      } : {
        i: common_vendor.t(userEdit.value.phoneNumber || "请选择手机号码"),
        j: common_vendor.o(handlePhoneEdit)
      }, {
        k: common_vendor.o(handleSave),
        l: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-756671fa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/mineEdit.js.map
