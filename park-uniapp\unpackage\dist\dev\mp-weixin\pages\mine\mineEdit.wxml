<view class="{{['mine-edit-container', 'data-v-756671fa', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{l}}"><view class="edit-card data-v-756671fa"><view class="edit-item-avatar data-v-756671fa"><view class="edit-label-avatar data-v-756671fa">我的头像</view><view class="avatar-wrapper data-v-756671fa"><button class="avatar-btn data-v-756671fa" open-type="chooseAvatar" bindchooseavatar="{{b}}"><image class="avatar-image data-v-756671fa" src="{{a}}" mode="aspectFill"></image></button></view></view><view class="edit-item-horizontal data-v-756671fa"><view class="edit-label-horizontal data-v-756671fa">我的昵称</view><view class="content-wrapper data-v-756671fa"><input type="nickname" placeholder="请输入昵称" maxlength="12" class="content-text data-v-756671fa" value="{{c}}" bindinput="{{d}}"/></view></view><view class="edit-item-horizontal data-v-756671fa"><view class="edit-label-horizontal data-v-756671fa">手机号码</view><view class="phone-wrapper data-v-756671fa"><block wx:if="{{e}}"><button class="phone-btn data-v-756671fa" open-type="getPhoneNumber" bindgetphonenumber="{{g}}" catchtap="{{h}}"><text class="phone-text data-v-756671fa">{{f}}</text></button></block><block wx:else><text class="phone-text data-v-756671fa" bindtap="{{j}}">{{i}}</text></block></view></view></view><view class="save-section data-v-756671fa"><button bindtap="{{k}}" class="save-btn data-v-756671fa">保存</button></view></view>