"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_car = require("../../api/car.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_u_icon2 + _easycom_up_empty2)();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_u_icon + _easycom_up_empty)();
}
const _sfc_main = {
  __name: "myCar",
  setup(__props) {
    const carList = common_vendor.ref([]);
    common_vendor.onShow(() => {
      api_car.getCarList().then((res) => {
        common_vendor.index.__f__("log", "at pages/myCar/myCar.vue:54", res);
        carList.value = [];
        common_vendor.nextTick$1(() => {
          carList.value = res.data;
        });
      });
    });
    const energyTypeOptions = [
      { label: "燃油", value: 1 },
      { label: "纯电", value: 2 },
      { label: "混动", value: 3 }
    ];
    const getEnergyTypeLabel = (value) => {
      const option = energyTypeOptions.find((item) => item.value === value);
      return option ? option.label : "";
    };
    const addCarNav = () => {
      common_vendor.index.navigateTo({
        url: "/pages/myCar/myCarAdd?isEdit=false"
      });
    };
    const editCarNav = (id) => {
      common_vendor.index.navigateTo({
        url: "/pages/myCar/myCarAdd?isEdit=true&id=" + id
      });
    };
    const deleteCarNav = (id) => {
      common_vendor.index.__f__("log", "at pages/myCar/myCar.vue:92", id);
      common_vendor.index.showModal({
        content: `确定要删除车辆吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "删除中..."
            });
            api_car.deleteCar({ id }).then((res2) => {
              common_vendor.index.hideLoading();
              if (res2.code === 200) {
                common_vendor.index.showToast({
                  title: "删除成功",
                  icon: "success"
                });
                api_car.getCarList().then((res3) => {
                  if (res3.data) {
                    carList.value = [];
                    common_vendor.nextTick$1(() => {
                      carList.value = res3.data;
                    });
                  }
                }).catch((err) => {
                  common_vendor.index.__f__("error", "at pages/myCar/myCar.vue:118", "重新获取车辆列表失败:", err);
                });
              } else {
                common_vendor.index.showToast({
                  title: res2.msg || "删除失败",
                  icon: "none"
                });
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/myCar/myCar.vue:128", "删除车辆失败:", err);
              common_vendor.index.showToast({
                title: "删除失败，请重试",
                icon: "none"
              });
            });
          }
        }
      });
    };
    const setDefaultCar = (car) => {
      if (car.isDefault) {
        common_vendor.index.showToast({
          title: "该车已经是默认车辆",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "设置中..."
      });
      api_car.editCar({ id: car.id, isDefault: 1 }).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "设置成功",
            icon: "success"
          });
          api_car.getCarList().then((res2) => {
            if (res2.data) {
              carList.value = [];
              common_vendor.nextTick$1(() => {
                carList.value = res2.data;
              });
            }
          }).catch((err) => {
            common_vendor.index.__f__("error", "at pages/myCar/myCar.vue:170", "重新获取车辆列表失败:", err);
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "设置失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/myCar/myCar.vue:180", "设置默认车辆失败:", err);
        common_vendor.index.showToast({
          title: "设置失败，请重试",
          icon: "none"
        });
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(carList.value, (car, k0, i0) => {
          return {
            a: common_vendor.t(car.plateNo),
            b: common_vendor.t(car.carType),
            c: common_vendor.t(getEnergyTypeLabel(car.energyType)),
            d: car.isDefault,
            e: common_vendor.o(($event) => setDefaultCar(car), car.id),
            f: common_vendor.o(($event) => editCarNav(car.id), car.id),
            g: "091d73e0-0-" + i0,
            h: common_vendor.o(($event) => deleteCarNav(car.id), car.id),
            i: "091d73e0-1-" + i0,
            j: car.id
          };
        }),
        b: common_assets._imports_0$3,
        c: common_vendor.p({
          name: "edit-pen",
          size: "30",
          color: "#999"
        }),
        d: common_vendor.p({
          name: "trash",
          size: "30",
          color: "#999"
        }),
        e: carList.value.length === 0
      }, carList.value.length === 0 ? {
        f: common_vendor.p({
          text: "暂无车辆信息"
        })
      } : {}, {
        g: common_vendor.o(addCarNav),
        h: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-091d73e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/myCar/myCar.js.map
