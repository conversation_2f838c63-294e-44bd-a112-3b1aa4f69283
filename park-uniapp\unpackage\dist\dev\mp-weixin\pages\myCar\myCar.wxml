<view class="{{['my-car-container', 'data-v-091d73e0', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{h}}"><view class="my-car-list data-v-091d73e0"><view wx:for="{{a}}" wx:for-item="car" wx:key="j" class="my-car-item data-v-091d73e0"><view class="my-car-content data-v-091d73e0"><view class="my-car-content-left data-v-091d73e0"><view class="plateNumber data-v-091d73e0">{{car.a}}</view><view class="car-info data-v-091d73e0"><text class="carType data-v-091d73e0">{{car.b}}</text><text class="divider data-v-091d73e0">·</text><text class="energyType data-v-091d73e0">{{car.c}}</text></view></view><view class="my-car-content-right data-v-091d73e0"><image src="{{b}}" class="car-image data-v-091d73e0" mode="widthFix"></image></view></view><view class="my-car-item-bottom data-v-091d73e0"><view class="default-section data-v-091d73e0" bindtap="{{car.e}}"><radio checked="{{car.d}}" color="#246bfd" class="radio-custom data-v-091d73e0"></radio><text class="default-text data-v-091d73e0">设置为默认车辆</text></view><view class="action-section data-v-091d73e0"><u-icon wx:if="{{c}}" class="data-v-091d73e0" virtualHostClass="data-v-091d73e0" bindtap="{{car.f}}" u-i="{{car.g}}" bind:__l="__l" u-p="{{c}}"></u-icon><u-icon wx:if="{{d}}" class="data-v-091d73e0" virtualHostClass="data-v-091d73e0" bindtap="{{car.h}}" u-i="{{car.i}}" bind:__l="__l" u-p="{{d}}"></u-icon></view></view></view></view><view wx:if="{{e}}" class="empty-state data-v-091d73e0"><up-empty wx:if="{{f}}" class="data-v-091d73e0" virtualHostClass="data-v-091d73e0" u-i="091d73e0-2" bind:__l="__l" u-p="{{f}}"/></view><view class="bottom-add-btn data-v-091d73e0" bindtap="{{g}}"><text class="add-icon data-v-091d73e0">+</text><text class="add-text data-v-091d73e0">添加车辆</text></view></view>