"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_car = require("../../api/car.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_picker2 = common_vendor.resolveComponent("u-picker");
  (_easycom_u_icon2 + _easycom_u_picker2)();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_picker = () => "../../node-modules/uview-plus/components/u-picker/u-picker.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_picker + UniPlateInput)();
}
const UniPlateInput = () => "../../components/uni-plate-input/uni-plate-input.js";
const _sfc_main = {
  __name: "myCarAdd",
  setup(__props) {
    const formData = common_vendor.ref({
      plateNo: "",
      carType: "",
      energyType: 0,
      carBrand: ""
    });
    const isEdit = common_vendor.ref(false);
    common_vendor.onLoad((options) => {
      if (options.isEdit === "true") {
        isEdit.value = true;
      }
      if (options.id) {
        loadCarInfo(options.id);
      }
    });
    const showCarType = common_vendor.ref(false);
    const showEnergyType = common_vendor.ref(false);
    const showPlateInputFlag = common_vendor.ref(false);
    const carTypeOptions = [
      "微型车",
      "轿车",
      "SUV",
      "其他"
    ];
    const energyTypeOptions = [
      { label: "燃油", value: 1 },
      { label: "纯电", value: 2 },
      { label: "混动", value: 3 }
    ];
    const getEnergyTypeLabel = (value) => {
      const option = energyTypeOptions.find((item) => item.value === value);
      return option ? option.label : "";
    };
    const loadCarInfo = (id) => {
      api_car.getCarDetailById({ id }).then((res) => {
        if (res.code === 200) {
          formData.value = res.data;
        }
      });
    };
    const showCarTypePicker = () => {
      showCarType.value = true;
    };
    const showEnergyTypePicker = () => {
      showEnergyType.value = true;
    };
    const onCarTypeConfirm = (e) => {
      formData.value.carType = e.value[0];
      showCarType.value = false;
    };
    const onEnergyTypeConfirm = (e) => {
      const selectedLabel = e.value[0];
      const selectedOption = energyTypeOptions.find((item) => item.label === selectedLabel);
      formData.value.energyType = selectedOption ? selectedOption.value : null;
      showEnergyType.value = false;
    };
    const showPlateInput = () => {
      showPlateInputFlag.value = true;
    };
    const closePlateInput = () => {
      showPlateInputFlag.value = false;
    };
    const onPlateConfirm = (plate) => {
      formData.value.plateNo = plate;
      showPlateInputFlag.value = false;
    };
    const handleSubmit = async () => {
      if (!formData.value.plateNo) {
        common_vendor.index.showToast({
          title: "请输入车牌号",
          icon: "none"
        });
        return;
      }
      if (!formData.value.carType) {
        common_vendor.index.showToast({
          title: "请选择车型",
          icon: "none"
        });
        return;
      }
      if (!formData.value.energyType) {
        common_vendor.index.showToast({
          title: "请选择能源类型",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: isEdit.value ? "保存中..." : "添加中...",
        mask: true
      });
      try {
        let res;
        if (isEdit.value) {
          res = await api_car.editCar(formData.value);
        } else {
          res = await api_car.addCar(formData.value);
        }
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: isEdit.value ? "编辑成功" : "添加成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "操作失败",
            icon: "none",
            duration: 3e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/myCar/myCarAdd.vue:246", "操作失败:", error);
        common_vendor.index.hideLoading();
        const errorMsg = error.msg || error.message || "操作失败，请重试";
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 3e3
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(isEdit.value ? "编辑车辆信息" : "添加车辆信息"),
        b: common_vendor.t(isEdit.value ? "Edit Vehicle Information" : "Add Vehicle Information"),
        c: common_assets._imports_0$5,
        d: common_vendor.t(formData.value.plateNo || "请选择车牌号"),
        e: common_vendor.p({
          name: "arrow-right",
          size: "22",
          color: "#999"
        }),
        f: common_vendor.o(showPlateInput),
        g: common_vendor.t(formData.value.carType || "请选择车型"),
        h: common_vendor.p({
          name: "arrow-right",
          size: "22",
          color: "#999"
        }),
        i: common_vendor.o(showCarTypePicker),
        j: common_vendor.t(getEnergyTypeLabel(formData.value.energyType) || "请选择能源类型"),
        k: common_vendor.p({
          name: "arrow-right",
          size: "22",
          color: "#999"
        }),
        l: common_vendor.o(showEnergyTypePicker),
        m: formData.value.carBrand,
        n: common_vendor.o(($event) => formData.value.carBrand = $event.detail.value),
        o: common_vendor.p({
          name: "arrow-right",
          size: "22",
          color: "#999"
        }),
        p: common_vendor.t(isEdit.value ? "保存修改" : "添加车辆"),
        q: common_vendor.o(handleSubmit),
        r: common_vendor.o(onCarTypeConfirm),
        s: common_vendor.o(($event) => showCarType.value = false),
        t: common_vendor.p({
          show: showCarType.value,
          columns: [carTypeOptions]
        }),
        v: common_vendor.o(onEnergyTypeConfirm),
        w: common_vendor.o(($event) => showEnergyType.value = false),
        x: common_vendor.p({
          show: showEnergyType.value,
          columns: [energyTypeOptions.map((item) => item.label)],
          ["default-index"]: [0]
        }),
        y: showPlateInputFlag.value
      }, showPlateInputFlag.value ? {
        z: common_vendor.o(closePlateInput),
        A: common_vendor.o(onPlateConfirm),
        B: common_vendor.p({
          plate: formData.value.plateNo
        })
      } : {}, {
        C: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-653d7414"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/myCar/myCarAdd.js.map
