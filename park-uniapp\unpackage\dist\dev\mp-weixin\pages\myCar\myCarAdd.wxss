/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-car-add-container.data-v-653d7414 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header-section.data-v-653d7414 {
  padding: 20rpx 32rpx 0;
}
.header-section .header-content .header-title.data-v-653d7414 {
  margin-right: 20rpx;
}
.header-section .header-content .header-title .title.data-v-653d7414 {
  font-size: 38rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.header-section .header-content .header-title .desc.data-v-653d7414 {
  font-size: 26rpx;
  opacity: 0.5;
}
.header-section .header-content .header-image.data-v-653d7414 {
  width: 280rpx;
  height: 200rpx;
}
.form-section.data-v-653d7414 {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 24rpx;
  padding: 32rpx;
}
.form-section .form-item.data-v-653d7414 {
  margin-bottom: 40rpx;
}
.form-section .form-item.data-v-653d7414:last-child {
  margin-bottom: 0;
}
.form-section .form-item .form-label.data-v-653d7414 {
  font-size: 32rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 20rpx;
}
.form-section .form-item .form-input.data-v-653d7414 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #dbdbdb;
}
.form-section .form-item .form-input .picker-input.data-v-653d7414 {
  flex: 1;
  font-size: 32rpx;
  color: #555;
}
.form-section .form-item .form-input .plate-display.data-v-653d7414 {
  flex: 1;
  font-size: 32rpx;
  color: #555;
}
.form-section .form-item .form-input .input-field.data-v-653d7414 {
  flex: 1;
  font-size: 32rpx;
  color: #555;
}
.bottom-section.data-v-653d7414 {
  margin: 32rpx 24rpx;
}
.bottom-section .submit-btn.data-v-653d7414 {
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  border-radius: 50rpx;
  padding: 28rpx;
  text-align: center;
}
.bottom-section .submit-btn .submit-text.data-v-653d7414 {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}
.my-car-add-container.data-v-653d7414 {
  min-height: 100vh;
  background-color: #f5f5f5;
}