"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  _easycom_up_icon2();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  _easycom_up_icon();
}
const _sfc_main = {
  __name: "ChargingFastPackage",
  setup(__props, { expose: __expose }) {
    const initData = async () => {
      common_vendor.index.__f__("log", "at pages/package/components/ChargingFastPackage.vue:14", "充电套餐组件初始化 - 暂无数据");
    };
    __expose({
      initData
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "file-text",
          size: "100",
          color: "#d0d0d0"
        }),
        b: common_vendor.gei(_ctx, "")
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f32f36f5"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/package/components/ChargingFastPackage.js.map
