<view class="{{['charging-package-component', 'data-v-f32f36f5', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{b}}"><view class="empty-state data-v-f32f36f5"><up-icon wx:if="{{a}}" class="data-v-f32f36f5" virtualHostClass="data-v-f32f36f5" u-i="f32f36f5-0" bind:__l="__l" u-p="{{a}}"></up-icon><text class="empty-text data-v-f32f36f5">暂无充电套餐</text></view></view>