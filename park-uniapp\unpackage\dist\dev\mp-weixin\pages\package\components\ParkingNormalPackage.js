"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_package = require("../../../api/package.js");
const api_warehouse = require("../../../api/warehouse.js");
const api_car = require("../../../api/car.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_u_picker2 = common_vendor.resolveComponent("u-picker");
  (_easycom_up_icon2 + _easycom_up_empty2 + _easycom_u_picker2)();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../../node-modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_u_picker = () => "../../../node-modules/uview-plus/components/u-picker/u-picker.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_empty + WarehouseSelector + _easycom_u_picker)();
}
const WarehouseSelector = () => "../../../components/warehouse-selector/warehouse-selector.js";
const _sfc_main = {
  __name: "ParkingNormalPackage",
  setup(__props, { expose: __expose }) {
    const vipType = common_vendor.ref(0);
    const wareHouseList = common_vendor.ref([]);
    const wareHouseSelector = common_vendor.ref(false);
    const currentWarehouse = common_vendor.ref({ id: 0, name: "选择场库" });
    const packageList = common_vendor.ref([]);
    const choosePackage = common_vendor.ref({});
    const carList = common_vendor.ref([]);
    const selectedCar = common_vendor.ref({});
    const showCarSelector = common_vendor.ref(false);
    const userPackagePlate = common_vendor.ref({});
    const initData = async () => {
      await initWarehouseData();
      await initPackageData();
      await initCarData();
    };
    const initWarehouseData = async () => {
      const res = await api_warehouse.getParkWareHouseList();
      wareHouseList.value = res.data.map((item) => ({
        id: item.id,
        name: item.warehouseName,
        latitude: item.latitude,
        longitude: item.longitude
      }));
      const cachedWarehouse = common_vendor.index.getStorageSync("currentWarehouse");
      if (cachedWarehouse && wareHouseList.value.some((w) => w.id === cachedWarehouse.id)) {
        currentWarehouse.value = cachedWarehouse;
      } else if (wareHouseList.value.length > 0) {
        currentWarehouse.value = wareHouseList.value[0];
        common_vendor.index.setStorageSync("currentWarehouse", currentWarehouse.value);
      }
    };
    const initPackageData = async () => {
      try {
        if (!currentWarehouse.value || !currentWarehouse.value.id) {
          packageList.value = [];
          choosePackage.value = {};
          return;
        }
        const warehouseId = currentWarehouse.value.id;
        const res = await api_package.getPackageList({ warehouseId });
        packageList.value = res.data || [];
        if (packageList.value.length > 0) {
          choosePackage.value = packageList.value[0];
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "套餐数据加载失败",
          icon: "none"
        });
        packageList.value = [];
        choosePackage.value = {};
      }
    };
    const initCarData = async () => {
      var _a, _b;
      try {
        const res = await api_car.getCarList();
        carList.value = res.data || [];
        if (carList.value.length === 0) {
          return;
        }
        const cachedCar = common_vendor.index.getStorageSync("selectedCar");
        if (cachedCar && cachedCar.plateNo) {
          const cachedCarInList = carList.value.find((car) => car.plateNo === cachedCar.plateNo);
          if (cachedCarInList) {
            selectedCar.value = cachedCarInList;
            try {
              common_vendor.index.setStorageSync("selectedCar", cachedCarInList);
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:163", "更新缓存车辆信息失败:", error);
            }
          } else {
            selectDefaultCar();
          }
        } else {
          selectDefaultCar();
        }
        if (((_a = currentWarehouse.value) == null ? void 0 : _a.id) && ((_b = selectedCar.value) == null ? void 0 : _b.plateNo)) {
          api_package.getUserPackagePlate({
            warehouseId: currentWarehouse.value.id,
            plateNo: selectedCar.value.plateNo,
            vipType: vipType.value
          }).then((res2) => {
            userPackagePlate.value = res2.data || {};
          }).catch((error) => {
            common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:183", "获取用户套餐信息失败:", error);
            userPackagePlate.value = {};
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:188", "初始化车辆数据失败:", error);
        carList.value = [];
        selectedCar.value = {};
        userPackagePlate.value = {};
      }
    };
    const selectDefaultCar = () => {
      const defaultCar = carList.value.find((car) => car.isDefault === 1);
      if (defaultCar) {
        selectedCar.value = defaultCar;
      } else if (carList.value.length > 0) {
        selectedCar.value = carList.value[0];
      }
      if (selectedCar.value && selectedCar.value.plateNo) {
        try {
          common_vendor.index.setStorageSync("selectedCar", selectedCar.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:211", "缓存车辆信息失败:", error);
        }
      }
    };
    const showWarehouseSelector = () => {
      wareHouseSelector.value = true;
    };
    const closeWarehouseSelector = () => {
      wareHouseSelector.value = false;
    };
    const selectWarehouse = (warehouse) => {
      var _a;
      currentWarehouse.value = warehouse;
      common_vendor.index.setStorageSync("currentWarehouse", warehouse);
      closeWarehouseSelector();
      initPackageData();
      if ((_a = selectedCar.value) == null ? void 0 : _a.plateNo) {
        api_package.getUserPackagePlate({
          warehouseId: warehouse.id,
          plateNo: selectedCar.value.plateNo,
          vipType: vipType.value
        }).then((res) => {
          userPackagePlate.value = res.data;
        }).catch((error) => {
          common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:244", "获取用户套餐信息失败:", error);
          userPackagePlate.value = {};
        });
      }
    };
    const openCarSelector = () => {
      if (!hasCar()) {
        return;
      }
      showCarSelector.value = true;
    };
    const hasCar = () => {
      if (carList.value.length === 0) {
        common_vendor.index.showToast({
          title: "请先去个人中心添加车辆",
          icon: "none",
          duration: 1500
        });
        return false;
      }
      return true;
    };
    const onCarConfirm = (e) => {
      var _a, _b;
      selectedCar.value = e.value[0];
      showCarSelector.value = false;
      try {
        common_vendor.index.setStorageSync("selectedCar", selectedCar.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:279", "缓存车辆信息失败:", error);
      }
      if (((_a = currentWarehouse.value) == null ? void 0 : _a.id) && ((_b = selectedCar.value) == null ? void 0 : _b.plateNo)) {
        api_package.getUserPackagePlate({
          warehouseId: currentWarehouse.value.id,
          plateNo: selectedCar.value.plateNo,
          vipType: vipType.value
        }).then((res) => {
          userPackagePlate.value = res.data || {};
          common_vendor.index.__f__("log", "at pages/package/components/ParkingNormalPackage.vue:290", "userPackagePlate", userPackagePlate.value);
        }).catch((error) => {
          common_vendor.index.__f__("error", "at pages/package/components/ParkingNormalPackage.vue:292", "获取用户套餐信息失败:", error);
          userPackagePlate.value = {};
        });
      }
    };
    const handleChoosePackage = (item) => {
      choosePackage.value = item;
    };
    const handleBuyPackage = () => {
      var _a, _b, _c, _d;
      if (!hasCar()) {
        return;
      }
      const packageOrder = {
        warehouseId: currentWarehouse.value.id,
        warehouseName: currentWarehouse.value.name,
        packageId: choosePackage.value.id,
        packageName: choosePackage.value.packageName,
        packagePrice: choosePackage.value.packagePrice,
        packageDays: choosePackage.value.packageType,
        plateNo: selectedCar.value.plateNo,
        vipType: vipType.value,
        isRenewal: !!(((_a = userPackagePlate.value) == null ? void 0 : _a.beginVipTime) && ((_b = userPackagePlate.value) == null ? void 0 : _b.endVipTime)),
        beginVipTime: (_c = userPackagePlate.value) == null ? void 0 : _c.beginVipTime,
        expirationTime: (_d = userPackagePlate.value) == null ? void 0 : _d.endVipTime
      };
      common_vendor.index.navigateTo({ url: "/pages/package/packageBuy?packageOrder=" + JSON.stringify(packageOrder) });
    };
    const goToRecord = () => {
      common_vendor.index.navigateTo({ url: "/pages/package/packageRecord?vipType=" + vipType.value });
    };
    __expose({
      initData
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e;
      return common_vendor.e({
        a: common_vendor.t(currentWarehouse.value.name),
        b: common_vendor.p({
          name: "arrow-right",
          size: "12",
          color: "#fff"
        }),
        c: common_vendor.o(showWarehouseSelector),
        d: common_vendor.t(selectedCar.value.plateNo || "点击选择"),
        e: common_vendor.p({
          name: "arrow-right",
          size: "12",
          color: "#fff"
        }),
        f: common_vendor.o(openCarSelector),
        g: common_vendor.t(((_a = userPackagePlate.value) == null ? void 0 : _a.beginVipTime) || "--"),
        h: common_vendor.t(((_b = userPackagePlate.value) == null ? void 0 : _b.endVipTime) || "--"),
        i: common_vendor.o(goToRecord),
        j: ((_c = packageList.value) == null ? void 0 : _c.length) > 0
      }, ((_d = packageList.value) == null ? void 0 : _d.length) > 0 ? {
        k: common_vendor.f(packageList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.packageName),
            b: common_vendor.t(item.packagePrice),
            c: item.id,
            d: choosePackage.value.id === item.id ? 1 : "",
            e: common_vendor.o(($event) => handleChoosePackage(item), item.id)
          };
        })
      } : {
        l: common_vendor.p({
          mode: "data",
          text: "暂无可用套餐"
        })
      }, {
        m: common_vendor.t(((_e = userPackagePlate.value) == null ? void 0 : _e.endVipTime) ? "续费套餐" : "购买套餐"),
        n: !choosePackage.value.id || packageList.value.length === 0,
        o: common_vendor.o(handleBuyPackage),
        p: common_vendor.o(closeWarehouseSelector),
        q: common_vendor.o(selectWarehouse),
        r: common_vendor.p({
          show: wareHouseSelector.value,
          ["warehouse-list"]: wareHouseList.value,
          ["current-warehouse"]: currentWarehouse.value,
          ["window-height-half"]: 400
        }),
        s: common_vendor.o(onCarConfirm),
        t: common_vendor.o(($event) => showCarSelector.value = false),
        v: common_vendor.p({
          show: showCarSelector.value,
          columns: [carList.value],
          keyName: "plateNo"
        }),
        w: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f658ade8"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/package/components/ParkingNormalPackage.js.map
