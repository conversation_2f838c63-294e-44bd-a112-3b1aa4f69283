<view class="{{['vip-page', 'data-v-f658ade8', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{w}}"><image class="vip-bg data-v-f658ade8" src="https://test-parknew.lgfw24hours.com:3443/statics/wx/vipHeader.png" mode="widthFix"></image><view class="vip-card data-v-f658ade8"><view class="vip-info data-v-f658ade8"><view class="vip-title-container data-v-f658ade8" bindtap="{{c}}"><text class="vip-title data-v-f658ade8">{{a}}</text><up-icon wx:if="{{b}}" class="data-v-f658ade8" virtualHostClass="data-v-f658ade8" u-i="f658ade8-0" bind:__l="__l" u-p="{{b}}"></up-icon></view><view class="vip-plate data-v-f658ade8" bindtap="{{f}}"><text class="data-v-f658ade8">车牌号：</text><text class="plate-number data-v-f658ade8">{{d}}</text><up-icon wx:if="{{e}}" class="data-v-f658ade8" virtualHostClass="data-v-f658ade8" u-i="f658ade8-1" bind:__l="__l" u-p="{{e}}"></up-icon></view><view class="vip-expire data-v-f658ade8">开始：{{g}}</view><view class="vip-expire data-v-f658ade8">结束：{{h}}</view><view class="vip-link data-v-f658ade8" bindtap="{{i}}">查看续费与开通记录</view></view></view><view class="package-list data-v-f658ade8"><view class="package-title data-v-f658ade8">选择普通充值套餐</view><view wx:if="{{j}}" class="package-grid data-v-f658ade8"><view wx:for="{{k}}" wx:for-item="item" wx:key="c" class="{{['package-item', 'data-v-f658ade8', item.d && 'active']}}" bindtap="{{item.e}}"><view class="package-content data-v-f658ade8"><view class="package-name data-v-f658ade8">{{item.a}}</view><view class="package-price data-v-f658ade8"><text class="price-symbol data-v-f658ade8">￥</text><text class="price-amount data-v-f658ade8">{{item.b}}</text></view></view></view></view><view wx:else class="empty-state data-v-f658ade8"><up-empty wx:if="{{l}}" class="data-v-f658ade8" virtualHostClass="data-v-f658ade8" u-i="f658ade8-2" bind:__l="__l" u-p="{{l}}"/></view><button class="buy-btn data-v-f658ade8" disabled="{{n}}" bindtap="{{o}}">{{m}}</button></view><warehouse-selector wx:if="{{r}}" class="data-v-f658ade8" virtualHostClass="data-v-f658ade8" bindclose="{{p}}" bindselect="{{q}}" u-i="f658ade8-3" bind:__l="__l" u-p="{{r}}"/><u-picker wx:if="{{v}}" class="data-v-f658ade8" virtualHostClass="data-v-f658ade8" bindconfirm="{{s}}" bindcancel="{{t}}" u-i="f658ade8-4" bind:__l="__l" u-p="{{v}}"></u-picker></view>