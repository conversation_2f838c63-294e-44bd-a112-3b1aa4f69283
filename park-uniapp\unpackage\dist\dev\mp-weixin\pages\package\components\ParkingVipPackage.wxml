<view class="{{['parking-vip-package', 'data-v-b8425ce1', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{b}}"><view class="vip-package-list data-v-b8425ce1"><view wx:for="{{a}}" wx:for-item="cardData" wx:key="j" class="vip-card data-v-b8425ce1"><view class="content-section data-v-b8425ce1"><view class="vip-info data-v-b8425ce1"><view class="vip-plate data-v-b8425ce1"> 车牌号：{{cardData.a}}</view><view class="vip-title-container data-v-b8425ce1"> 场库：{{cardData.b}}</view><view class="vip-expire data-v-b8425ce1"> 开始：{{cardData.c}}</view><view class="vip-expire data-v-b8425ce1"> 结束：{{cardData.d}}</view></view><view class="right-section data-v-b8425ce1"><view wx:if="{{cardData.e}}" class="car-image data-v-b8425ce1"><image class="data-v-b8425ce1" src="{{cardData.f}}" mode="aspectFit"></image></view></view></view><view class="button-section data-v-b8425ce1"><view class="{{['vip-link', 'data-v-b8425ce1', cardData.h && 'disabled']}}" bindtap="{{cardData.i}}">{{cardData.g}}</view></view></view></view></view>