"use strict";
const common_vendor = require("../../common/vendor.js");
const api_specialUser = require("../../api/specialUser.js");
if (!Math) {
  (ParkingNormalPackage + ParkingVipPackage + ChargingFastPackage + CustomTabBar)();
}
const CustomTabBar = () => "../../components/custom-tab-bar/index.js";
const ParkingNormalPackage = () => "./components/ParkingNormalPackage.js";
const ChargingFastPackage = () => "./components/ChargingFastPackage.js";
const ParkingVipPackage = () => "./components/ParkingVipPackage.js";
const _sfc_main = {
  __name: "package",
  setup(__props) {
    const specialUser = common_vendor.ref({});
    const parkingSubTypes = common_vendor.ref([
      { name: "普通套餐" }
    ]);
    const chargingSubTypes = common_vendor.ref([]);
    const selectedPackageType = common_vendor.ref({ name: "停车套餐" });
    const selectedSubType = common_vendor.ref({ name: "普通套餐" });
    const componentKey = common_vendor.ref(0);
    const parkingNormalRef = common_vendor.ref(null);
    const parkingVipRef = common_vendor.ref(null);
    const chargingFastRef = common_vendor.ref(null);
    common_vendor.onShow(() => {
      loadSelectedTypesFromCache();
      api_specialUser.getSpecialUser().then((res) => {
        specialUser.value = res.data || {};
        if (res.data && res.data.userType) {
          if (res.data.userType === "VIP客户") {
            parkingSubTypes.value = [
              { name: "VIP套餐" },
              { name: "普通套餐" }
            ];
          } else if (res.data.userType === "集团客户") {
            parkingSubTypes.value = [
              { name: "集团套餐" },
              { name: "普通套餐" }
            ];
          } else {
            parkingSubTypes.value = [
              { name: "普通套餐" }
            ];
          }
        } else {
          parkingSubTypes.value = [
            { name: "普通套餐" }
          ];
        }
        validateAndCorrectSelectedTypes();
        forceComponentReload();
      });
    });
    const loadSelectedTypesFromCache = () => {
      try {
        const cachedPackageType = common_vendor.index.getStorageSync("selectedPackageType");
        const cachedSubType = common_vendor.index.getStorageSync("selectedSubType");
        if (cachedPackageType) {
          selectedPackageType.value = cachedPackageType;
        }
        if (cachedSubType) {
          selectedSubType.value = cachedSubType;
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/package/package.vue:138", "读取缓存失败:", error);
      }
    };
    const validateAndCorrectSelectedTypes = () => {
      if (selectedPackageType.value.name === "停车套餐") {
        const currentSubTypeNames = parkingSubTypes.value.map((item) => item.name);
        if (!currentSubTypeNames.includes(selectedSubType.value.name)) {
          selectedSubType.value = parkingSubTypes.value[0];
          saveSelectedTypesToCache();
        }
      }
    };
    const saveSelectedTypesToCache = () => {
      try {
        common_vendor.index.setStorageSync("selectedPackageType", selectedPackageType.value);
        common_vendor.index.setStorageSync("selectedSubType", selectedSubType.value);
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/package/package.vue:162", "保存缓存失败:", error);
      }
    };
    const forceComponentReload = () => {
      componentKey.value++;
      common_vendor.nextTick$1(() => {
        const tryInitData = (retryCount = 0) => {
          const currentRef = getCurrentComponentRef();
          if (currentRef && currentRef.initData) {
            currentRef.initData();
          } else if (retryCount < 5) {
            setTimeout(() => {
              tryInitData(retryCount + 1);
            }, 200 * (retryCount + 1));
          }
        };
        setTimeout(() => {
          tryInitData();
        }, 150);
      });
    };
    const getCurrentComponentRef = () => {
      if (selectedPackageType.value.name === "停车套餐") {
        if (selectedSubType.value.name === "普通套餐") {
          return parkingNormalRef.value;
        } else if (selectedSubType.value.name === "VIP套餐" || selectedSubType.value.name === "集团套餐") {
          return parkingVipRef.value;
        }
      } else if (selectedPackageType.value.name === "充电套餐") {
        return chargingFastRef.value;
      }
      return null;
    };
    const currentSubTypes = common_vendor.computed(() => {
      return selectedPackageType.value.name === "停车套餐" ? parkingSubTypes.value : chargingSubTypes.value;
    });
    const selectPackageType = (typeName) => {
      selectedPackageType.value = { name: typeName };
      selectedSubType.value = currentSubTypes.value[0];
      saveSelectedTypesToCache();
      forceComponentReload();
    };
    const selectSubType = (subType) => {
      if (selectedSubType.value.name === subType.name)
        return;
      selectedSubType.value = subType;
      saveSelectedTypesToCache();
      forceComponentReload();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: selectedPackageType.value.name === "停车套餐" ? 1 : "",
        b: common_vendor.o(($event) => selectPackageType("停车套餐")),
        c: selectedPackageType.value.name === "充电套餐" ? 1 : "",
        d: common_vendor.o(($event) => selectPackageType("充电套餐")),
        e: common_vendor.f(currentSubTypes.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.n(selectedSubType.value.name === item.name ? "active" : ""),
            d: common_vendor.o(($event) => selectSubType(item), index)
          };
        }),
        f: selectedPackageType.value.name === "停车套餐" && selectedSubType.value.name === "普通套餐"
      }, selectedPackageType.value.name === "停车套餐" && selectedSubType.value.name === "普通套餐" ? {
        g: common_vendor.sr(parkingNormalRef, "9a861780-0", {
          "k": "parkingNormalRef"
        }),
        h: componentKey.value
      } : {}, {
        i: selectedPackageType.value.name === "停车套餐" && (selectedSubType.value.name === "VIP套餐" || selectedSubType.value.name === "集团套餐")
      }, selectedPackageType.value.name === "停车套餐" && (selectedSubType.value.name === "VIP套餐" || selectedSubType.value.name === "集团套餐") ? {
        j: common_vendor.sr(parkingVipRef, "9a861780-1", {
          "k": "parkingVipRef"
        }),
        k: componentKey.value,
        l: common_vendor.p({
          userType: selectedSubType.value.name === "集团套餐" ? "集团套餐" : "VIP套餐"
        })
      } : {}, {
        m: selectedPackageType.value.name === "充电套餐"
      }, selectedPackageType.value.name === "充电套餐" ? {
        n: common_vendor.sr(chargingFastRef, "9a861780-2", {
          "k": "chargingFastRef"
        }),
        o: componentKey.value
      } : {}, {
        p: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9a861780"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/package/package.js.map
