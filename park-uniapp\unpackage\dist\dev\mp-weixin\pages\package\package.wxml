<view class="{{['package-container', 'data-v-9a861780', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{p}}"><view class="tab-header data-v-9a861780"><view class="{{['tab-item', 'data-v-9a861780', a && 'active']}}" bindtap="{{b}}"><text class="tab-text data-v-9a861780">停车套餐</text></view><view class="tab-divider data-v-9a861780"></view><view class="{{['tab-item', 'data-v-9a861780', c && 'active']}}" bindtap="{{d}}"><text class="tab-text data-v-9a861780">充电套餐</text></view></view><view class="sub-type-container data-v-9a861780"><view wx:for="{{e}}" wx:for-item="item" wx:key="b" class="{{['data-v-9a861780', 'sub-type-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view><view class="package-content data-v-9a861780"><parking-normal-package wx:if="{{f}}" class="r data-v-9a861780" virtualHostClass="r data-v-9a861780" key="{{h}}" u-r="parkingNormalRef" u-i="9a861780-0" bind:__l="__l"/><parking-vip-package wx:if="{{i}}" class="r data-v-9a861780" virtualHostClass="r data-v-9a861780" key="{{k}}" u-r="parkingVipRef" u-i="9a861780-1" bind:__l="__l" u-p="{{l}}"/><charging-fast-package wx:if="{{m}}" class="r data-v-9a861780" virtualHostClass="r data-v-9a861780" key="{{o}}" u-r="chargingFastRef" u-i="9a861780-2" bind:__l="__l"/></view><custom-tab-bar class="data-v-9a861780" virtualHostClass="data-v-9a861780" u-i="9a861780-3" bind:__l="__l"></custom-tab-bar></view>