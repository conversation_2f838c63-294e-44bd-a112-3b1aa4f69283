"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_package = require("../../api/package.js");
const api_login = require("../../api/login.js");
const utils_utils = require("../../utils/utils.js");
const _sfc_main = {
  __name: "packageBuy",
  setup(__props) {
    const packageOrder = common_vendor.ref({});
    const isExistingMember = common_vendor.ref(false);
    const isCarInWarehouse = common_vendor.ref(false);
    const packageJudge = common_vendor.ref({});
    const canSelectTime = common_vendor.ref(false);
    const selectedDate = common_vendor.ref("");
    const minDate = common_vendor.ref("");
    const maxDate = common_vendor.ref("");
    const noticeText = common_vendor.computed(() => {
      if (packageOrder.value.isRenewal) {
        return `当前选择的续费套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
        续费将在原有套餐到期日期基础上延长，无法修改时间`;
      }
      if (isCarInWarehouse.value) {
        const hasExpiredMember = packageJudge.value.endVipTime !== null;
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        if (hasExpiredMember || hasParkingPayment) {
          return `当前选择的套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;
        } else {
          return `当前选择的套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;
        }
      }
      return `当前选择的开通套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;
    });
    common_vendor.onLoad((options) => {
      packageOrder.value = JSON.parse(options.packageOrder);
      isExistingMember.value = packageOrder.value.isRenewal;
      common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:136", "isExistingMember.value: ", isExistingMember.value);
      common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:137", "packageOrder.value: ", packageOrder.value);
      if (isExistingMember.value) {
        handleRenewalOrder();
      } else {
        api_package.checkCarInWarehouse({
          plateNo: packageOrder.value.plateNo,
          warehouseId: packageOrder.value.warehouseId
        }).then((res) => {
          packageJudge.value = res.data;
          isCarInWarehouse.value = res.data && res.data.isCarInWarehouse;
          handleFirstTimeOrder();
        }).catch((err) => {
          common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:152", "检查车辆是否在场失败：", err);
          isCarInWarehouse.value = false;
          handleFirstTimeOrder();
        });
      }
    });
    const handleRenewalOrder = () => {
      if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {
        common_vendor.index.showToast({
          title: "续费订单数据异常，请重新操作",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      const originalEndDate = new Date(packageOrder.value.expirationTime);
      const newEndDate = new Date(originalEndDate);
      newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);
      const endDateStr = utils_utils.formatDate(newEndDate);
      packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;
      packageOrder.value.canSelectTime = false;
    };
    const handleFirstTimeOrder = () => {
      if (isCarInWarehouse.value) {
        handleCarInWarehouse();
      } else {
        handleCarNotInWarehouse();
      }
    };
    const handleCarInWarehouse = () => {
      const hasExpiredMember = packageJudge.value.endVipTime !== null;
      if (hasExpiredMember && packageJudge.value.finalBeginTime) {
        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);
        let startDate = new Date(finalBeginDate);
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        const isFinalBeginFromParking = hasParkingPayment && packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;
        if (isFinalBeginFromParking) {
          startDate.setHours(0, 0, 0, 0);
        } else {
          startDate.setDate(finalBeginDate.getDate() + 1);
          startDate.setHours(0, 0, 0, 0);
        }
        const startDateStr = utils_utils.formatDate(startDate);
        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;
        const today = /* @__PURE__ */ new Date();
        const endDate = new Date(startDate);
        const isToday = utils_utils.isSameDate(startDate, today);
        if (isToday) {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
        } else {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }
        const endDateStr = utils_utils.formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
      } else {
        if (packageJudge.value.finalBeginTime) {
          const datePart = utils_utils.extractDatePart(packageJudge.value.finalBeginTime);
          packageOrder.value.beginVipTime = `${datePart} 00:00:00`;
          const startDate = new Date(packageOrder.value.beginVipTime);
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
          const endDateStr = utils_utils.formatDate(endDate);
          packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
        } else {
          common_vendor.index.showToast({
            title: "参数错误，请联系客服",
            icon: "none",
            duration: 2e3
          });
          return;
        }
      }
      packageOrder.value.canSelectTime = false;
    };
    const handleCarNotInWarehouse = () => {
      canSelectTime.value = true;
      const today = /* @__PURE__ */ new Date();
      const dateStr = utils_utils.formatDate(today);
      packageOrder.value.beginVipTime = `${dateStr} 00:00:00`;
      updateDateRange();
      calculateEndDate();
    };
    const updateDateRange = () => {
      if (!canSelectTime.value) {
        return;
      }
      const today = /* @__PURE__ */ new Date();
      minDate.value = utils_utils.formatDate(today);
      const maxDateObj = /* @__PURE__ */ new Date();
      maxDateObj.setMonth(today.getMonth() + 3);
      maxDate.value = utils_utils.formatDate(maxDateObj);
      selectedDate.value = utils_utils.formatDate(today);
    };
    const onDateChange = (e) => {
      if (!canSelectTime.value) {
        return;
      }
      const selectedDateStr = e.detail.value;
      const selectedDateObj = utils_utils.createDate(selectedDateStr);
      const today = /* @__PURE__ */ new Date();
      utils_utils.isSameDate(selectedDateObj, today);
      packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`;
      selectedDate.value = selectedDateStr;
      calculateEndDate();
    };
    const calculateEndDate = () => {
      if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {
        const startDate = new Date(packageOrder.value.beginVipTime);
        const today = /* @__PURE__ */ new Date();
        const endDate = new Date(startDate);
        const isToday = utils_utils.isSameDate(startDate, today);
        if (isToday) {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
        } else {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }
        const endDateStr = utils_utils.formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
      }
    };
    const submitOrder = () => {
      const currentTime = /* @__PURE__ */ new Date();
      let endTime;
      if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {
        endTime = new Date(packageOrder.value.newExpirationTime);
      } else if (packageOrder.value.expirationTime) {
        endTime = new Date(packageOrder.value.expirationTime);
      } else {
        common_vendor.index.showToast({
          title: "套餐到期时间异常，请重新选择",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (endTime <= currentTime) {
        common_vendor.index.showToast({
          title: "套餐到期时间不能早于当前时间，请重新选择",
          icon: "none",
          duration: 3e3
        });
        return;
      }
      common_vendor.index.login({
        success: async (loginRes) => {
          try {
            const openidRes = await api_login.getOpenid({
              wxCode: loginRes.code
            });
            packageOrder.value.openid = openidRes.data;
            createOrderWithOpenid();
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/package/packageBuy.vue:392", "获取openid失败:", error);
            common_vendor.index.showToast({
              title: "获取用户信息失败",
              icon: "none",
              duration: 2e3
            });
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/package/packageBuy.vue:401", "登录失败:", error);
          common_vendor.index.showToast({
            title: "登录失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const createOrderWithOpenid = () => {
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_package.createOrder(packageOrder.value).then((res) => {
        common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:420", "创建订单 res: ", res);
        if (res.data.needPay) {
          common_vendor.index.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: res.data.signType,
            paySign: res.data.paySign,
            success: function(result) {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付成功~",
                  icon: "none",
                  duration: 2e3
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            fail: function(err) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:444", "支付失败的回调：", err);
              if (res.data.orderId) {
                api_package.updateOrder({
                  id: res.data.orderId,
                  payStatus: 3
                  // 已取消
                }).then((updateRes) => {
                  common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:452", "订单状态更新为已取消：", updateRes);
                }).catch((updateErr) => {
                  common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:454", "订单状态更新失败：", updateErr);
                });
              }
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付失败",
                  icon: "none",
                  duration: 1500
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            complete: function(res2) {
              common_vendor.index.hideLoading();
            }
          });
        } else {
          common_vendor.index.hideLoading();
          setTimeout(() => {
            common_vendor.index.showToast({
              title: "开通成功~",
              icon: "none",
              duration: 2e3
            });
          }, 100);
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 2e3);
        }
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/package/packageBuy.vue:489", err);
        common_vendor.index.hideLoading();
        setTimeout(() => {
          common_vendor.index.showToast({
            title: "订单创建失败",
            icon: "none",
            duration: 1500
          });
        }, 100);
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(noticeText.value),
        b: common_assets._imports_1$2,
        c: common_vendor.t(packageOrder.value.warehouseName || "-"),
        d: common_assets._imports_2$1,
        e: common_vendor.t(packageOrder.value.plateNo || "-"),
        f: common_assets._imports_3,
        g: common_vendor.t(packageOrder.value.packageName || "-"),
        h: common_assets._imports_4,
        i: canSelectTime.value
      }, canSelectTime.value ? {
        j: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间"),
        k: selectedDate.value,
        l: minDate.value,
        m: maxDate.value,
        n: common_vendor.o(onDateChange)
      } : {
        o: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间")
      }, {
        p: common_vendor.n(canSelectTime.value ? "clickable-text" : ""),
        q: packageOrder.value.isRenewal
      }, packageOrder.value.isRenewal ? {
        r: common_assets._imports_4,
        s: common_vendor.t(packageOrder.value.expirationTime)
      } : {}, {
        t: common_assets._imports_4,
        v: common_vendor.t(packageOrder.value.isRenewal ? "续费后到期时间" : "到期时间"),
        w: common_vendor.t(packageOrder.value.newExpirationTime || packageOrder.value.expirationTime || "--"),
        x: common_assets._imports_5,
        y: common_vendor.t(packageOrder.value.packagePrice || "-"),
        z: common_assets._imports_0$7,
        A: common_vendor.t(packageOrder.value.packagePrice || "0.00"),
        B: common_vendor.t(packageOrder.value.isRenewal ? "确认续费" : "提交订单"),
        C: common_vendor.o(submitOrder),
        D: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e922d8e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/package/packageBuy.js.map
