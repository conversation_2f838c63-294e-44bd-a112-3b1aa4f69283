"use strict";
const common_vendor = require("../../common/vendor.js");
const api_package = require("../../api/package.js");
const api_specialUser = require("../../api/specialUser.js");
const api_invoice = require("../../api/invoice.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_popup2 = common_vendor.resolveComponent("up-popup");
  (_easycom_up_icon2 + _easycom_up_input2 + _easycom_up_popup2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_input = () => "../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_input + _easycom_up_popup)();
}
const _sfc_main = {
  __name: "packageRecord",
  setup(__props) {
    const recordList = common_vendor.ref([]);
    const vipType = common_vendor.ref(0);
    const specialUser = common_vendor.ref({});
    const activeStatusFilter = common_vendor.ref(0);
    const activeUserTypeFilter = common_vendor.ref("");
    const showPopup = common_vendor.ref(false);
    const invoiceId = common_vendor.ref(null);
    const notifyEmail = common_vendor.ref("");
    const statusFilters = common_vendor.ref([
      { value: 0, label: "全部" },
      { value: 5, label: "已支付" },
      { value: 3, label: "已失败" },
      { value: 4, label: "已退款" }
    ]);
    const userTypeFilters = common_vendor.ref([
      { value: "普通用户", label: "普通套餐" }
    ]);
    const placeholderStyle = {
      color: "#616161"
    };
    const emailInputStyle = {
      paddingLeft: "20rpx",
      paddingTop: "26rpx",
      paddingBottom: "26rpx",
      borderBottom: "1rpx solid rgba(189,189,189,0.2)"
    };
    const showUserTypeFilter = common_vendor.computed(() => {
      return specialUser.value && specialUser.value.userType && (specialUser.value.userType === "VIP客户" || specialUser.value.userType === "集团客户");
    });
    common_vendor.onLoad((options) => {
      vipType.value = options.vipType || 0;
    });
    common_vendor.onShow(() => {
      initUserData();
    });
    const initUserData = async () => {
      try {
        const res = await api_specialUser.getSpecialUser();
        specialUser.value = res.data || {};
        common_vendor.index.__f__("log", "at pages/package/packageRecord.vue:223", "specialUser:", specialUser.value);
        if (specialUser.value && specialUser.value.userType) {
          const userTypeLabel = specialUser.value.userType === "VIP客户" ? "VIP套餐" : "集团套餐";
          userTypeFilters.value = [
            { value: "普通用户", label: "普通套餐" },
            { value: specialUser.value.userType, label: userTypeLabel }
          ];
          activeUserTypeFilter.value = specialUser.value.userType;
        } else {
          activeUserTypeFilter.value = "普通用户";
        }
        getRecordList();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageRecord.vue:243", "获取用户信息失败:", error);
        activeUserTypeFilter.value = "普通用户";
        getRecordList();
      }
    };
    const getRecordList = async () => {
      try {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const params = {};
        if (activeUserTypeFilter.value) {
          params.vipType = getUserTypeNumber(activeUserTypeFilter.value);
        } else {
          params.vipType = vipType.value;
        }
        if (activeStatusFilter.value !== 0) {
          params.payStatus = activeStatusFilter.value;
        }
        common_vendor.index.__f__("log", "at pages/package/packageRecord.vue:273", "查询参数:", params);
        const res = await api_package.getUserPackageRecordList(params);
        recordList.value = res.data || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageRecord.vue:278", "获取套餐购买记录失败:", error);
        common_vendor.index.showToast({
          title: "获取记录失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const setStatusFilter = async (payStatus) => {
      if (activeStatusFilter.value === payStatus)
        return;
      activeStatusFilter.value = payStatus;
      await getRecordList();
    };
    const setUserTypeFilter = async (userType) => {
      if (activeUserTypeFilter.value === userType)
        return;
      activeUserTypeFilter.value = userType;
      await getRecordList();
    };
    const userTypeMap = {
      "VIP客户": 2,
      "集团客户": 1,
      "普通用户": 0
    };
    const getUserTypeNumber = (userType) => {
      return userTypeMap[userType] ?? 0;
    };
    const statusMap = {
      3: { text: "已失败", class: "status-failed" },
      4: { text: "已退款", class: "status-failed" },
      5: { text: "已支付", class: "status-paid" }
    };
    const getStatusText = (status) => {
      var _a;
      return ((_a = statusMap[status]) == null ? void 0 : _a.text) || "未知状态";
    };
    const getStatusClass = (status) => {
      var _a;
      return ((_a = statusMap[status]) == null ? void 0 : _a.class) || "status-unknown";
    };
    const routeToInvoice = (item) => {
      if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {
        if (item.miniInvoiceRecord.isResume) {
          common_vendor.index.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`
          });
        } else {
          common_vendor.index.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}`
          });
        }
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2`
        });
      }
    };
    const chooseEmail = (id) => {
      invoiceId.value = id;
      showPopup.value = true;
    };
    const handlePostInvoiceSend = async () => {
      if (!notifyEmail.value) {
        common_vendor.index.showToast({
          title: "请输入邮箱",
          icon: "none"
        });
        return;
      }
      try {
        const params = {
          id: invoiceId.value,
          notifyEmail: notifyEmail.value
        };
        await api_invoice.postInvoiceSend(params);
        common_vendor.index.showToast({
          title: "邮箱发送成功～",
          icon: "none",
          duration: 2e3
        });
        setTimeout(() => {
          showPopup.value = false;
          notifyEmail.value = "";
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageRecord.vue:415", "发送邮箱失败:", error);
        common_vendor.index.showToast({
          title: "发送失败，请重试",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showUserTypeFilter.value
      }, showUserTypeFilter.value ? {
        b: common_vendor.f(userTypeFilters.value, (userType, k0, i0) => {
          return {
            a: common_vendor.t(userType.label),
            b: userType.value,
            c: activeUserTypeFilter.value === userType.value ? 1 : "",
            d: common_vendor.o(($event) => setUserTypeFilter(userType.value), userType.value)
          };
        })
      } : {}, {
        c: common_vendor.f(statusFilters.value, (status, k0, i0) => {
          return {
            a: common_vendor.t(status.label),
            b: status.value,
            c: activeStatusFilter.value === status.value ? 1 : "",
            d: common_vendor.o(($event) => setStatusFilter(status.value), status.value)
          };
        }),
        d: specialUser.value && specialUser.value.userType && (specialUser.value.userType === "VIP客户" || specialUser.value.userType === "集团客户") ? "88rpx" : "0",
        e: recordList.value.length > 0
      }, recordList.value.length > 0 ? {
        f: common_vendor.f(recordList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.tradeId || "订单号待生成"),
            b: common_vendor.t(getStatusText(item.payStatus)),
            c: common_vendor.n(getStatusClass(item.payStatus)),
            d: common_vendor.t(item.warehouseName),
            e: common_vendor.t(item.plateNo),
            f: common_vendor.t(item.packageName),
            g: common_vendor.t(item.createTime),
            h: common_vendor.t(item.beginVipTime),
            i: common_vendor.t(item.expirationTime),
            j: item.discountAmount > 0
          }, item.discountAmount > 0 ? {
            k: common_vendor.t(item.discountAmount)
          } : {}, {
            l: common_vendor.t(item.actualPayment),
            m: item.payStatus === 5
          }, item.payStatus === 5 ? common_vendor.e({
            n: item.miniInvoiceRecord && item.miniInvoiceRecord.status === "ISSUED"
          }, item.miniInvoiceRecord && item.miniInvoiceRecord.status === "ISSUED" ? {
            o: "756b3d7e-0-" + i0,
            p: common_vendor.p({
              name: "email",
              color: "#3b82f6",
              size: "14"
            }),
            q: common_vendor.o(($event) => chooseEmail(item.miniInvoiceRecord.id), item.id)
          } : {}, {
            r: !item.miniInvoiceRecord || item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "UNISSU" || item.miniInvoiceRecord.status === "CLOSED") && !item.miniInvoiceRecord.reopenSign
          }, !item.miniInvoiceRecord || item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "UNISSU" || item.miniInvoiceRecord.status === "CLOSED") && !item.miniInvoiceRecord.reopenSign ? {
            s: "756b3d7e-1-" + i0,
            t: common_vendor.p({
              name: "file-text",
              color: "#ffffff",
              size: "14"
            }),
            v: common_vendor.o(($event) => routeToInvoice(item), item.id)
          } : {}, {
            w: item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "ISSUED" && !item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "CLOSED" && item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "REVERSED")
          }, item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "ISSUED" && !item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "CLOSED" && item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "REVERSED") ? {
            x: "756b3d7e-2-" + i0,
            y: common_vendor.p({
              name: "reload",
              color: "#ffffff",
              size: "14"
            }),
            z: common_vendor.o(($event) => routeToInvoice(item), item.id)
          } : {}) : {}, {
            A: item.id
          });
        })
      } : {}, {
        g: specialUser.value && specialUser.value.userType && (specialUser.value.userType === "VIP客户" || specialUser.value.userType === "集团客户") ? "196rpx" : "108rpx",
        h: common_vendor.o(($event) => notifyEmail.value = $event),
        i: common_vendor.p({
          border: "none",
          placeholder: "请输入您的电子邮箱",
          clearable: true,
          fontSize: "28rpx",
          color: "#616161",
          placeholderStyle,
          customStyle: emailInputStyle,
          modelValue: notifyEmail.value
        }),
        j: common_vendor.o(($event) => showPopup.value = false),
        k: common_vendor.o(handlePostInvoiceSend),
        l: common_vendor.o(($event) => showPopup.value = false),
        m: common_vendor.p({
          show: showPopup.value,
          mode: "center",
          round: 10,
          safeAreaInsetBottom: false,
          closeOnClickOverlay: true
        }),
        n: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-756b3d7e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/package/packageRecord.js.map
