/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.package-record.data-v-756b3d7e {
  background: #f5f5f5;
  min-height: 100vh;
}
.user-type-filter.data-v-756b3d7e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  z-index: 10;
  display: flex;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
}
.user-type-filter .filter-tab.data-v-756b3d7e {
  flex: 1;
  text-align: center;
  padding: 14rpx 12rpx;
  font-size: 26rpx;
  color: #666666;
  border-radius: 12rpx;
  margin: 0 6rpx;
  transition: all 0.3s ease;
}
.user-type-filter .filter-tab.active.data-v-756b3d7e {
  background: #3b82f6;
  color: #ffffff;
  font-weight: 500;
}
.user-type-filter .filter-tab.data-v-756b3d7e:not(.active):active {
  background: #f8fafc;
}
.user-type-filter .filter-tab.data-v-756b3d7e:first-child {
  margin-left: 0;
}
.user-type-filter .filter-tab.data-v-756b3d7e:last-child {
  margin-right: 0;
}
.status-filter.data-v-756b3d7e {
  position: fixed;
  left: 0;
  width: 100%;
  background: #ffffff;
  z-index: 9;
  display: flex;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
}
.status-filter .filter-tab.data-v-756b3d7e {
  flex: 1;
  text-align: center;
  padding: 14rpx 12rpx;
  font-size: 26rpx;
  color: #666666;
  border-radius: 12rpx;
  margin: 0 6rpx;
  transition: all 0.3s ease;
}
.status-filter .filter-tab.active.data-v-756b3d7e {
  background: #3b82f6;
  color: #ffffff;
  font-weight: 500;
}
.status-filter .filter-tab.data-v-756b3d7e:not(.active):active {
  background: #f8fafc;
}
.status-filter .filter-tab.data-v-756b3d7e:first-child {
  margin-left: 0;
}
.status-filter .filter-tab.data-v-756b3d7e:last-child {
  margin-right: 0;
}
.content.data-v-756b3d7e {
  padding: 20rpx 32rpx 40rpx;
}
.record-list .record-item.data-v-756b3d7e {
  margin-bottom: 20rpx;
}
.record-list .record-item .record-card.data-v-756b3d7e {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);
}
.record-list .record-item .record-card .card-header.data-v-756b3d7e {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.record-list .record-item .record-card .card-header .order-info .order-no.data-v-756b3d7e {
  font-size: 20rpx;
  font-weight: bold;
  color: #797979;
}
.record-list .record-item .record-card .card-header .header-right.data-v-756b3d7e {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.record-list .record-item .record-card .card-header .header-right .status-row.data-v-756b3d7e {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.record-list .record-item .record-card .card-header .header-right .status-badge.data-v-756b3d7e {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.record-list .record-item .record-card .card-header .header-right .status-badge.status-failed.data-v-756b3d7e {
  background: #fef2f2;
  color: #dc2626;
  border: 1rpx solid #fecaca;
}
.record-list .record-item .record-card .card-header .header-right .status-badge.status-paid.data-v-756b3d7e {
  background: #f0f9ff;
  color: #0ea5e9;
  border: 1rpx solid #bae6fd;
}
.record-list .record-item .record-card .card-header .header-right .status-badge.status-unknown.data-v-756b3d7e {
  background: #f9fafb;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.record-list .record-item .record-card .card-body .info-item.data-v-756b3d7e {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.record-list .record-item .record-card .card-body .info-item .info-label.data-v-756b3d7e {
  font-size: 26rpx;
  color: #666666;
  margin-right: 16rpx;
  min-width: 120rpx;
}
.record-list .record-item .record-card .card-body .info-item .info-label.plate-label.data-v-756b3d7e {
  margin-left: 40rpx;
  min-width: 80rpx;
}
.record-list .record-item .record-card .card-body .info-item .info-value.data-v-756b3d7e {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}
.record-list .record-item .record-card .card-body .info-item .info-value.time.data-v-756b3d7e {
  color: #333333;
}
.record-list .record-item .record-card .card-body .card-bottom.data-v-756b3d7e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info.data-v-756b3d7e {
  display: flex;
  align-items: center;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info .price-item.data-v-756b3d7e {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info .price-item .price-label.data-v-756b3d7e {
  font-size: 22rpx;
  color: #666666;
  margin-right: 8rpx;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info .price-item .price-value.data-v-756b3d7e {
  font-size: 26rpx;
  font-weight: bold;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info .price-item .price-value.discount.data-v-756b3d7e {
  color: #16a34a;
}
.record-list .record-item .record-card .card-body .card-bottom .price-info .price-item .price-value.actual.data-v-756b3d7e {
  color: #ff0000;
  font-size: 30rpx;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions.data-v-756b3d7e {
  display: flex;
  align-items: center;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions .invoice-btn.data-v-756b3d7e {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 10rpx;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions .invoice-btn text.data-v-756b3d7e {
  margin-left: 6rpx;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions .invoice-btn.send-btn.data-v-756b3d7e {
  background: #f0f9ff;
  color: #3b82f6;
  border: 1rpx solid #bae6fd;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions .invoice-btn.open-btn.data-v-756b3d7e {
  background: #3b82f6;
  color: #ffffff;
}
.record-list .record-item .record-card .card-body .card-bottom .invoice-actions .invoice-btn.reopen-btn.data-v-756b3d7e {
  background: #f59e0b;
  color: #ffffff;
}
.bottom-tip.data-v-756b3d7e {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  padding: 20rpx 0;
}
.bottom-tip .divider-line.data-v-756b3d7e {
  width: 100rpx;
  height: 1rpx;
  background: #e0e0e0;
}
.bottom-tip .tip-text.data-v-756b3d7e {
  font-size: 22rpx;
  color: #666666;
  margin: 0 20rpx;
}
.empty-state.data-v-756b3d7e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-state .empty-icon.data-v-756b3d7e {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}
.empty-state .empty-text.data-v-756b3d7e {
  font-size: 26rpx;
  color: #666666;
}
.popup-cell.data-v-756b3d7e {
  width: 600rpx;
  text-align: center;
  padding: 44rpx 0;
}
.popup-cell .email.data-v-756b3d7e {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
}
.popup-cell .email .content_item.data-v-756b3d7e {
  margin-bottom: 20rpx;
}
.popup-cell .email .content_item .email-title.data-v-756b3d7e {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
  margin-bottom: 16rpx;
  text-align: left;
}
.popup-cell .email .desc.data-v-756b3d7e {
  font-size: 24rpx;
  color: #f5820e;
  line-height: 1.5;
  margin-top: 16rpx;
}
.popup-cell .choose_btn.data-v-756b3d7e {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
  margin-top: 32rpx;
}
.popup-cell .choose_btn .cancel_btn.data-v-756b3d7e, .popup-cell .choose_btn .sure_btn.data-v-756b3d7e {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 80rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}
.popup-cell .choose_btn .cancel_btn.data-v-756b3d7e {
  background: #ffffff;
  border: 2rpx solid #246bfd;
  color: #246bfd;
}
.popup-cell .choose_btn .sure_btn.data-v-756b3d7e {
  background: #246bfd;
  color: #ffffff;
}