"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_warehouse = require("../../api/warehouse.js");
const api_package = require("../../api/package.js");
const api_login = require("../../api/login.js");
const utils_utils = require("../../utils/utils.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_popup2 = common_vendor.resolveComponent("up-popup");
  (_easycom_u_icon2 + _easycom_up_icon2 + _easycom_up_popup2)();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_icon + plateInput + WarehouseSelector + _easycom_up_icon + _easycom_up_popup)();
}
const plateInput = () => "../../components/uni-plate-input/uni-plate-input.js";
const WarehouseSelector = () => "../../components/warehouse-selector/warehouse-selector.js";
const _sfc_main = {
  __name: "packageVipBuy",
  setup(__props) {
    const userPhone = common_vendor.ref("");
    const plateShow = common_vendor.ref(false);
    const warehouseList = common_vendor.ref([]);
    const showSelector = common_vendor.ref(false);
    const currentWarehouse = common_vendor.ref({ id: 0, name: "" });
    const packageOrder = common_vendor.ref({
      plateNo: "",
      warehouseId: null,
      warehouseName: "",
      packageId: null,
      packageName: "",
      packagePrice: 0,
      packageDays: null,
      beginVipTime: "",
      expirationTime: "",
      vipType: 0,
      isRenewal: false,
      index: 1
    });
    const packageJudge = common_vendor.ref({});
    const isCarInWarehouse = common_vendor.ref(false);
    const canSelectTime = common_vendor.ref(false);
    const canSelectWarehouse = common_vendor.ref(true);
    const canSelectPlate = common_vendor.ref(true);
    const canSelectPackage = common_vendor.ref(false);
    const selectedDate = common_vendor.ref("");
    const minDate = common_vendor.ref("");
    const maxDate = common_vendor.ref("");
    const showPackageSelector = common_vendor.ref(false);
    const packageOptions = common_vendor.ref([]);
    const selectedPackage = common_vendor.ref(null);
    const canSubmit = common_vendor.computed(() => {
      const basicRequirements = packageOrder.value.plateNo && packageOrder.value.warehouseId && packageOrder.value.beginVipTime;
      if (packageOrder.value.vipType === 2) {
        return basicRequirements && packageOrder.value.packagePrice !== null;
      }
      if (packageOrder.value.vipType === 1) {
        const packageRequirements = packageOrder.value.packageName && packageOrder.value.packageDays !== null && packageOrder.value.packagePrice !== null;
        if (packageOrder.value.isRenewal) {
          return basicRequirements && packageRequirements && packageOrder.value.newExpirationTime !== null;
        }
        return basicRequirements && packageRequirements;
      }
      return basicRequirements;
    });
    const noticeText = common_vendor.computed(() => {
      const userType = packageOrder.value.vipType === 1 ? "集团客户" : "VIP用户";
      const packageInfo = packageOrder.value.packageName ? `${packageOrder.value.packageName}(${packageOrder.value.packageDays || ""}个自然日)` : "套餐类型未选择";
      if (packageOrder.value.isRenewal) {
        if (packageOrder.value.vipType === 1) {
          return `当前的续费套餐是：${packageInfo}。
            续费将在原有套餐到期日期基础上延长，无法修改时间。`;
        } else {
          return `当前的续费套餐是：${packageInfo}。
            续费将在原有套餐到期日期基础上延长，无法修改时间。`;
        }
      }
      if (isCarInWarehouse.value) {
        const hasExpiredMember = packageJudge.value.endVipTime !== null;
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        if (hasExpiredMember || hasParkingPayment) {
          return `当前${userType}套餐是：${packageInfo}。
            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;
        } else {
          return `当前${userType}套餐是：${packageInfo}。
            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;
        }
      }
      return `当前${userType}开通套餐是：${packageInfo}。
    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;
    });
    common_vendor.onLoad((options) => {
      loadUserInfo();
      loadWarehouseList();
      common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:280", "接收到的参数:", options);
      if (options.packageOrder) {
        try {
          const parsedOrder = JSON.parse(decodeURIComponent(options.packageOrder));
          packageOrder.value = Object.assign({}, packageOrder.value, parsedOrder);
          packageOrder.value.phoneNumber = userPhone.value;
          if (packageOrder.value.isRenewal && packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {
            packageOrder.value.newExpirationTime = null;
          }
          common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:294", "解析的套餐订单:", packageOrder.value);
          initControlStates();
          if (packageOrder.value.isRenewal) {
            handleRenewalOrder();
          } else {
            checkPackageJudge();
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:307", "解析套餐订单失败:", error);
        }
      }
    });
    const initControlStates = () => {
      if (packageOrder.value.isRenewal) {
        canSelectWarehouse.value = false;
        canSelectPlate.value = false;
        canSelectTime.value = false;
        canSelectPackage.value = packageOrder.value.vipType === 1;
        if (canSelectPackage.value) {
          generatePackageOptions();
        }
        return;
      }
      if (packageOrder.value.plateNo && packageOrder.value.plateNo !== "--") {
        canSelectPlate.value = false;
        canSelectWarehouse.value = true;
      } else {
        canSelectPlate.value = true;
        canSelectWarehouse.value = true;
      }
      if (packageOrder.value.vipType === 1) {
        canSelectPackage.value = true;
        generatePackageOptions();
      }
    };
    const generatePackageOptions = () => {
      const index = packageOrder.value.index || 1;
      const options = [];
      if (index === 1) {
        options.push({
          id: 1,
          name: "年度套餐",
          days: 1,
          price: 0
        });
      } else if (index === 2) {
        options.push({
          id: 4,
          name: "年度套餐",
          days: 2,
          price: 0.01
        });
        options.push({
          id: 2,
          name: "半年度套餐",
          days: 1,
          price: 0.01
        });
      } else if (index === 3) {
        options.push({
          id: 4,
          name: "年度套餐",
          days: 2,
          price: 0.01
        });
        options.push({
          id: 2,
          name: "半年度套餐",
          days: 1,
          price: 0.01
        });
      }
      packageOptions.value = options;
    };
    const checkPackageJudge = () => {
      if (packageOrder.value.plateNo && packageOrder.value.warehouseId) {
        performPackageJudge();
      } else {
        handleCarNotInWarehouse();
      }
    };
    const performPackageJudge = async () => {
      try {
        const res = await api_package.checkCarInWarehouse({
          plateNo: packageOrder.value.plateNo,
          warehouseId: packageOrder.value.warehouseId
        });
        packageJudge.value = res.data;
        isCarInWarehouse.value = res.data && res.data.isCarInWarehouse;
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:446", "packageJudge结果:", res.data);
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:447", "车辆是否在场:", isCarInWarehouse.value);
        if (isCarInWarehouse.value) {
          handleCarInWarehouse();
        } else {
          handleCarNotInWarehouse();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:455", "检查车辆失败:", error);
        isCarInWarehouse.value = false;
        handleCarNotInWarehouse();
      }
    };
    const handleRenewalOrder = () => {
      if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {
        common_vendor.index.showToast({
          title: "续费订单数据异常，请重新操作",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {
        packageOrder.value.newExpirationTime = null;
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:476", "集团客户续费，等待选择套餐类型");
        return;
      }
      const originalEndDate = new Date(packageOrder.value.expirationTime);
      const newEndDate = new Date(originalEndDate);
      newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);
      const endDateStr = utils_utils.formatDate(newEndDate);
      packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;
      common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:488", "续费订单处理完成，新结束时间:", packageOrder.value.newExpirationTime);
    };
    const handleCarInWarehouse = () => {
      const hasExpiredMember = packageJudge.value.endVipTime !== null;
      if (hasExpiredMember && packageJudge.value.finalBeginTime) {
        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);
        let startDate = new Date(finalBeginDate);
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        const isFinalBeginFromParking = hasParkingPayment && packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;
        if (isFinalBeginFromParking) {
          startDate.setHours(0, 0, 0, 0);
        } else {
          startDate.setDate(finalBeginDate.getDate() + 1);
          startDate.setHours(0, 0, 0, 0);
        }
        const startDateStr = utils_utils.formatDate(startDate);
        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;
        if (packageOrder.value.packageDays) {
          const today = /* @__PURE__ */ new Date();
          const endDate = new Date(startDate);
          const isToday = utils_utils.isSameDate(startDate, today);
          if (isToday) {
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
          } else {
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
          }
          const endDateStr = utils_utils.formatDate(endDate);
          packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
        }
      } else {
        if (packageJudge.value.finalBeginTime) {
          const datePart = utils_utils.extractDatePart(packageJudge.value.finalBeginTime);
          packageOrder.value.beginVipTime = `${datePart} 00:00:00`;
          if (packageOrder.value.packageDays) {
            const startDate = new Date(packageOrder.value.beginVipTime);
            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
            const endDateStr = utils_utils.formatDate(endDate);
            packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
          }
        } else {
          common_vendor.index.showToast({
            title: "参数错误，请联系客服",
            icon: "none",
            duration: 2e3
          });
          return;
        }
      }
      canSelectTime.value = false;
    };
    const handleCarNotInWarehouse = () => {
      canSelectTime.value = true;
      const today = /* @__PURE__ */ new Date();
      const dateStr = utils_utils.formatDate(today);
      packageOrder.value.beginVipTime = `${dateStr} 00:00:00`;
      selectedDate.value = dateStr;
      updateDateRange();
      calculateEndDate();
      common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:578", "车辆不在场，允许选择开始时间");
    };
    const loadUserInfo = () => {
      try {
        const wxUser = common_vendor.index.getStorageSync("wxUser");
        if (wxUser && wxUser.phoneNumber) {
          userPhone.value = wxUser.phoneNumber;
        } else {
          userPhone.value = "未获取到手机号";
        }
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:590", "获取用户手机号:", userPhone.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:592", "获取用户信息失败:", error);
        userPhone.value = "获取失败";
      }
    };
    const loadWarehouseList = async () => {
      try {
        const res = await api_warehouse.getParkWareHouseList();
        warehouseList.value = res.data.map((item) => ({
          id: item.id,
          name: item.warehouseName,
          latitude: item.latitude,
          longitude: item.longitude
        }));
        if (packageOrder.value.warehouseId && packageOrder.value.warehouseName) {
          currentWarehouse.value = {
            id: packageOrder.value.warehouseId,
            name: packageOrder.value.warehouseName
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:616", "获取场库列表失败:", error);
        common_vendor.index.showToast({
          title: "场库数据加载失败",
          icon: "none"
        });
      }
    };
    const handleWarehouseClick = () => {
      if (!canSelectWarehouse.value)
        return;
      if (!common_vendor.index.getStorageSync("token")) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
        return;
      }
      showSelector.value = true;
    };
    const closeWarehouseSelector = () => {
      showSelector.value = false;
    };
    const selectWarehouse = (warehouse) => {
      packageOrder.value.warehouseId = warehouse.id;
      packageOrder.value.warehouseName = warehouse.name;
      currentWarehouse.value = warehouse;
      common_vendor.index.setStorageSync("currentWarehouse", currentWarehouse.value);
      closeWarehouseSelector();
      common_vendor.index.showToast({
        title: `已选择${warehouse.name}`,
        icon: "success",
        duration: 1500
      });
      checkPackageJudge();
    };
    const handlePlateClick = () => {
      if (!canSelectPlate.value)
        return;
      plateShow.value = true;
    };
    const setPlate = (plate) => {
      if (plate.length >= 7) {
        packageOrder.value.plateNo = plate;
        plateShow.value = false;
        checkPackageJudge();
      }
    };
    const typeChange = (e) => {
      packageOrder.value.plateNo = "";
    };
    const handlePackageClick = () => {
      if (!canSelectPackage.value)
        return;
      showPackageSelector.value = true;
    };
    const selectPackageOption = (option) => {
      selectedPackage.value = option;
      packageOrder.value.packageId = option.id;
      packageOrder.value.packageName = option.name;
      packageOrder.value.packagePrice = option.price;
      packageOrder.value.packageDays = option.days;
      showPackageSelector.value = false;
      if (packageOrder.value.isRenewal) {
        const originalEndDate = new Date(packageOrder.value.expirationTime);
        const newEndDate = new Date(originalEndDate);
        newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);
        const endDateStr = utils_utils.formatDate(newEndDate);
        packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:712", "续费套餐选择完成，新结束时间:", packageOrder.value.newExpirationTime);
      } else if (packageOrder.value.beginVipTime) {
        calculateEndDate();
      }
      common_vendor.index.showToast({
        title: `已选择${option.name}`,
        icon: "success",
        duration: 1500
      });
    };
    const updateDateRange = () => {
      if (!canSelectTime.value)
        return;
      const today = /* @__PURE__ */ new Date();
      minDate.value = utils_utils.formatDate(today);
      const maxDateObj = /* @__PURE__ */ new Date();
      maxDateObj.setMonth(today.getMonth() + 3);
      maxDate.value = utils_utils.formatDate(maxDateObj);
      selectedDate.value = utils_utils.formatDate(today);
    };
    const onDateChange = (e) => {
      if (!canSelectTime.value)
        return;
      const selectedDateStr = e.detail.value;
      packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`;
      selectedDate.value = selectedDateStr;
      calculateEndDate();
    };
    const calculateEndDate = () => {
      if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {
        const startDate = new Date(packageOrder.value.beginVipTime);
        const today = /* @__PURE__ */ new Date();
        const endDate = new Date(startDate);
        const isToday = utils_utils.isSameDate(startDate, today);
        if (isToday) {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
          common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:760", "车辆不在场，开始时间是今天，送用户一天，截止日期", endDate);
        } else {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }
        const endDateStr = utils_utils.formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
      }
    };
    const submitOrder = () => {
      if (!packageOrder.value.plateNo) {
        common_vendor.index.showToast({
          title: "请选择车牌号",
          icon: "none"
        });
        return;
      }
      if (!packageOrder.value.warehouseId) {
        common_vendor.index.showToast({
          title: "请选择场库",
          icon: "none"
        });
        return;
      }
      if (packageOrder.value.vipType === 1 && !packageOrder.value.packageName) {
        common_vendor.index.showToast({
          title: "请选择套餐类型",
          icon: "none"
        });
        return;
      }
      if (!packageOrder.value.beginVipTime) {
        common_vendor.index.showToast({
          title: "请选择开始时间",
          icon: "none"
        });
        return;
      }
      const currentTime = /* @__PURE__ */ new Date();
      let endTime;
      if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {
        endTime = new Date(packageOrder.value.newExpirationTime);
      } else if (packageOrder.value.expirationTime) {
        endTime = new Date(packageOrder.value.expirationTime);
      } else {
        common_vendor.index.showToast({
          title: "套餐到期时间异常，请重新选择",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (endTime <= currentTime) {
        common_vendor.index.showToast({
          title: "套餐到期时间不能早于当前时间，请重新选择",
          icon: "none",
          duration: 3e3
        });
        return;
      }
      common_vendor.index.login({
        success: async (loginRes) => {
          common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:835", "登录成功 res:", loginRes);
          try {
            const openidRes = await api_login.getOpenid({
              wxCode: loginRes.code
            });
            common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:840", "获取openid res:", openidRes);
            packageOrder.value.openid = openidRes.data;
            packageOrder.value.userPhone = userPhone.value;
            createOrderWithOpenid();
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:847", "获取openid失败:", error);
            common_vendor.index.showToast({
              title: "获取用户信息失败",
              icon: "none",
              duration: 2e3
            });
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/package/packageVipBuy.vue:856", "登录失败:", error);
          common_vendor.index.showToast({
            title: "登录失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const createOrderWithOpenid = () => {
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_package.createOrder(packageOrder.value).then((res) => {
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:874", "创建订单 res:", res);
        if (res.data.needPay) {
          common_vendor.index.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: res.data.signType,
            paySign: res.data.paySign,
            success: function(result) {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付成功~",
                  icon: "none",
                  duration: 2e3
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            fail: function(err) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:898", "支付失败的回调：", err);
              if (res.data.orderId) {
                api_package.updateOrder({
                  id: res.data.orderId,
                  payStatus: 3
                  // 已取消
                }).then((updateRes) => {
                  common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:906", "订单状态更新为已取消：", updateRes);
                }).catch((updateErr) => {
                  common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:908", "订单状态更新失败：", updateErr);
                });
              }
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付失败",
                  icon: "none",
                  duration: 1500
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            complete: function(res2) {
              common_vendor.index.hideLoading();
            }
          });
        } else if (!res.data.needPay) {
          common_vendor.index.hideLoading();
          setTimeout(() => {
            common_vendor.index.showToast({
              title: "开通成功~",
              icon: "none",
              duration: 2e3
            });
          }, 100);
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 2e3);
        }
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/package/packageVipBuy.vue:943", err);
        common_vendor.index.hideLoading();
        setTimeout(() => {
          common_vendor.index.showToast({
            title: "订单创建失败",
            icon: "none",
            duration: 1500
          });
        }, 100);
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(noticeText.value),
        b: common_assets._imports_0$7,
        c: common_vendor.t(userPhone.value || "未获取到手机号"),
        d: common_assets._imports_1$2,
        e: common_vendor.t(packageOrder.value.warehouseName || "请选择场库"),
        f: canSelectWarehouse.value
      }, canSelectWarehouse.value ? {
        g: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#4BA1FC"
        })
      } : {}, {
        h: canSelectWarehouse.value ? 1 : "",
        i: packageOrder.value.warehouseName && packageOrder.value.warehouseName !== "--" ? 1 : "",
        j: common_vendor.o(handleWarehouseClick),
        k: common_assets._imports_2$1,
        l: common_vendor.t(packageOrder.value.plateNo || "请输入车牌号"),
        m: canSelectPlate.value
      }, canSelectPlate.value ? {
        n: common_vendor.p({
          name: "edit-pen",
          size: "12",
          color: "#4BA1FC"
        })
      } : {}, {
        o: common_vendor.n(canSelectPlate.value ? "clickable-text" : ""),
        p: common_vendor.o(handlePlateClick),
        q: common_assets._imports_3,
        r: packageOrder.value.vipType === 2 && packageOrder.value.isRenewal
      }, packageOrder.value.vipType === 2 && packageOrder.value.isRenewal ? {
        s: common_vendor.t(packageOrder.value.packageName || "-")
      } : common_vendor.e({
        t: common_vendor.t(packageOrder.value.packageName || "请选择套餐类型"),
        v: canSelectPackage.value
      }, canSelectPackage.value ? {
        w: common_vendor.p({
          name: "arrow-down",
          size: "12",
          color: "#4BA1FC"
        })
      } : {}, {
        x: common_vendor.n(canSelectPackage.value ? "clickable-text" : ""),
        y: common_vendor.o(handlePackageClick)
      }), {
        z: common_assets._imports_4,
        A: canSelectTime.value
      }, canSelectTime.value ? {
        B: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间"),
        C: selectedDate.value,
        D: minDate.value,
        E: maxDate.value,
        F: common_vendor.o(onDateChange)
      } : {
        G: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间")
      }, {
        H: common_vendor.n(canSelectTime.value ? "clickable-text" : ""),
        I: packageOrder.value.isRenewal
      }, packageOrder.value.isRenewal ? {
        J: common_assets._imports_4,
        K: common_vendor.t(packageOrder.value.expirationTime)
      } : {}, {
        L: common_assets._imports_4,
        M: common_vendor.t(packageOrder.value.isRenewal ? "续费后到期时间" : "到期时间"),
        N: common_vendor.t(packageOrder.value.isRenewal ? packageOrder.value.newExpirationTime || "--" : packageOrder.value.expirationTime || "--"),
        O: common_assets._imports_5,
        P: common_vendor.t(packageOrder.value.packagePrice !== null && packageOrder.value.packagePrice !== void 0 ? packageOrder.value.packagePrice : "0.00"),
        Q: common_assets._imports_0$7,
        R: common_vendor.t(packageOrder.value.packagePrice !== null && packageOrder.value.packagePrice !== void 0 ? packageOrder.value.packagePrice : "0.00"),
        S: common_vendor.t(packageOrder.value.isRenewal ? "确认续费" : "立即购买"),
        T: common_vendor.o(submitOrder),
        U: !canSubmit.value,
        V: plateShow.value
      }, plateShow.value ? {
        W: common_vendor.o(typeChange),
        X: common_vendor.o(setPlate),
        Y: common_vendor.o(($event) => plateShow.value = false),
        Z: common_vendor.p({
          plate: packageOrder.value.plateNo || ""
        })
      } : {}, {
        aa: common_vendor.o(closeWarehouseSelector),
        ab: common_vendor.o(selectWarehouse),
        ac: common_vendor.p({
          show: showSelector.value,
          warehouseList: warehouseList.value,
          currentWarehouse: currentWarehouse.value,
          windowHeightHalf: 400
        }),
        ad: common_vendor.o(($event) => showPackageSelector.value = false),
        ae: common_vendor.p({
          name: "close",
          size: "18",
          color: "#999"
        }),
        af: common_vendor.f(packageOptions.value, (option, index, i0) => {
          return {
            a: common_vendor.t(option.name),
            b: common_vendor.t(option.days),
            c: common_vendor.t(option.price),
            d: index,
            e: selectedPackage.value && selectedPackage.value.name === option.name ? 1 : "",
            f: common_vendor.o(($event) => selectPackageOption(option), index)
          };
        }),
        ag: common_vendor.o(($event) => showPackageSelector.value = false),
        ah: common_vendor.p({
          show: showPackageSelector.value,
          mode: "bottom",
          round: "20",
          safeAreaInsetBottom: true
        }),
        ai: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7857afbc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/package/packageVipBuy.js.map
