"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_parkingOrder = require("../../api/parkingOrder.js");
const _sfc_main = {
  __name: "payPlateDetail",
  setup(__props) {
    const orderInfo = common_vendor.ref({});
    const plateNo = common_vendor.ref("");
    const warehouseId = common_vendor.ref(0);
    common_vendor.onLoad((options) => {
      plateNo.value = options.plateNo;
      warehouseId.value = options.warehouseId;
      fetchParkingOrderDetail();
    });
    const fetchParkingOrderDetail = () => {
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      if (!plateNo.value || !warehouseId.value) {
        return;
      }
      let params = {
        plateNo: plateNo.value,
        warehouseId: warehouseId.value
      };
      api_parkingOrder.getParkingOrderDetail(params).then((res) => {
        orderInfo.value = res.data;
        common_vendor.index.hideLoading();
      });
    };
    const chenkPayStatus = (status) => {
      switch (status) {
        case 1:
          return "进行中";
        case 5:
          return "已支付";
        default:
          return "--";
      }
    };
    const handleCreateParkingOrder = () => {
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_parkingOrder.createParkingOrder(orderInfo.value).then((res) => {
        common_vendor.index.__f__("log", "at pages/payPlateDetail/payPlateDetail.vue:117", "创建停车订单 res: ", res);
        if (res.data.needPay) {
          common_vendor.index.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: res.data.signType,
            paySign: res.data.paySign,
            success: function(result) {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付成功~",
                  icon: "none",
                  duration: 2e3
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            fail: function(err) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("log", "at pages/payPlateDetail/payPlateDetail.vue:147", "支付失败的回调：", err);
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付失败",
                  icon: "none",
                  duration: 1500
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            complete: function(res2) {
              common_vendor.index.hideLoading();
            }
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$5,
        b: common_assets._imports_0$6,
        c: common_vendor.t(plateNo.value),
        d: common_assets._imports_1$2,
        e: common_vendor.t(orderInfo.value.warehouseName || "--"),
        f: common_assets._imports_4,
        g: common_vendor.t(orderInfo.value.beginParkingTime || "--"),
        h: common_assets._imports_4,
        i: common_vendor.t(orderInfo.value.endParkingTime || "--"),
        j: common_assets._imports_4,
        k: common_vendor.t(orderInfo.value.parkingDuration || "--"),
        l: common_assets._imports_0$7,
        m: common_vendor.t(chenkPayStatus(orderInfo.value.payStatus)),
        n: common_assets._imports_5,
        o: common_vendor.t(orderInfo.value.paymentAmount || "--"),
        p: common_vendor.t(orderInfo.value.actualPayment || "--"),
        q: common_vendor.o(handleCreateParkingOrder),
        r: orderInfo.value.payStatus === 5 || orderInfo.value.paymentAmount === 0 || !orderInfo.value.paymentAmount,
        s: common_vendor.gei(_ctx, "")
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a7de0af5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/payPlateDetail/payPlateDetail.js.map
