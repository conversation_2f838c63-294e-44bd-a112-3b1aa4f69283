"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_warehouse = require("../../api/warehouse.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_grid_item2 = common_vendor.resolveComponent("u-grid-item");
  const _easycom_u_grid2 = common_vendor.resolveComponent("u-grid");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  (_easycom_u_icon2 + _easycom_u_grid_item2 + _easycom_u_grid2 + _easycom_u_empty2)();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_grid_item = () => "../../node-modules/uview-plus/components/u-grid-item/u-grid-item.js";
const _easycom_u_grid = () => "../../node-modules/uview-plus/components/u-grid/u-grid.js";
const _easycom_u_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_grid_item + _easycom_u_grid + _easycom_u_empty + plateInput + WarehouseSelector)();
}
const plateInput = () => "../../components/uni-plate-input/uni-plate-input.js";
const WarehouseSelector = () => "../../components/warehouse-selector/warehouse-selector.js";
const _sfc_main = {
  __name: "payPlateQuery",
  setup(__props) {
    const plateNo = common_vendor.ref("");
    const plateShow = common_vendor.ref(false);
    const PageCur = common_vendor.ref(1);
    const isEdit = common_vendor.ref(false);
    const plateNoHistoryList = common_vendor.ref([]);
    const wareHouseList = common_vendor.ref([]);
    const showSelector = common_vendor.ref(false);
    const currentWarehouse = common_vendor.ref({ id: 0, name: "选择场库" });
    common_vendor.watch(plateNo, (newVal) => {
      if (newVal.length === 7) {
        PageCur.value = 1;
      }
      if (newVal.length === 8) {
        PageCur.value = 2;
      }
    });
    common_vendor.onShow(() => {
      plateNoHistoryList.value = common_vendor.index.getStorageSync("plateNoHistoryList") || [];
      initWarehouseData();
    });
    const setPlate = (plate) => {
      if (plate.length >= 7)
        plateNo.value = plate;
      plateShow.value = false;
    };
    const typeChange = (e) => {
      PageCur.value = e;
      plateNo.value = "";
    };
    const close = () => {
      if (!isEdit.value) {
        PageCur.value = 1;
      }
    };
    const handleChoosePlateNo = (plateNoValue) => {
      plateNo.value = plateNoValue;
    };
    const initWarehouseData = async () => {
      try {
        const res = await api_warehouse.getParkWareHouseList();
        wareHouseList.value = res.data.map((item) => ({
          id: item.id,
          name: item.warehouseName,
          latitude: item.latitude,
          longitude: item.longitude
        }));
        const cachedWarehouse = common_vendor.index.getStorageSync("currentWarehouse");
        if (cachedWarehouse && wareHouseList.value.some((w) => w.id === cachedWarehouse.id)) {
          currentWarehouse.value = cachedWarehouse;
        } else if (wareHouseList.value.length > 0) {
          currentWarehouse.value = wareHouseList.value[0];
          common_vendor.index.setStorageSync("currentWarehouse", currentWarehouse.value);
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "场库数据加载失败",
          icon: "none"
        });
      }
    };
    const showWarehouseSelector = () => {
      showSelector.value = true;
    };
    const closeWarehouseSelector = () => {
      showSelector.value = false;
    };
    const selectWarehouse = (warehouse) => {
      currentWarehouse.value = warehouse;
      common_vendor.index.setStorageSync("currentWarehouse", currentWarehouse.value);
      closeWarehouseSelector();
    };
    const handleRoutePage = () => {
      if (!plateNo.value) {
        return common_vendor.index.showToast({
          title: "请选择车牌~",
          duration: 2e3,
          icon: "none"
        });
      }
      if (!currentWarehouse.value || !currentWarehouse.value.id || currentWarehouse.value.id === 0) {
        return common_vendor.index.showToast({
          title: "请选择场库~",
          duration: 2e3,
          icon: "none"
        });
      }
      let flag = true;
      plateNoHistoryList.value.forEach((item) => {
        if (plateNo.value === item) {
          flag = false;
        }
      });
      if (flag) {
        if (plateNoHistoryList.value.length === 6) {
          plateNoHistoryList.value.pop();
        }
        plateNoHistoryList.value.unshift(plateNo.value);
        common_vendor.index.setStorageSync("plateNoHistoryList", plateNoHistoryList.value);
      }
      common_vendor.index.navigateTo({
        url: "/pages/payPlateDetail/payPlateDetail?plateNo=" + plateNo.value + "&warehouseId=" + currentWarehouse.value.id
      });
    };
    const deleteHistory = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认要清空历史记录吗?",
        success(res) {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("plateNoHistoryList");
            setTimeout(() => {
              plateNoHistoryList.value = common_vendor.index.getStorageSync("plateNoHistoryList") || [];
            }, 300);
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.t(currentWarehouse.value.name || "请选择场库"),
        c: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#999"
        }),
        d: common_vendor.o(showWarehouseSelector),
        e: common_vendor.t(plateNo.value.substr(0, 1)),
        f: common_vendor.t(plateNo.value.substr(1, 1)),
        g: common_vendor.t(plateNo.value.substr(2, 1)),
        h: common_vendor.t(plateNo.value.substr(3, 1)),
        i: common_vendor.t(plateNo.value.substr(4, 1)),
        j: common_vendor.t(plateNo.value.substr(5, 1)),
        k: common_vendor.t(plateNo.value.substr(6, 1)),
        l: PageCur.value == "2"
      }, PageCur.value == "2" ? {
        m: common_vendor.t(plateNo.value.substr(7, 1))
      } : {}, {
        n: common_vendor.o(($event) => plateShow.value = true),
        o: common_vendor.o(deleteHistory),
        p: common_vendor.p({
          name: "trash",
          size: "24",
          color: "#999"
        }),
        q: plateNoHistoryList.value.length > 0
      }, plateNoHistoryList.value.length > 0 ? {
        r: common_vendor.f(plateNoHistoryList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item),
            b: item,
            c: common_vendor.o(($event) => handleChoosePlateNo(item), item),
            d: "485d7737-3-" + i0 + ",485d7737-2"
          };
        }),
        s: common_vendor.p({
          border: false,
          col: "3"
        })
      } : {
        t: common_vendor.p({
          text: "暂无历史记录",
          mode: "history",
          iconSize: "50"
        })
      }, {
        v: common_vendor.o(handleRoutePage),
        w: plateShow.value
      }, plateShow.value ? {
        x: common_vendor.o(typeChange),
        y: common_vendor.o(setPlate),
        z: common_vendor.o(($event) => plateShow.value = false & close()),
        A: common_vendor.p({
          plate: plateNo.value
        })
      } : {}, {
        B: common_vendor.o(closeWarehouseSelector),
        C: common_vendor.o(selectWarehouse),
        D: common_vendor.p({
          show: showSelector.value,
          warehouseList: wareHouseList.value,
          currentWarehouse: currentWarehouse.value,
          windowHeightHalf: 400
        }),
        E: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-485d7737"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/payPlateQuery/payPlateQuery.js.map
