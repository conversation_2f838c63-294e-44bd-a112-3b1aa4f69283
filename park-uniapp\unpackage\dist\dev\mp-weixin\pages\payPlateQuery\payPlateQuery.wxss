/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.plate-query-container.data-v-485d7737 {
  background-color: #f5f5f5;
  height: 100vh;
}
.warehouse-selector-section.data-v-485d7737 {
  margin-bottom: 20rpx;
}
.warehouse-selector.data-v-485d7737 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
}
.warehouse-name.data-v-485d7737 {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}
.cell.data-v-485d7737 {
  padding: 40rpx 32rpx 0;
}
.cell .top-cell.data-v-485d7737 {
  margin-bottom: 20rpx;
}
.cell .top-cell .top-cell-title.data-v-485d7737 {
  margin-right: 8rpx;
}
.cell .top-cell .top-cell-title .title.data-v-485d7737 {
  font-size: 40rpx;
  font-weight: bold;
  color: #212121;
  margin-bottom: 8rpx;
}
.cell .top-cell .top-cell-title .desc.data-v-485d7737 {
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
}
.cell .top-cell image.data-v-485d7737 {
  width: 284rpx;
  height: 200rpx;
}
.cell .form-cell.data-v-485d7737 {
  padding: 32rpx;
  border-radius: 20rpx;
  background-color: #fff;
}
.cell .form-cell .form-cell-title.data-v-485d7737 {
  font-size: 32rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 20rpx;
}
.cell .form-cell .form-cell-title image.data-v-485d7737 {
  width: 36rpx;
  height: 36rpx;
}
.cell .form-cell .center_cell_top_block.data-v-485d7737 {
  background: #f3f5f7;
  border-radius: 200rpx;
  padding: 16rpx 0 18rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
  text-align: center;
  word-break: keep-all;
  margin-bottom: 20rpx;
  width: 95%;
}
.so-plate-body.data-v-485d7737 {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.so-plate-word.data-v-485d7737 {
  border: 1rpx solid #246bfd;
  border-radius: 10rpx;
  height: 88rpx;
  margin: 0 5rpx;
  box-sizing: border-box;
  width: 71rpx;
  position: relative;
}
.so-plate-word text.data-v-485d7737 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  font-weight: 700;
  font-size: 32rpx;
}
.so-plate-dot.data-v-485d7737 {
  width: 15rpx;
  height: 15rpx;
  background: #246bfd;
  border-radius: 50%;
  margin: 0 5rpx;
}
.search-btn.data-v-485d7737 {
  margin-top: 40rpx;
  width: 85%;
  background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
}