"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_warehouse = require("../../api/warehouse.js");
if (!Array) {
  const _easycom_up_loading_page2 = common_vendor.resolveComponent("up-loading-page");
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_loading_page2 + _easycom_up_icon2 + _easycom_up_empty2)();
}
const _easycom_up_loading_page = () => "../../node-modules/uview-plus/components/u-loading-page/u-loading-page.js";
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_loading_page + _easycom_up_icon + _easycom_up_empty + CustomTabBar)();
}
const CustomTabBar = () => "../../components/custom-tab-bar/index.js";
const _sfc_main = {
  __name: "warehouse",
  setup(__props) {
    const currentTab = common_vendor.ref("parking");
    const parkWareHouseList = common_vendor.ref([]);
    const userLocation = common_vendor.ref(null);
    const isLoading = common_vendor.ref(true);
    common_vendor.onShow(() => {
      if (parkWareHouseList.value.length === 0) {
        isLoading.value = true;
      }
      getUserLocation();
    });
    const getUserLocation = () => {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          const newLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          const locationChanged = !userLocation.value || userLocation.value.latitude !== newLocation.latitude || userLocation.value.longitude !== newLocation.longitude;
          userLocation.value = newLocation;
          if (parkWareHouseList.value.length === 0 || locationChanged) {
            initParkWarehouse();
          } else {
            recalculateDistances();
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/warehouse/warehouse.vue:117", "获取位置失败:", err);
          userLocation.value = null;
          if (parkWareHouseList.value.length === 0) {
            initParkWarehouse();
          }
        }
      });
    };
    const initParkWarehouse = () => {
      api_warehouse.getParkWareHouseList().then((res) => {
        const list = res.data;
        if (userLocation.value) {
          list.forEach((item) => {
            item.distance = calculateDistance(
              userLocation.value.latitude,
              userLocation.value.longitude,
              item.latitude,
              item.longitude
            );
          });
          list.sort((a, b) => {
            if (a.distance === void 0 && b.distance === void 0)
              return 0;
            if (a.distance === void 0)
              return 1;
            if (b.distance === void 0)
              return -1;
            return a.distance - b.distance;
          });
        }
        parkWareHouseList.value = list;
        isLoading.value = false;
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/warehouse/warehouse.vue:154", "加载场库列表失败:", err);
        isLoading.value = false;
      });
    };
    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const radLat1 = lat1 * Math.PI / 180;
      const radLat2 = lat2 * Math.PI / 180;
      const a = radLat1 - radLat2;
      const b = lng1 * Math.PI / 180 - lng2 * Math.PI / 180;
      let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
      s = s * 6378.137;
      s = Math.round(s * 100) / 100;
      return s;
    };
    const recalculateDistances = () => {
      if (!userLocation.value || parkWareHouseList.value.length === 0) {
        return;
      }
      parkWareHouseList.value.forEach((item) => {
        item.distance = calculateDistance(
          userLocation.value.latitude,
          userLocation.value.longitude,
          item.latitude,
          item.longitude
        );
      });
      parkWareHouseList.value.sort((a, b) => {
        if (a.distance === void 0 && b.distance === void 0)
          return 0;
        if (a.distance === void 0)
          return 1;
        if (b.distance === void 0)
          return -1;
        return a.distance - b.distance;
      });
    };
    const getFirstImage = (carouselImages) => {
      if (!carouselImages)
        return "/static/image/parkPay.png";
      try {
        if (typeof carouselImages === "string") {
          const parsedArray = JSON.parse(carouselImages);
          if (Array.isArray(parsedArray) && parsedArray.length > 0) {
            if (typeof parsedArray[0] === "string") {
              return parsedArray[0];
            }
            if (Array.isArray(parsedArray[0]) && parsedArray[0].length > 0) {
              return parsedArray[0][0];
            }
          }
        }
        if (Array.isArray(carouselImages) && carouselImages.length > 0) {
          return carouselImages[0];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/warehouse/warehouse.vue:221", "解析图片数据失败:", error);
        if (typeof carouselImages === "string" && carouselImages.startsWith("http")) {
          return carouselImages;
        }
      }
      return "/static/image/parkPay.png";
    };
    const switchTab = (tab) => {
      currentTab.value = tab;
      if (tab === "parking") {
        isLoading.value = true;
        initParkWarehouse();
      }
    };
    const goToMap = (item) => {
      common_vendor.index.openLocation({
        latitude: item.latitude,
        longitude: item.longitude,
        name: item.warehouseName,
        address: item.address,
        success: () => {
          common_vendor.index.__f__("log", "at pages/warehouse/warehouse.vue:247", "打开地图成功");
        }
      });
    };
    const makePhoneCall = (item) => {
      if (!item.managerPhone) {
        common_vendor.index.showToast({
          title: "暂无联系电话",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "拨打电话",
        content: `是否拨打 ${item.managerPhone} 的咨询电话？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: item.managerPhone,
              success: () => {
                common_vendor.index.__f__("log", "at pages/warehouse/warehouse.vue:270", "拨打电话成功");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/warehouse/warehouse.vue:273", "拨打电话失败:", err);
                common_vendor.index.showToast({
                  title: "拨打电话失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    const loadMore = () => {
      common_vendor.index.__f__("log", "at pages/warehouse/warehouse.vue:287", "加载更多数据");
    };
    const goToDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/pages/warehouse/warehouseDetail?id=${id}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: currentTab.value === "parking" ? 1 : "",
        b: common_vendor.o(($event) => switchTab("parking")),
        c: currentTab.value === "charging" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("charging")),
        e: isLoading.value
      }, isLoading.value ? {
        f: common_vendor.p({
          loading: "true",
          ["loading-text"]: "加载中..."
        })
      } : currentTab.value === "parking" && parkWareHouseList.value.length > 0 ? {
        h: common_vendor.f(parkWareHouseList.value, (item, index, i0) => {
          return common_vendor.e({
            a: getFirstImage(item.carouselImages),
            b: common_vendor.t(item.warehouseName),
            c: common_vendor.t(item.address || "暂无地址信息"),
            d: common_vendor.t(item.distance ? `${item.distance}km` : ""),
            e: common_vendor.n(!userLocation.value || item.distance === void 0 ? "distance-hidden" : ""),
            f: common_vendor.o(($event) => goToMap(item), index),
            g: item.managerPhone
          }, item.managerPhone ? {
            h: "41554ef3-1-" + i0,
            i: common_vendor.p({
              name: "phone",
              size: "20",
              color: "#ffffff"
            }),
            j: common_vendor.o(($event) => makePhoneCall(item), index)
          } : {}, {
            k: index,
            l: common_vendor.o(($event) => goToDetail(item.id), index)
          });
        }),
        i: common_assets._imports_0$1
      } : currentTab.value === "parking" && parkWareHouseList.value.length === 0 ? {
        k: common_vendor.p({
          text: "暂无停车场数据",
          mode: "data"
        })
      } : {
        l: common_vendor.p({
          text: "暂无充电场数据",
          mode: "data"
        })
      }, {
        g: currentTab.value === "parking" && parkWareHouseList.value.length > 0,
        j: currentTab.value === "parking" && parkWareHouseList.value.length === 0,
        m: common_vendor.o(loadMore),
        n: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-41554ef3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/warehouse/warehouse.js.map
