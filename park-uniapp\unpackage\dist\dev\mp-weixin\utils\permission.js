"use strict";
const common_vendor = require("../common/vendor.js");
const whiteList = [
  "/pages/home/<USER>",
  "/pages/warehouse/warehouse",
  "/pages/warehouse/warehouseDetail",
  "/pages/login/login",
  "/pages/mine/mine",
  "/pages/noPlate/noPlateIn",
  "/pages/noPlate/noPlateOut",
  "/pages/aggrement/user-aggrement",
  "/pages/aggrement/privacy-aggrement",
  "/pages/carStop/carStop"
];
function initPermission() {
  const list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
  list.forEach((item) => {
    common_vendor.index.addInterceptor(item, {
      invoke(e) {
        common_vendor.index.getStorageSync("wxUser");
        const token = common_vendor.index.getStorageSync("token");
        const url = e.url.split("?")[0];
        let notNeed = whiteList.includes(url);
        if (notNeed) {
          return e;
        } else {
          if (!token) {
            common_vendor.index.showModal({
              title: "温馨提示",
              content: "您还未登录，请先登录",
              success: function(res) {
                if (res.confirm) {
                  common_vendor.index.reLaunch({
                    url: "/pages/login/login"
                  });
                }
              }
            });
            return false;
          } else {
            return e;
          }
        }
      },
      fail(err) {
        common_vendor.index.__f__("log", "at utils/permission.js:60", err);
      }
    });
  });
}
exports.initPermission = initPermission;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/permission.js.map
