"use strict";
const common_vendor = require("../common/vendor.js");
const utils_utils = require("./utils.js");
const utils_requestManager = require("./requestManager.js");
const config_index = require("../config/index.js");
const accountInfo = common_vendor.wx$1.getAccountInfoSync();
accountInfo.miniProgram.envVersion;
const manager = new utils_requestManager.RequestManager();
const setHeaders = (type) => {
  const header = {
    Authorization: "WxBearer " + utils_utils.getStorageSync("token") || ""
  };
  if (type === "form") {
    header["content-type"] = "application/x-www-form-urlencoded";
  }
  return header;
};
const handleUnauthorized = () => {
  common_vendor.index.removeStorageSync("token");
  common_vendor.index.removeStorageSync("wxUser");
};
const showErrorToast = (message) => {
  common_vendor.index.showToast({
    title: message || "网络连接失败，请稍后重试",
    icon: "none",
    duration: 2e3
  });
};
const baseRequest = async (url, method, data = {}, type = "json", loading = true) => {
  const requestId = manager.generateId(method, url, data);
  if (!requestId) {
    showErrorToast("请勿重复请求");
    return Promise.reject("重复请求");
  }
  try {
    const result = await new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: config_index.URL + url,
        method: method || "get",
        header: setHeaders(type),
        timeout: method === "get" ? 1e4 : 3e4,
        data,
        success: (res) => {
          const response = res.data;
          if (response.code === 200) {
            resolve(response);
          } else if (response.code === 401) {
            handleUnauthorized();
            reject(response);
          } else {
            showErrorToast(response.msg);
            reject(response);
          }
        },
        fail: (err) => {
          showErrorToast(err.errMsg);
          reject(err);
        },
        complete: () => {
          manager.deleteById(requestId);
        }
      });
    });
    return result;
  } catch (error) {
    throw error;
  }
};
const request = {};
["options", "get", "post", "put", "head", "delete", "trace", "connect"].forEach((method) => {
  request[method] = (api, data, type, loading) => baseRequest(api, method, data, type, loading);
});
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
